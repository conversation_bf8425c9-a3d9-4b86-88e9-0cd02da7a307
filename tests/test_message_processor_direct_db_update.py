#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 MessageProcessor 直接调用数据库方法更新会话状态
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock

from src.domain.services.message_processor import MessageProcessor


class TestMessageProcessorDirectDBUpdate:
    """测试 MessageProcessor 直接数据库更新功能"""

    @pytest.fixture
    def message_processor(self):
        """创建 MessageProcessor 实例"""
        processor = MessageProcessor()
        
        # 模拟 SSE 管理器
        mock_sse_manager = Mock()
        mock_sse_manager.sse_connections = {"test_session": Mock()}
        mock_sse_manager.push_to_sse = AsyncMock()
        mock_sse_manager.close_session_connection = Mock()
        processor.set_sse_manager(mock_sse_manager)
        
        # 模拟 Session 管理器
        mock_session_manager = Mock()
        processor.set_session_manager(mock_session_manager)
        
        return processor

    @pytest.fixture
    def mock_finish_event(self):
        """创建模拟的完成事件"""
        from memory.events import EventType
        
        mock_event = Mock()
        mock_event.session_id = "test_session"
        mock_event.run_id = "test_run"
        mock_event.type = EventType.RUN_FINISHED
        mock_event.type.value = "run_finished"
        
        return mock_event

    @pytest.fixture
    def mock_session(self):
        """创建模拟的 Session 对象"""
        mock_session = Mock()
        mock_session.session_id = "test_session"
        mock_session.finish_processing = Mock(return_value=True)
        return mock_session

    @pytest.mark.asyncio
    async def test_direct_database_update_on_finish_event(self, message_processor, mock_finish_event, mock_session):
        """测试收到完成事件时直接更新数据库状态"""
        
        # 模拟数据库服务
        with patch('src.domain.kms.message_processor.session_db_service') as mock_db_service:
            mock_db_service.update_session_status.return_value = True
            
            # 模拟 _load_session_from_db 方法
            with patch.object(message_processor, '_load_session_from_db', return_value=mock_session):
                # 模拟 _is_session_finished_event 方法
                with patch.object(message_processor, '_is_session_finished_event', return_value=True):
                    # 模拟 _format_done_event_data 方法
                    with patch.object(message_processor, '_format_done_event_data', return_value={"event": "done"}):
                        # 模拟 _handle_agentbay_artifact 方法
                        with patch.object(message_processor, '_handle_agentbay_artifact', new_callable=AsyncMock):
                            
                            # 调用处理方法
                            await message_processor._handle_new_message_internal(
                                "test_session", "test_run", mock_finish_event
                            )
                            
                            # 验证直接调用了数据库更新方法
                            mock_db_service.update_session_status.assert_called_once_with("test_session", 'CLOSED')
                            
                            # 验证没有调用 session.finish_processing()
                            mock_session.finish_processing.assert_not_called()
                            
                            # 验证发送了完成事件到SSE
                            message_processor.sse_manager.push_to_sse.assert_called_once()
                            
                            # 验证关闭了SSE连接
                            message_processor.sse_manager.close_session_connection.assert_called_once_with("test_session")

    @pytest.mark.asyncio
    async def test_database_update_failure_handling(self, message_processor, mock_finish_event, mock_session):
        """测试数据库更新失败时的错误处理"""
        
        # 模拟数据库服务更新失败
        with patch('src.domain.kms.message_processor.session_db_service') as mock_db_service:
            mock_db_service.update_session_status.return_value = False  # 更新失败
            
            # 模拟 _load_session_from_db 方法
            with patch.object(message_processor, '_load_session_from_db', return_value=mock_session):
                # 模拟 _is_session_finished_event 方法
                with patch.object(message_processor, '_is_session_finished_event', return_value=True):
                    # 模拟 _format_done_event_data 方法
                    with patch.object(message_processor, '_format_done_event_data', return_value={"event": "done"}):
                        # 模拟 _handle_agentbay_artifact 方法
                        with patch.object(message_processor, '_handle_agentbay_artifact', new_callable=AsyncMock):
                            
                            # 调用处理方法（不应该抛出异常）
                            await message_processor._handle_new_message_internal(
                                "test_session", "test_run", mock_finish_event
                            )
                            
                            # 验证调用了数据库更新方法
                            mock_db_service.update_session_status.assert_called_once_with("test_session", 'CLOSED')
                            
                            # 验证没有调用 session.finish_processing()
                            mock_session.finish_processing.assert_not_called()

    @pytest.mark.asyncio
    async def test_database_update_exception_handling(self, message_processor, mock_finish_event, mock_session):
        """测试数据库更新抛出异常时的错误处理"""
        
        # 模拟数据库服务抛出异常
        with patch('src.domain.kms.message_processor.session_db_service') as mock_db_service:
            mock_db_service.update_session_status.side_effect = Exception("数据库连接失败")
            
            # 模拟 _load_session_from_db 方法
            with patch.object(message_processor, '_load_session_from_db', return_value=mock_session):
                # 模拟 _is_session_finished_event 方法
                with patch.object(message_processor, '_is_session_finished_event', return_value=True):
                    # 模拟 _format_done_event_data 方法
                    with patch.object(message_processor, '_format_done_event_data', return_value={"event": "done"}):
                        # 模拟 _handle_agentbay_artifact 方法
                        with patch.object(message_processor, '_handle_agentbay_artifact', new_callable=AsyncMock):
                            
                            # 调用处理方法（不应该抛出异常）
                            await message_processor._handle_new_message_internal(
                                "test_session", "test_run", mock_finish_event
                            )
                            
                            # 验证调用了数据库更新方法
                            mock_db_service.update_session_status.assert_called_once_with("test_session", 'CLOSED')
                            
                            # 验证没有调用 session.finish_processing()
                            mock_session.finish_processing.assert_not_called()

    @pytest.mark.asyncio
    async def test_non_finish_event_no_database_update(self, message_processor, mock_session):
        """测试非完成事件时不会更新数据库状态"""
        
        # 创建非完成事件
        mock_event = Mock()
        mock_event.session_id = "test_session"
        mock_event.run_id = "test_run"
        mock_event.type = Mock()
        mock_event.type.value = "message"
        
        # 模拟数据库服务
        with patch('src.domain.kms.message_processor.session_db_service') as mock_db_service:
            mock_db_service.update_session_status.return_value = True
            
            # 模拟 _load_session_from_db 方法
            with patch.object(message_processor, '_load_session_from_db', return_value=mock_session):
                # 模拟 _is_session_finished_event 方法返回 False
                with patch.object(message_processor, '_is_session_finished_event', return_value=False):
                    # 模拟 _serialize_event_for_sse 方法
                    with patch.object(message_processor, '_serialize_event_for_sse', return_value={"event": "message"}):
                        
                        # 调用处理方法
                        await message_processor._handle_new_message_internal(
                            "test_session", "test_run", mock_event
                        )
                        
                        # 验证没有调用数据库更新方法
                        mock_db_service.update_session_status.assert_not_called()
                        
                        # 验证没有调用 session.finish_processing()
                        mock_session.finish_processing.assert_not_called()

    def test_database_service_import(self):
        """测试数据库服务导入是否正确"""
        try:
            from src.infrastructure.database.repositories.session_repository import session_db_service
            assert session_db_service is not None
            assert hasattr(session_db_service, 'update_session_status')
        except ImportError as e:
            pytest.fail(f"无法导入 session_db_service: {e}")


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
