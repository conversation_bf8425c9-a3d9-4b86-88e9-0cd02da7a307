#!/usr/bin/env python3
"""
测试 CustomEvent 对象访问修复
"""

import os


def test_custom_event_access_fix():
    """测试CustomEvent对象访问修复"""
    print("🧪 测试 CustomEvent 对象访问修复")
    print("=" * 50)
    
    try:
        # 读取session_service.py文件
        service_file = os.path.join(os.path.dirname(__file__), '..', 'src', 'domain', 'kms', 'session_service.py')
        
        with open(service_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查修复的关键代码
        fix_checks = [
            ('run_id = getattr(last_event, \'run_id\', None)', "使用getattr访问run_id属性"),
            ('event_id = getattr(last_event, \'event_id\', \'unknown\')', "使用getattr访问event_id属性"),
            ('# CustomEvent对象使用属性访问，不是字典访问', "添加了解释注释")
        ]
        
        print("📝 修复检查:")
        all_passed = True
        
        for check, desc in fix_checks:
            if check in content:
                print(f"   ✅ {desc}")
            else:
                print(f"   ❌ {desc}")
                all_passed = False
        
        # 检查是否移除了错误的字典访问
        error_checks = [
            ('last_event.get("run_id")', "移除了错误的字典访问run_id"),
            ('last_event.get(\'event_id\')', "移除了错误的字典访问event_id")
        ]
        
        print("\n📝 错误代码移除检查:")
        for check, desc in error_checks:
            if check not in content:
                print(f"   ✅ {desc}")
            else:
                print(f"   ❌ {desc} - 仍然存在")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_getattr_usage_pattern():
    """测试getattr使用模式"""
    print("\n🧪 测试getattr使用模式")
    print("=" * 50)
    
    # 模拟CustomEvent对象
    class MockCustomEvent:
        def __init__(self, run_id=None, event_id=None):
            if run_id:
                self.run_id = run_id
            if event_id:
                self.event_id = event_id
    
    # 测试不同情况
    test_cases = [
        (MockCustomEvent(run_id="test_run_123", event_id="event_456"), "test_run_123", "event_456", "有run_id和event_id"),
        (MockCustomEvent(event_id="event_789"), None, "event_789", "只有event_id，没有run_id"),
        (MockCustomEvent(run_id="test_run_999"), "test_run_999", "unknown", "只有run_id，没有event_id"),
        (MockCustomEvent(), None, "unknown", "既没有run_id也没有event_id")
    ]
    
    print("📝 getattr使用模式测试:")
    all_passed = True
    
    for event, expected_run_id, expected_event_id, description in test_cases:
        # 模拟修复后的代码逻辑
        run_id = getattr(event, 'run_id', None)
        event_id = getattr(event, 'event_id', 'unknown')
        
        if run_id == expected_run_id and event_id == expected_event_id:
            print(f"   ✅ {description}: run_id={run_id}, event_id={event_id}")
        else:
            print(f"   ❌ {description}: 期望run_id={expected_run_id}, event_id={expected_event_id}, 实际run_id={run_id}, event_id={event_id}")
            all_passed = False
    
    return all_passed


def test_error_scenario_simulation():
    """测试错误场景模拟"""
    print("\n🧪 测试错误场景模拟")
    print("=" * 50)
    
    # 模拟原始错误场景
    class MockCustomEventWithoutGet:
        """模拟没有get方法的CustomEvent对象"""
        def __init__(self, run_id="test_run"):
            self.run_id = run_id
            self.event_id = "test_event"
        
        # 故意不实现get方法，模拟CustomEvent的实际情况
    
    event = MockCustomEventWithoutGet()
    
    print("📝 错误场景模拟:")
    
    # 测试原始错误的代码（会失败）
    try:
        # 这会失败，因为CustomEvent没有get方法
        run_id = event.get("run_id")  # 这行会报错
        print(f"   ❌ 原始代码不应该成功: {run_id}")
        return False
    except AttributeError as e:
        print(f"   ✅ 原始代码确实会报错: {e}")
    
    # 测试修复后的代码（会成功）
    try:
        # 修复后的代码
        run_id = getattr(event, 'run_id', None)
        event_id = getattr(event, 'event_id', 'unknown')
        print(f"   ✅ 修复后的代码成功: run_id={run_id}, event_id={event_id}")
        return True
    except Exception as e:
        print(f"   ❌ 修复后的代码仍然失败: {e}")
        return False


def test_terminate_session_logic():
    """测试terminate_session逻辑"""
    print("\n🧪 测试terminate_session逻辑")
    print("=" * 50)
    
    try:
        # 读取session_service.py文件
        service_file = os.path.join(os.path.dirname(__file__), '..', 'src', 'domain', 'kms', 'session_service.py')
        
        with open(service_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找terminate_session方法
        method_start = content.find('async def terminate_session(')
        if method_start == -1:
            print("❌ 未找到terminate_session方法")
            return False
        
        method_end = content.find('async def ', method_start + 1)
        if method_end == -1:
            method_end = len(content)
        
        method_content = content[method_start:method_end]
        
        # 检查方法中的关键逻辑
        logic_checks = [
            ('events_result = await self.get_raw_events(session_id)', "获取原始事件"),
            ('events = events_result["events"]', "提取事件列表"),
            ('last_event = events[0]', "获取最新事件"),
            ('run_id = getattr(last_event, \'run_id\', None)', "正确访问run_id属性"),
            ('if not run_id:', "检查run_id是否存在"),
            ('logger.warning(f"[SessionService] 最新事件中没有run_id，无需终止异步任务:', "无run_id时的警告日志"),
            ('return True', "无run_id时返回成功")
        ]
        
        print("📝 terminate_session逻辑检查:")
        all_passed = True
        
        for check, desc in logic_checks:
            if check in method_content:
                print(f"   ✅ {desc}")
            else:
                print(f"   ❌ {desc}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ terminate_session逻辑测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试 CustomEvent 对象访问修复")
    
    tests = [
        test_custom_event_access_fix,
        test_getattr_usage_pattern,
        test_error_scenario_simulation,
        test_terminate_session_logic
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试 {test.__name__} 异常: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print("✅ 所有测试通过！CustomEvent 对象访问修复成功")
    else:
        print(f"❌ {total - passed} 个测试失败，{passed} 个测试通过")
    
    print(f"\n📊 测试结果: {passed}/{total}")
    
    print("\n📋 修复总结:")
    print("1. ✅ 修复了CustomEvent对象的属性访问方式")
    print("2. ✅ 使用getattr替代字典的get方法")
    print("3. ✅ 添加了默认值处理，避免AttributeError")
    print("4. ✅ 保持了原有的业务逻辑不变")
    
    print("\n🔧 修复详情:")
    print("- 错误代码: last_event.get('run_id')")
    print("- 正确代码: getattr(last_event, 'run_id', None)")
    print("- 错误代码: last_event.get('event_id')")
    print("- 正确代码: getattr(last_event, 'event_id', 'unknown')")
    
    print("\n🎯 修复原因:")
    print("- CustomEvent是对象，不是字典，没有get()方法")
    print("- 需要使用getattr()来安全访问对象属性")
    print("- getattr()提供默认值，避免AttributeError异常")
    
    print("\n✅ 现在disconnect_session接口应该可以正常工作了！")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
