#!/usr/bin/env python3
"""
测试 SessionService 中新添加的 terminate_session 方法
"""

import sys
import os
import asyncio
from unittest.mock import Mock, patch, AsyncMock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))


def test_method_exists():
    """测试方法是否存在"""
    print("🧪 测试 terminate_session 方法是否存在")
    print("=" * 50)
    
    try:
        # 读取文件内容检查方法定义
        file_path = os.path.join(os.path.dirname(__file__), '..', 'src', 'domain', 'kms', 'session_service.py')
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查方法定义
        if 'async def terminate_session(self, session_id: str, context: AuthContext) -> bool:' in content:
            print("✅ terminate_session 方法定义存在")
        else:
            print("❌ terminate_session 方法定义不存在")
            return False
        
        # 检查方法签名的关键部分
        signature_checks = [
            ('session_id: str', "session_id参数类型注解"),
            ('context: AuthContext', "context参数类型注解"),
            (') -> bool:', "返回类型注解"),
            ('async def', "异步方法定义")
        ]
        
        print("📝 方法签名检查:")
        for check, desc in signature_checks:
            if check in content:
                print(f"   ✅ {desc}")
            else:
                print(f"   ❌ {desc}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_method_documentation():
    """测试方法文档"""
    print("\n🧪 测试方法文档")
    print("=" * 50)
    
    try:
        file_path = os.path.join(os.path.dirname(__file__), '..', 'src', 'domain', 'kms', 'session_service.py')
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查文档字符串内容
        doc_checks = [
            ('"""', "文档字符串开始"),
            ('终止会话的异步任务', "方法描述"),
            ('Args:', "参数说明"),
            ('session_id: 会话ID', "session_id参数说明"),
            ('context: 用户认证上下文', "context参数说明"),
            ('Returns:', "返回值说明"),
            ('bool: 终止是否成功', "返回值类型说明"),
            ('Raises:', "异常说明"),
            ('Exception: 当权限验证失败或终止任务失败时抛出', "异常描述")
        ]
        
        print("📝 文档内容检查:")
        all_passed = True
        
        for check, desc in doc_checks:
            if check in content:
                print(f"   ✅ {desc}")
            else:
                print(f"   ❌ {desc}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 文档检查失败: {e}")
        return False


def test_business_logic():
    """测试业务逻辑实现"""
    print("\n🧪 测试业务逻辑实现")
    print("=" * 50)
    
    try:
        file_path = os.path.join(os.path.dirname(__file__), '..', 'src', 'domain', 'kms', 'session_service.py')
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查业务逻辑关键代码
        logic_checks = [
            # 权限验证
            ('await self.get_session_with_permission_check_async(', "权限验证调用"),
            ('required_permission=PermissionType.READ', "READ权限检查"),
            
            # 获取事件和run_id
            ('await self.get_raw_events(session_id)', "获取原始事件"),
            ('events_result["events"]', "提取事件列表"),
            ('events[0]', "获取最后一条事件"),
            ('last_event.get("run_id")', "提取run_id"),
            
            # 终止异步任务
            ('from alibabacloud_wuyingaiinner20250708 import models as waiy_models', "导入waiy_models"),
            ('waiy_models.TerminateAsyncRequest()', "创建终止请求"),
            ('terminate_request.run_id = run_id', "设置run_id"),
            ('create_waiy_infra_client()', "创建waiy客户端"),
            ('waiy_client.terminate_async(terminate_request)', "调用终止方法"),
            
            # 错误处理
            ('except Exception as perm_error:', "权限验证异常处理"),
            ('except WaiyInfraClientError as client_error:', "客户端异常处理"),
            ('logger.info', "信息日志"),
            ('logger.warning', "警告日志"),
            ('logger.error', "错误日志")
        ]
        
        print("📝 业务逻辑检查:")
        all_passed = True
        
        for check, desc in logic_checks:
            if check in content:
                print(f"   ✅ {desc}")
            else:
                print(f"   ❌ {desc}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 业务逻辑检查失败: {e}")
        return False


def test_disconnect_session_modification():
    """测试disconnect_session接口的修改"""
    print("\n🧪 测试disconnect_session接口修改")
    print("=" * 50)
    
    try:
        file_path = os.path.join(os.path.dirname(__file__), '..', 'src', 'presentation', 'api', 'routes', 'session_routes.py')
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查修改的关键代码
        modification_checks = [
            ('await session_service.terminate_session(request.session_id, current_user)', "调用terminate_session方法"),
            ('# 2. 先终止会话的异步任务', "步骤2注释"),
            ('# 3. 调用SSE管理器断开连接', "步骤3注释"),
            ('logger.info(f"[API] 会话异步任务终止成功:', "终止成功日志"),
            ('except Exception as terminate_error:', "终止异常处理"),
            ('logger.warning(f"[API] 终止会话异步任务失败，继续断开SSE连接:', "终止失败警告日志"),
            ('# 即使终止异步任务失败，也要继续断开SSE连接', "容错处理注释")
        ]
        
        print("📝 接口修改检查:")
        all_passed = True
        
        for check, desc in modification_checks:
            if check in content:
                print(f"   ✅ {desc}")
            else:
                print(f"   ❌ {desc}")
                all_passed = False
        
        # 检查调用顺序
        terminate_pos = content.find('await session_service.terminate_session')
        close_connection_pos = content.find('sse_stream_manager.close_session_connection')
        
        if terminate_pos != -1 and close_connection_pos != -1:
            if terminate_pos < close_connection_pos:
                print("   ✅ 调用顺序正确：先终止异步任务，再关闭SSE连接")
            else:
                print("   ❌ 调用顺序错误：应该先终止异步任务，再关闭SSE连接")
                all_passed = False
        else:
            print("   ❌ 未找到关键方法调用")
            all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 接口修改检查失败: {e}")
        return False


def test_error_handling():
    """测试错误处理逻辑"""
    print("\n🧪 测试错误处理逻辑")
    print("=" * 50)
    
    try:
        file_path = os.path.join(os.path.dirname(__file__), '..', 'src', 'domain', 'kms', 'session_service.py')
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查各种错误处理场景
        error_handling_checks = [
            # 权限验证失败
            ('except Exception as perm_error:', "权限验证异常捕获"),
            ('logger.error(f"[SessionService] 会话权限验证失败:', "权限验证失败日志"),
            ('raise', "权限验证异常重新抛出"),
            
            # 没有事件的情况
            ('if not events_result or not events_result.get("events"):', "检查事件结果"),
            ('logger.warning(f"[SessionService] 会话没有事件记录，无需终止异步任务:', "无事件警告日志"),
            ('return True', "无事件时返回成功"),
            
            # 没有run_id的情况
            ('if not run_id:', "检查run_id"),
            ('logger.warning(f"[SessionService] 最新事件中没有run_id，无需终止异步任务:', "无run_id警告日志"),
            
            # 客户端调用异常
            ('except WaiyInfraClientError as client_error:', "客户端异常捕获"),
            ('except Exception as terminate_error:', "终止异常捕获"),
            ('logger.error(f"[SessionService] 终止会话异步任务失败:', "最终异常日志")
        ]
        
        print("📝 错误处理检查:")
        all_passed = True
        
        for check, desc in error_handling_checks:
            if check in content:
                print(f"   ✅ {desc}")
            else:
                print(f"   ❌ {desc}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 错误处理检查失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试 SessionService.terminate_session 方法")
    
    tests = [
        test_method_exists,
        test_method_documentation,
        test_business_logic,
        test_disconnect_session_modification,
        test_error_handling
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试 {test.__name__} 异常: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print("✅ 所有测试通过！terminate_session 方法实现正确")
    else:
        print(f"❌ {total - passed} 个测试失败，{passed} 个测试通过")
    
    print(f"\n📊 测试结果: {passed}/{total}")
    
    print("\n📋 功能实现总结:")
    print("1. ✅ 添加了 terminate_session 异步方法")
    print("2. ✅ 实现了完整的权限验证逻辑")
    print("3. ✅ 实现了从事件中提取run_id的逻辑")
    print("4. ✅ 实现了调用waiy_infra_client终止异步任务")
    print("5. ✅ 实现了完整的错误处理和日志记录")
    print("6. ✅ 修改了disconnect_session接口，先终止任务再关闭连接")
    
    print("\n🎯 业务流程:")
    print("1. 权限验证 → 检查用户是否有READ权限")
    print("2. 获取事件 → 从get_raw_events获取会话事件")
    print("3. 提取run_id → 从最新事件中获取run_id")
    print("4. 终止任务 → 调用waiy_infra_client.terminate_async")
    print("5. 关闭连接 → 调用sse_stream_manager.close_session_connection")
    
    print("\n🛡️ 错误处理:")
    print("- 权限验证失败 → 抛出异常")
    print("- 没有事件记录 → 返回成功（无需终止）")
    print("- 没有run_id → 返回成功（无需终止）")
    print("- 终止任务失败 → 记录警告，继续关闭SSE连接")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
