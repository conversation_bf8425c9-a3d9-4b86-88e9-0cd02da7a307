#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试北京时间统一修复
验证所有时间相关操作都使用北京时间
"""

import sys
import os
from datetime import datetime, timezone, timedelta
from loguru import logger

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))


def test_get_current_time_functions():
    """测试所有get_current_time函数都返回北京时间"""
    logger.info("🧪 测试get_current_time函数")
    
    try:
        # 测试file_models中的get_current_time
        from src.infrastructure.database.models.file_models import get_current_time as file_get_current_time
        file_time = file_get_current_time()
        logger.info(f"✅ file_models.get_current_time(): {file_time}")
        logger.info(f"   时区信息: {file_time.tzinfo}")
        
        # 验证是北京时间（UTC+8）
        beijing_tz = timezone(timedelta(hours=8))
        assert file_time.tzinfo == beijing_tz, f"file_models时区应该是UTC+8，实际为: {file_time.tzinfo}"
        
        # 测试auth_models中的get_current_time
        from src.infrastructure.database.models.auth_models import get_current_time as auth_get_current_time
        auth_time = auth_get_current_time()
        logger.info(f"✅ auth_models.get_current_time(): {auth_time}")
        logger.info(f"   时区信息: {auth_time.tzinfo}")
        
        # 验证是北京时间（UTC+8）
        assert auth_time.tzinfo == beijing_tz, f"auth_models时区应该是UTC+8，实际为: {auth_time.tzinfo}"
        
        logger.info("🎉 所有get_current_time函数都返回北京时间！")
        return True
        
    except Exception as e:
        logger.error(f"❌ get_current_time函数测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def test_file_model_time_operations():
    """测试文件模型的时间操作"""
    logger.info("\n🧪 测试文件模型时间操作")
    
    try:
        from src.infrastructure.database.models.file_models import AlphaFile, get_current_time
        
        # 创建文件对象
        file_obj = AlphaFile(
            title="test_file.txt",
            oss_bucket="test-bucket",
            oss_object_name="test/file.txt",
            type="sessionFile"
        )
        
        # 测试创建时间
        created_time = file_obj.gmt_created
        logger.info(f"✅ 文件创建时间: {created_time}")
        
        # 测试mark_completed方法
        file_obj.mark_completed(file_size=1024)
        completed_time = file_obj.gmt_modified
        logger.info(f"✅ 文件完成时间: {completed_time}")
        logger.info(f"   时区信息: {completed_time.tzinfo}")
        
        # 验证是北京时间
        beijing_tz = timezone(timedelta(hours=8))
        assert completed_time.tzinfo == beijing_tz, f"完成时间应该是北京时间，实际为: {completed_time.tzinfo}"
        
        # 测试mark_failed方法
        file_obj.mark_failed("测试错误")
        failed_time = file_obj.gmt_modified
        logger.info(f"✅ 文件失败时间: {failed_time}")
        logger.info(f"   时区信息: {failed_time.tzinfo}")
        
        # 验证是北京时间
        assert failed_time.tzinfo == beijing_tz, f"失败时间应该是北京时间，实际为: {failed_time.tzinfo}"
        
        logger.info("🎉 文件模型时间操作测试通过！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 文件模型时间操作测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def test_time_utils_conversion():
    """测试TimeUtils的时间转换"""
    logger.info("\n🧪 测试TimeUtils时间转换")
    
    try:
        from src.domain.utils.time_utils import TimeUtils
        
        # 测试1: 北京时间转换为ISO格式
        beijing_tz = timezone(timedelta(hours=8))
        beijing_dt = datetime(2025, 8, 24, 14, 44, 0, tzinfo=beijing_tz)
        iso_str = TimeUtils.to_iso8601_utc(beijing_dt)
        logger.info(f"✅ 北京时间转换:")
        logger.info(f"   输入: {beijing_dt}")
        logger.info(f"   输出: {iso_str}")
        
        # 应该转换为UTC时间（减去8小时）
        expected_utc = "2025-08-24T06:44Z"
        assert iso_str == expected_utc, f"期望: {expected_utc}, 实际: {iso_str}"
        
        # 测试2: 无时区信息的datetime（应该假设为北京时间）
        naive_dt = datetime(2025, 8, 24, 14, 44, 0)
        naive_iso = TimeUtils.to_iso8601_utc(naive_dt)
        logger.info(f"✅ 无时区datetime转换:")
        logger.info(f"   输入: {naive_dt}")
        logger.info(f"   输出: {naive_iso}")
        
        # 应该假设为北京时间，然后转换为UTC
        expected_utc = "2025-08-24T06:44Z"
        assert naive_iso == expected_utc, f"期望: {expected_utc}, 实际: {naive_iso}"
        
        logger.info("🎉 TimeUtils时间转换测试通过！")
        return True
        
    except Exception as e:
        logger.error(f"❌ TimeUtils时间转换测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def test_real_scenario_simulation():
    """模拟真实场景，验证时间一致性"""
    logger.info("\n🧪 模拟真实场景")
    
    try:
        from src.infrastructure.database.models.file_models import get_current_time
        from src.domain.utils.time_utils import TimeUtils
        
        logger.info("✅ 模拟文件上传完整流程:")
        
        # 1. 文件创建（数据库记录创建）
        gmt_created = get_current_time()
        logger.info(f"   1. 文件创建: {gmt_created}")
        logger.info(f"      时区: {gmt_created.tzinfo}")
        
        # 2. 文件上传完成（调用mark_completed）
        import time
        time.sleep(0.1)  # 模拟上传耗时
        gmt_modified = get_current_time()
        logger.info(f"   2. 上传完成: {gmt_modified}")
        logger.info(f"      时区: {gmt_modified.tzinfo}")
        
        # 3. API返回格式化
        created_iso = TimeUtils.to_iso8601_utc(gmt_created)
        modified_iso = TimeUtils.to_iso8601_utc(gmt_modified)
        
        logger.info(f"   3. API返回:")
        logger.info(f"      GmtCreated: {created_iso}")
        logger.info(f"      GmtModified: {modified_iso}")
        
        # 4. 验证逻辑正确性
        assert gmt_modified >= gmt_created, "修改时间应该晚于创建时间"
        assert modified_iso >= created_iso, "修改时间ISO应该晚于创建时间ISO"
        
        # 5. 验证时区一致性
        beijing_tz = timezone(timedelta(hours=8))
        assert gmt_created.tzinfo == beijing_tz, "创建时间应该是北京时间"
        assert gmt_modified.tzinfo == beijing_tz, "修改时间应该是北京时间"
        
        # 6. 解析时间进行比较
        created_dt = datetime.fromisoformat(created_iso.replace('Z', '+00:00'))
        modified_dt = datetime.fromisoformat(modified_iso.replace('Z', '+00:00'))
        
        time_diff = (modified_dt - created_dt).total_seconds()
        logger.info(f"   时间差: {time_diff:.3f} 秒")
        
        assert time_diff >= 0, f"时间差应该为正数或零，实际为: {time_diff}"
        
        logger.info("✅ 验证问题已修复:")
        logger.info(f"   现在所有时间都使用北京时间")
        logger.info(f"   GmtCreated <= GmtModified，符合逻辑预期")
        
        logger.info("🎉 真实场景模拟测试通过！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 真实场景模拟测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def main():
    """主测试函数"""
    logger.info("🚀 开始测试北京时间统一修复")
    
    success1 = test_get_current_time_functions()
    success2 = test_file_model_time_operations()
    success3 = test_time_utils_conversion()
    success4 = test_real_scenario_simulation()
    
    logger.info("\n" + "=" * 60)
    if success1 and success2 and success3 and success4:
        logger.info("🎉 所有测试通过！")
        
        logger.info("\n📋 修复总结:")
        logger.info("1. ✅ 统一所有get_current_time()函数返回北京时间")
        logger.info("2. ✅ 替换所有datetime.utcnow()为get_current_time()")
        logger.info("3. ✅ 修复文件模型的mark_completed()和mark_failed()方法")
        logger.info("4. ✅ TimeUtils.to_iso8601_utc()正确处理北京时间")
        logger.info("5. ✅ 所有时间操作保持一致的时区处理")
        
        logger.info("\n🎯 问题解决:")
        logger.info("- 之前: 混合使用UTC时间和北京时间，导致时间逻辑错误")
        logger.info("- 现在: 统一使用北京时间，确保时间逻辑正确")
        logger.info("- 结果: GmtCreated <= GmtModified，符合逻辑预期")
        
        logger.info("\n📝 修改文件:")
        logger.info("- src/infrastructure/database/models/file_models.py")
        logger.info("- src/infrastructure/database/repositories/file_repository.py")
        logger.info("- src/infrastructure/database/repositories/auth_repository.py")
        logger.info("- src/domain/kms/auth_service.py")
        logger.info("- src/domain/utils/time_utils.py")
        
        logger.info("\n🔧 修改内容:")
        logger.info("- 所有datetime.utcnow()替换为get_current_time()")
        logger.info("- get_current_time()统一返回北京时间")
        logger.info("- TimeUtils默认假设输入为北京时间")
        
        return True
    else:
        logger.error("❌ 部分测试失败")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
