#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 FileService.get_download_urls 方法
"""

import sys
import os
import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.domain.services.file_service import file_service
from src.application.file_api_models import DownloadUrlsResponse, DownloadLink, FailedFile
from src.infrastructure.database.models.file_models import AlphaFile, FileType, UploadStatus


class TestFileServiceDownloadUrls:
    """测试 FileService.get_download_urls 方法"""

    def setup_method(self):
        """每个测试方法执行前的设置"""
        self.test_file_id = "artifact-65aedb6d788442ee8d017bf39c7c2e9e"
        self.test_file_ids = [self.test_file_id]

    @patch('src.domain.kms.file_service.file_repository')
    @patch.object(file_service, 'get_file_download_url')
    def test_get_download_urls_success(self, mock_get_download_url, mock_file_repository):
        """测试成功获取下载链接"""
        # 模拟数据库中的文件对象
        mock_file = Mock(spec=AlphaFile)
        mock_file.id = self.test_file_id
        mock_file.title = "test_artifact.txt"
        mock_file.file_size = 1024
        mock_file.content_type = "text/plain"
        mock_file.upload_status = UploadStatus.COMPLETED.value
        
        # 模拟 repository 返回文件列表
        mock_file_repository.get_files_by_ids.return_value = [mock_file]
        
        # 模拟生成下载链接
        mock_download_url = "https://example.com/download/test_artifact.txt?expires=3600"
        mock_get_download_url.return_value = mock_download_url
        
        # 执行测试
        result = file_service.get_download_urls(self.test_file_ids, expires=3600)
        
        # 验证结果
        assert isinstance(result, DownloadUrlsResponse)
        assert len(result.download_links) == 1
        assert len(result.failed_files) == 0
        
        download_link = result.download_links[0]
        assert download_link.file_id == self.test_file_id
        assert download_link.file_name == "test_artifact.txt"
        assert download_link.file_size == 1024
        assert download_link.content_type == "text/plain"
        assert download_link.download_url == mock_download_url
        assert download_link.expires_in == 3600
        
        # 验证调用
        mock_file_repository.get_files_by_ids.assert_called_once_with(self.test_file_ids)
        mock_get_download_url.assert_called_once_with(mock_file, 3600)

    @patch('src.domain.kms.file_service.file_repository')
    def test_get_download_urls_file_not_found(self, mock_file_repository):
        """测试文件不存在的情况"""
        # 模拟 repository 返回空列表
        mock_file_repository.get_files_by_ids.return_value = []
        
        # 执行测试
        result = file_service.get_download_urls(self.test_file_ids)
        
        # 验证结果
        assert isinstance(result, DownloadUrlsResponse)
        assert len(result.download_links) == 0
        assert len(result.failed_files) == 1
        
        failed_file = result.failed_files[0]
        assert failed_file.file_id == self.test_file_id
        assert failed_file.file_name == "未知"
        assert failed_file.error == "文件不存在"

    @patch('src.domain.kms.file_service.file_repository')
    def test_get_download_urls_file_not_completed(self, mock_file_repository):
        """测试文件未上传完成的情况"""
        # 模拟数据库中的未完成文件
        mock_file = Mock(spec=AlphaFile)
        mock_file.id = self.test_file_id
        mock_file.title = "uploading_file.txt"
        mock_file.upload_status = UploadStatus.UPLOADING.value
        
        mock_file_repository.get_files_by_ids.return_value = [mock_file]
        
        # 执行测试
        result = file_service.get_download_urls(self.test_file_ids)
        
        # 验证结果
        assert isinstance(result, DownloadUrlsResponse)
        assert len(result.download_links) == 0
        assert len(result.failed_files) == 1
        
        failed_file = result.failed_files[0]
        assert failed_file.file_id == self.test_file_id
        assert failed_file.file_name == "uploading_file.txt"
        assert "文件未上传完成" in failed_file.error

    @patch('src.domain.kms.file_service.file_repository')
    @patch.object(file_service, 'get_file_download_url')
    def test_get_download_urls_generate_url_failed(self, mock_get_download_url, mock_file_repository):
        """测试生成下载链接失败的情况"""
        # 模拟数据库中的文件对象
        mock_file = Mock(spec=AlphaFile)
        mock_file.id = self.test_file_id
        mock_file.title = "test_file.txt"
        mock_file.upload_status = UploadStatus.COMPLETED.value
        
        mock_file_repository.get_files_by_ids.return_value = [mock_file]
        
        # 模拟生成下载链接失败
        mock_get_download_url.return_value = None
        
        # 执行测试
        result = file_service.get_download_urls(self.test_file_ids)
        
        # 验证结果
        assert isinstance(result, DownloadUrlsResponse)
        assert len(result.download_links) == 0
        assert len(result.failed_files) == 1
        
        failed_file = result.failed_files[0]
        assert failed_file.file_id == self.test_file_id
        assert failed_file.file_name == "test_file.txt"
        assert failed_file.error == "生成下载链接失败"

    @patch('src.domain.kms.file_service.file_repository')
    @patch.object(file_service, 'get_file_download_url')
    def test_get_download_urls_mixed_results(self, mock_get_download_url, mock_file_repository):
        """测试混合结果：部分成功，部分失败"""
        file_ids = [
            "artifact-65aedb6d788442ee8d017bf39c7c2e9e",
            "artifact-nonexistent",
            "artifact-uploading"
        ]
        
        # 模拟数据库返回部分文件
        mock_file1 = Mock(spec=AlphaFile)
        mock_file1.id = file_ids[0]
        mock_file1.title = "success_file.txt"
        mock_file1.file_size = 2048
        mock_file1.content_type = "text/plain"
        mock_file1.upload_status = UploadStatus.COMPLETED.value
        
        mock_file2 = Mock(spec=AlphaFile)
        mock_file2.id = file_ids[2]
        mock_file2.title = "uploading_file.txt"
        mock_file2.upload_status = UploadStatus.UPLOADING.value
        
        mock_file_repository.get_files_by_ids.return_value = [mock_file1, mock_file2]
        
        # 模拟第一个文件成功生成下载链接
        mock_get_download_url.return_value = "https://example.com/download/success_file.txt"
        
        # 执行测试
        result = file_service.get_download_urls(file_ids)
        
        # 验证结果
        assert isinstance(result, DownloadUrlsResponse)
        assert len(result.download_links) == 1
        assert len(result.failed_files) == 2
        
        # 验证成功的下载链接
        download_link = result.download_links[0]
        assert download_link.file_id == file_ids[0]
        assert download_link.file_name == "success_file.txt"
        
        # 验证失败的文件
        failed_file_ids = {f.file_id for f in result.failed_files}
        assert file_ids[1] in failed_file_ids  # 不存在的文件
        assert file_ids[2] in failed_file_ids  # 未完成上传的文件

    @patch('src.domain.kms.file_service.file_repository')
    def test_get_download_urls_repository_exception(self, mock_file_repository):
        """测试数据库异常的情况"""
        # 模拟数据库异常
        mock_file_repository.get_files_by_ids.side_effect = Exception("Database error")
        
        # 执行测试
        result = file_service.get_download_urls(self.test_file_ids)
        
        # 验证结果
        assert isinstance(result, DownloadUrlsResponse)
        assert len(result.download_links) == 0
        assert len(result.failed_files) == 1
        
        failed_file = result.failed_files[0]
        assert failed_file.file_id == self.test_file_id
        assert failed_file.error == "服务异常"

    def test_get_download_urls_empty_file_ids(self):
        """测试空文件ID列表"""
        result = file_service.get_download_urls([])
        
        assert isinstance(result, DownloadUrlsResponse)
        assert len(result.download_links) == 0
        assert len(result.failed_files) == 0


if __name__ == "__main__":
    # 运行特定测试
    test_instance = TestFileServiceDownloadUrls()
    test_instance.setup_method()
    
    print("开始测试 FileService.get_download_urls 方法...")
    
    # 可以在这里添加实际的测试调用
    # 注意：这需要真实的数据库连接和文件数据
    try:
        # 实际调用测试（需要真实环境）
        result = file_service.get_download_urls(["artifact-65aedb6d788442ee8d017bf39c7c2e9e"])
        print(f"测试结果: {result}")
    except Exception as e:
        print(f"测试异常: {e}")
    
    print("测试完成！")
