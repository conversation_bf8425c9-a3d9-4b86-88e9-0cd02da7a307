#!/usr/bin/env python3
"""
测试文件内容长度检查功能
"""

import asyncio
import sys
import os
from unittest.mock import Mock, patch, AsyncMock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.domain.services.session_service import SessionService, FileContentTooLargeError
from src.application.api_models import ResourceType, SessionResource
from src.domain.services.auth_service import AuthContext


class MockWaiyResource:
    """模拟WaiyInfra资源对象"""
    def __init__(self, resource_type: str, content: str = None):
        self.type = resource_type
        self.content = content


class MockFileInfo:
    """模拟文件信息"""
    def __init__(self, content: str, ali_uid: int = 12345):
        self.content = content
        self.ali_uid = ali_uid
    
    def get(self, key: str, default=None):
        if key == 'content':
            return self.content
        elif key == 'ali_uid':
            return self.ali_uid
        elif key == 'title':
            return 'test_file.txt'
        elif key == 'download_url':
            return 'http://example.com/file'
        elif key == 'file_size':
            return len(self.content) if self.content else 0
        elif key == 'gmt_created':
            return '2024-01-01T00:00:00'
        return default


async def test_file_content_length_check():
    """测试文件内容长度检查功能"""
    print("开始测试文件内容长度检查功能...")
    
    # 创建SessionService实例
    session_service = SessionService()
    
    # 创建认证上下文
    context = AuthContext(ali_uid=12345, wy_id="test_wy_id")
    
    # 测试用例1: 正常情况 - 文件内容长度在限制内
    print("\n=== 测试用例1: 正常情况 ===")
    try:
        # 模拟小文件内容（1000字符）
        small_content = "a" * 1000
        mock_file_info = MockFileInfo(content=small_content)
        
        # 模拟waiy_client.create_message_resource返回的资源
        mock_waiy_resource = MockWaiyResource("file", small_content)
        
        with patch('src.domain.kms.file_service.file_service.get_file_info', return_value=mock_file_info), \
             patch.object(session_service, '_process_file_resource', return_value=mock_waiy_resource):
            
            # 创建资源列表
            resources = [
                SessionResource(type=ResourceType.FILE, resource_id="1")
            ]
            
            # 调用资源处理方法
            result = await session_service._process_resources(resources, context)
            
            print(f"✅ 正常情况测试通过: 返回 {len(result)} 个资源")
            print(f"   文件内容长度: {len(small_content)} 字符")
            
    except Exception as e:
        print(f"❌ 正常情况测试失败: {e}")
    
    # 测试用例2: 异常情况 - 单个文件内容超过限制
    print("\n=== 测试用例2: 单个大文件 ===")
    try:
        # 模拟大文件内容（25万字符，超过20万限制）
        large_content = "b" * 250000
        mock_file_info = MockFileInfo(content=large_content)
        mock_waiy_resource = MockWaiyResource("file", large_content)
        
        with patch('src.domain.kms.file_service.file_service.get_file_info', return_value=mock_file_info), \
             patch.object(session_service, '_process_file_resource', return_value=mock_waiy_resource):
            
            resources = [
                SessionResource(type=ResourceType.FILE, resource_id="2")
            ]
            
            # 调用资源处理方法，应该抛出异常
            await session_service._process_resources(resources, context)
            
            print("❌ 单个大文件测试失败: 应该抛出异常但没有")
            
    except FileContentTooLargeError as e:
        print(f"✅ 单个大文件测试通过: 正确抛出异常")
        print(f"   异常信息: {e.message}")
        print(f"   文件内容长度: {e.total_length} 字符")
        print(f"   限制: {e.limit} 字符")
    except Exception as e:
        print(f"❌ 单个大文件测试失败: 抛出了错误的异常类型: {e}")
    
    # 测试用例3: 异常情况 - 多个文件内容累计超过限制
    print("\n=== 测试用例3: 多个文件累计超限 ===")
    try:
        # 模拟3个文件，每个8万字符，总计24万字符（超过20万限制）
        content1 = "c" * 80000
        content2 = "d" * 80000
        content3 = "e" * 80000
        
        def mock_get_file_info(file_id):
            contents = {1: content1, 2: content2, 3: content3}
            return MockFileInfo(content=contents.get(int(file_id), ""))
        
        def mock_create_resource(type, **kwargs):
            return MockWaiyResource(type, kwargs.get('content', ''))
        
        def mock_process_file_resource(file_id, context):
            contents = {1: content1, 2: content2, 3: content3}
            content = contents.get(int(file_id), "")
            return MockWaiyResource("file", content)

        with patch('src.domain.kms.file_service.file_service.get_file_info', side_effect=mock_get_file_info), \
             patch.object(session_service, '_process_file_resource', side_effect=mock_process_file_resource):
            
            resources = [
                SessionResource(type=ResourceType.FILE, resource_id="1"),
                SessionResource(type=ResourceType.FILE, resource_id="2"),
                SessionResource(type=ResourceType.FILE, resource_id="3")
            ]
            
            # 调用资源处理方法，应该抛出异常
            await session_service._process_resources(resources, context)
            
            print("❌ 多个文件累计超限测试失败: 应该抛出异常但没有")
            
    except FileContentTooLargeError as e:
        print(f"✅ 多个文件累计超限测试通过: 正确抛出异常")
        print(f"   异常信息: {e.message}")
        print(f"   文件内容总长度: {e.total_length} 字符")
        print(f"   限制: {e.limit} 字符")
    except Exception as e:
        print(f"❌ 多个文件累计超限测试失败: 抛出了错误的异常类型: {e}")
    
    # 测试用例4: 混合资源类型 - 包含文件和知识库资源
    print("\n=== 测试用例4: 混合资源类型 ===")
    try:
        # 模拟文件内容（15万字符，在限制内）
        mixed_content = "f" * 150000
        mock_file_info = MockFileInfo(content=mixed_content)
        mock_waiy_file_resource = MockWaiyResource("file", mixed_content)
        mock_waiy_kb_resource = MockWaiyResource("knowledge_base", None)  # 知识库资源没有content
        
        def mock_create_resource(type, **kwargs):
            if type == "file":
                return mock_waiy_file_resource
            else:
                return mock_waiy_kb_resource
        
        with patch('src.domain.kms.file_service.file_service.get_file_info', return_value=mock_file_info), \
             patch.object(session_service, '_process_file_resource', return_value=mock_waiy_file_resource), \
             patch.object(session_service, '_process_knowledge_base_resource', return_value=mock_waiy_kb_resource):
            
            resources = [
                SessionResource(type=ResourceType.FILE, resource_id="1"),
                SessionResource(type=ResourceType.KNOWLEDGE_BASE, resource_id="kb1")
            ]
            
            # 调用资源处理方法
            result = await session_service._process_resources(resources, context)
            
            print(f"✅ 混合资源类型测试通过: 返回 {len(result)} 个资源")
            print(f"   文件内容长度: {len(mixed_content)} 字符 (在限制内)")
            
    except Exception as e:
        print(f"❌ 混合资源类型测试失败: {e}")


async def test_api_exception_handling():
    """测试API异常处理"""
    print("\n=== 测试API异常处理 ===")

    try:
        from src.presentation.api.dependencies.api_common_utils import handle_exception
        from src.domain.services.session_service import FileContentTooLargeError
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return
    
    # 测试FileContentTooLargeError异常处理
    try:
        error = FileContentTooLargeError(
            message="文件内容过多，请精简一下",
            total_length=250000,
            limit=200000
        )
        
        result = handle_exception(error, "test_request_id")
        
        print("✅ API异常处理测试通过:")
        print(f"   返回码: {result.get('code')}")
        print(f"   消息: {result.get('message')}")
        print(f"   成功标志: {result.get('success')}")
        
    except Exception as e:
        print(f"❌ API异常处理测试失败: {e}")


async def main():
    """主测试函数"""
    print("🚀 开始文件内容长度检查功能测试")
    print("=" * 60)
    
    await test_file_content_length_check()
    await test_api_exception_handling()
    
    print("\n" + "=" * 60)
    print("✅ 测试完成！")
    
    print("\n📋 功能说明:")
    print("1. 在 _process_resources 方法中检查所有文件资源的content字段长度")
    print("2. 如果总长度超过20万字符，抛出 FileContentTooLargeError 异常")
    print("3. 在 /sessions/send 接口中，返回session_id之前进行资源检查")
    print("4. 异常处理器会返回友好的错误信息给前端")


if __name__ == "__main__":
    asyncio.run(main())
