#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试KnowledgeService异步文件上传功能
"""

import asyncio
import sys
import os
from unittest.mock import Mock, patch, AsyncMock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.domain.services.knowledge_service import KnowledgeService
from src.domain.services.auth_service import AuthContext


class MockCloudStorageClient:
    """模拟云存储客户端"""
    
    def __init__(self):
        self.pre_upload_called = False
        self.complete_upload_called = False
    
    async def pre_upload_file_async(self, file_path: str, product_type: str, user_ali_uid: str, wy_drive_owner_id: str):
        """模拟预上传文件"""
        self.pre_upload_called = True
        mock_response = Mock()
        mock_response.body = Mock()
        mock_response.body.upload_url = "https://example.com/upload/test_file"
        mock_response.body.file_name = "test_file.pdf"
        return mock_response
    
    async def complete_upload_file_async(self, file_path: str, product_type: str, user_ali_uid: str, wy_drive_owner_id: str):
        """模拟完成上传文件"""
        self.complete_upload_called = True
        mock_response = Mock()
        mock_response.body = Mock()
        mock_response.body.file_path = "/files/test_file"
        return mock_response


class MockFileService:
    """模拟文件服务"""
    
    def get_file_info(self, file_id: int):
        """模拟获取文件信息"""
        return {
            'id': file_id,
            'title': f'test_file_{file_id}.pdf',
            'upload_status': 'COMPLETED',
            'download_url': f'https://example.com/download/test_file_{file_id}.pdf',
            'file_size': 1024,
            'content_type': 'application/pdf'
        }


async def test_async_file_upload():
    """测试异步文件上传功能"""
    print("🚀 开始测试KnowledgeService异步文件上传功能...")
    
    # 创建模拟的认证上下文
    auth_context = AuthContext(
        user_key="test_user",
        ali_uid=123456,
        wy_id="test_wy_id",
        account_type="test"
    )
    
    # 创建KnowledgeService实例
    knowledge_service = KnowledgeService()
    
    # 模拟文件ID列表
    file_id_list = ["file_001", "file_002", "file_003"]
    kb_id = "test_kb_001"
    session_id = "test_session_001"
    
    # 使用patch装饰器模拟依赖
    with patch('src.domain.kms.knowledge_service.get_cloud_storage_client') as mock_get_client, \
         patch('src.domain.kms.knowledge_service.file_service') as mock_file_service, \
         patch('src.domain.kms.knowledge_service.knowledge_service.kb_documents_repository') as mock_doc_repo, \
         patch('src.domain.kms.knowledge_service.knowledge_service.kb_document_relations_repository') as mock_relations_repo, \
         patch('aiohttp.ClientSession') as mock_session:
        
        # 设置模拟对象
        mock_cloud_client = MockCloudStorageClient()
        mock_get_client.return_value = mock_cloud_client
        
        mock_file_service.get_file_info = MockFileService().get_file_info
        
        # 模拟数据库操作
        mock_doc_repo.list_kb_documents.return_value = []
        mock_doc_repo.create_kb_document.return_value = True
        mock_relations_repo.batch_create_relations.return_value = True
        
        # 模拟HTTP请求
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.read.return_value = b"test file content"
        
        mock_session_context = AsyncMock()
        mock_session_context.__aenter__.return_value = mock_session_context
        mock_session_context.__aexit__.return_value = None
        mock_session_context.get.return_value = mock_session_context
        mock_session_context.put.return_value = mock_response
        mock_session.return_value = mock_session_context
        
        try:
            # 调用异步文件上传方法
            await knowledge_service._process_file_list_async(
                auth_context=auth_context,
                kb_id=kb_id,
                session_id=session_id,
                file_id_list=file_id_list
            )
            
            print("✅ 异步文件上传测试完成")
            print(f"   - 预上传调用次数: {mock_cloud_client.pre_upload_called}")
            print(f"   - 完成上传调用次数: {mock_cloud_client.complete_upload_called}")
            print(f"   - 文件ID列表: {file_id_list}")
            print(f"   - 知识库ID: {kb_id}")
            print(f"   - 会话ID: {session_id}")
            
            # 验证调用
            assert mock_cloud_client.pre_upload_called, "预上传方法应该被调用"
            assert mock_cloud_client.complete_upload_called, "完成上传方法应该被调用"
            assert mock_doc_repo.list_kb_documents.called, "文档查询应该被调用"
            assert mock_relations_repo.batch_create_relations.called, "关联创建应该被调用"
            
            print("✅ 所有验证通过")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()


async def test_empty_file_list():
    """测试空文件列表的情况"""
    print("\n🧪 测试空文件列表...")
    
    auth_context = AuthContext(
        user_key="test_user",
        ali_uid=123456,
        wy_id="test_wy_id",
        account_type="test"
    )
    
    knowledge_service = KnowledgeService()
    
    with patch('src.domain.kms.knowledge_service.knowledge_service.kb_documents_repository') as mock_doc_repo:
        mock_doc_repo.list_kb_documents.return_value = []
        
        try:
            await knowledge_service._process_file_list_async(
                auth_context=auth_context,
                kb_id="test_kb",
                session_id="test_session",
                file_id_list=[]
            )
            print("✅ 空文件列表测试通过")
        except Exception as e:
            print(f"❌ 空文件列表测试失败: {e}")


async def test_duplicate_files():
    """测试重复文件的情况"""
    print("\n🧪 测试重复文件...")
    
    auth_context = AuthContext(
        user_key="test_user",
        ali_uid=123456,
        wy_id="test_wy_id",
        account_type="test"
    )
    
    knowledge_service = KnowledgeService()
    
    # 模拟已存在的文档
    from src.infrastructure.database.models.knowledgebase_models import KbDocumentModel
    existing_doc = Mock(spec=KbDocumentModel)
    existing_doc.file_id = "file_001"
    
    with patch('src.domain.kms.knowledge_service.knowledge_service.kb_documents_repository') as mock_doc_repo:
        mock_doc_repo.list_kb_documents.return_value = [existing_doc]
        
        try:
            await knowledge_service._process_file_list_async(
                auth_context=auth_context,
                kb_id="test_kb",
                session_id="test_session",
                file_id_list=["file_001", "file_002"]
            )
            print("✅ 重复文件测试通过")
        except Exception as e:
            print(f"❌ 重复文件测试失败: {e}")


async def main():
    """主测试函数"""
    print("=" * 60)
    print("KnowledgeService 异步文件上传功能测试")
    print("=" * 60)
    
    # 运行所有测试
    await test_async_file_upload()
    await test_empty_file_list()
    await test_duplicate_files()
    
    print("\n" + "=" * 60)
    print("所有测试完成")
    print("=" * 60)


if __name__ == "__main__":
    # 运行异步测试
    asyncio.run(main())
