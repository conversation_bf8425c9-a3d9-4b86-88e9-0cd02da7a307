#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试桌面验证和 region_id 获取功能
"""

import sys
import os
from unittest.mock import Mock, patch

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.domain.services.session_service import SessionService
from src.domain.services.auth_service import AuthContext
from src.domain.services.user_service import DesktopInfo, EnvironmentListResult


def test_build_runtime_resource_with_valid_desktop():
    """测试使用有效桌面ID构建运行时资源"""
    print("=== 测试有效桌面ID的运行时资源构建 ===")
    
    try:
        # 创建 SessionService 实例
        session_service = SessionService()
        
        # 创建模拟的认证上下文
        mock_context = Mock(spec=AuthContext)
        mock_context.user_key = "test_user"
        mock_context.end_user_id = 12345
        
        # 创建模拟的桌面环境
        desktop_1 = DesktopInfo(
            name="桌面1",
            desktop_id="desktop_001",
            desktop_status="Running",
            region_id="cn-hangzhou"
        )
        
        desktop_2 = DesktopInfo(
            name="桌面2", 
            desktop_id="desktop_002",
            desktop_status="Running",
            region_id="cn-beijing"
        )
        
        agentbay = DesktopInfo(
            name="Agentbay",
            desktop_id="Agentbay",
            desktop_status="Running",
            region_id="default"
        )
        
        # 创建模拟的用户环境数据
        mock_user_environments = EnvironmentListResult(
            environments=[desktop_1, desktop_2, agentbay],
            total=3
        )
        
        # 模拟 user_service.get_user_environments
        with patch('src.domain.kms.user_service.user_service') as mock_user_service:
            mock_user_service.get_user_environments.return_value = mock_user_environments
            
            # 测试用例1：有效的桌面ID
            print("\n1. 测试有效的桌面ID...")
            
            test_cases = [
                {"desktop_id": "desktop_001", "expected_region": "cn-hangzhou"},
                {"desktop_id": "desktop_002", "expected_region": "cn-beijing"},
                {"desktop_id": "Agentbay", "expected_region": "default"},
            ]
            
            for case in test_cases:
                desktop_id = case["desktop_id"]
                expected_region = case["expected_region"]
                
                runtime_resource = session_service._build_runtime_resource(
                    desktop_id=desktop_id,
                    auth_code="test_auth_code",
                    auth_context=mock_context
                )
                
                print(f"   ✓ 桌面ID: {desktop_id}")
                print(f"     类型: {runtime_resource.type}")
                print(f"     云资源ID: {runtime_resource.cloud_resource_id}")
                print(f"     区域: {runtime_resource.region}")
                print(f"     Token: {runtime_resource.token}")
                
                # 验证结果
                if desktop_id.lower() == "agentbay":
                    assert runtime_resource.type == "agentbay", f"AgentBay 类型应该是 agentbay"
                    assert runtime_resource.cloud_resource_id == "akm-2f6c64e0-ba79-47cb-9847-d265773c7873", f"AgentBay 云资源ID不正确"
                else:
                    assert runtime_resource.type == "desktop", f"桌面类型应该是 desktop"
                    assert runtime_resource.cloud_resource_id == desktop_id, f"云资源ID应该是 {desktop_id}"
                    assert runtime_resource.region == expected_region, f"区域应该是 {expected_region}，实际是 {runtime_resource.region}"
                
                print(f"     验证通过 ✓")
        
        return True
        
    except Exception as e:
        print(f"\n✗ 有效桌面ID测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_build_runtime_resource_with_invalid_desktop():
    """测试使用无效桌面ID构建运行时资源"""
    print("\n=== 测试无效桌面ID的运行时资源构建 ===")
    
    try:
        # 创建 SessionService 实例
        session_service = SessionService()
        
        # 创建模拟的认证上下文
        mock_context = Mock(spec=AuthContext)
        mock_context.user_key = "test_user"
        mock_context.end_user_id = 12345
        
        # 创建模拟的桌面环境（只有两个桌面）
        desktop_1 = DesktopInfo(
            name="桌面1",
            desktop_id="desktop_001",
            desktop_status="Running",
            region_id="cn-hangzhou"
        )
        
        agentbay = DesktopInfo(
            name="Agentbay",
            desktop_id="Agentbay",
            desktop_status="Running",
            region_id="default"
        )
        
        # 创建模拟的用户环境数据
        mock_user_environments = EnvironmentListResult(
            environments=[desktop_1, agentbay],
            total=2
        )
        
        # 模拟 user_service.get_user_environments
        with patch('src.domain.kms.user_service.user_service') as mock_user_service:
            mock_user_service.get_user_environments.return_value = mock_user_environments
            
            # 测试无效的桌面ID
            print("\n1. 测试无效的桌面ID...")
            
            invalid_desktop_id = "desktop_999"
            
            try:
                runtime_resource = session_service._build_runtime_resource(
                    desktop_id=invalid_desktop_id,
                    auth_code="test_auth_code",
                    auth_context=mock_context
                )
                
                # 如果没有抛出异常，说明测试失败
                print(f"✗ 应该抛出 ValueError，但没有抛出异常")
                return False
                
            except ValueError as e:
                # 验证错误消息
                error_msg = str(e)
                print(f"   ✓ 正确抛出 ValueError: {error_msg}")
                
                # 验证错误消息包含期望的内容
                assert invalid_desktop_id in error_msg, f"错误消息应该包含无效的桌面ID: {invalid_desktop_id}"
                assert "不在用户可用环境列表中" in error_msg, "错误消息应该包含提示信息"
                assert "desktop_001" in error_msg, "错误消息应该包含可用的桌面列表"
                assert "Agentbay" in error_msg, "错误消息应该包含可用的桌面列表"
                
                print(f"   ✓ 错误消息验证通过")
                
            except Exception as e:
                print(f"✗ 抛出了错误的异常类型: {type(e).__name__}: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"\n✗ 无效桌面ID测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_build_runtime_resource_without_auth_context():
    """测试没有认证上下文时的运行时资源构建"""
    print("\n=== 测试没有认证上下文的运行时资源构建 ===")
    
    try:
        # 创建 SessionService 实例
        session_service = SessionService()
        
        # 测试没有认证上下文的情况
        print("\n1. 测试没有认证上下文...")
        
        runtime_resource = session_service._build_runtime_resource(
            desktop_id="desktop_001",
            auth_code="test_auth_code",
            auth_context=None  # 没有认证上下文
        )
        
        print(f"   ✓ 类型: {runtime_resource.type}")
        print(f"   ✓ 云资源ID: {runtime_resource.cloud_resource_id}")
        print(f"   ✓ 区域: {runtime_resource.region}")
        print(f"   ✓ Token: {runtime_resource.token}")
        
        # 验证结果
        assert runtime_resource.type == "desktop", "类型应该是 desktop"
        assert runtime_resource.cloud_resource_id == "desktop_001", "云资源ID应该是 desktop_001"
        assert runtime_resource.region is None, "区域应该是 None（因为没有认证上下文）"
        assert runtime_resource.token == "test_auth_code", "Token应该是 auth_code"
        
        print(f"   ✓ 验证通过：没有认证上下文时使用默认配置")
        
        return True
        
    except Exception as e:
        print(f"\n✗ 没有认证上下文测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_build_runtime_resource_user_service_error():
    """测试用户服务异常时的运行时资源构建"""
    print("\n=== 测试用户服务异常的运行时资源构建 ===")
    
    try:
        # 创建 SessionService 实例
        session_service = SessionService()
        
        # 创建模拟的认证上下文
        mock_context = Mock(spec=AuthContext)
        mock_context.user_key = "test_user"
        mock_context.end_user_id = 12345
        
        # 模拟 user_service.get_user_environments 抛出异常
        with patch('src.domain.kms.user_service.user_service') as mock_user_service:
            mock_user_service.get_user_environments.side_effect = Exception("网络连接失败")
            
            # 测试用户服务异常的情况
            print("\n1. 测试用户服务异常...")
            
            runtime_resource = session_service._build_runtime_resource(
                desktop_id="desktop_001",
                auth_code="test_auth_code",
                auth_context=mock_context
            )
            
            print(f"   ✓ 类型: {runtime_resource.type}")
            print(f"   ✓ 云资源ID: {runtime_resource.cloud_resource_id}")
            print(f"   ✓ 区域: {runtime_resource.region}")
            print(f"   ✓ Token: {runtime_resource.token}")
            
            # 验证结果
            assert runtime_resource.type == "desktop", "类型应该是 desktop"
            assert runtime_resource.cloud_resource_id == "desktop_001", "云资源ID应该是 desktop_001"
            assert runtime_resource.region is None, "区域应该是 None（因为用户服务异常）"
            assert runtime_resource.token == "test_auth_code", "Token应该是 auth_code"
            
            print(f"   ✓ 验证通过：用户服务异常时使用默认配置")
        
        return True
        
    except Exception as e:
        print(f"\n✗ 用户服务异常测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_special_desktop_ids():
    """测试特殊桌面ID的处理"""
    print("\n=== 测试特殊桌面ID的处理 ===")
    
    try:
        # 创建 SessionService 实例
        session_service = SessionService()
        
        # 创建模拟的认证上下文
        mock_context = Mock(spec=AuthContext)
        mock_context.user_key = "test_user"
        mock_context.end_user_id = 12345
        
        # 测试特殊情况
        test_cases = [
            {"desktop_id": "Agentbay", "expected_type": "agentbay", "description": "AgentBay（大写）"},
            {"desktop_id": "agentbay", "expected_type": "agentbay", "description": "agentbay（小写）"},
            {"desktop_id": "AGENTBAY", "expected_type": "agentbay", "description": "AGENTBAY（全大写）"},
            {"desktop_id": None, "expected_type": "none", "description": "None"},
            {"desktop_id": "", "expected_type": "none", "description": "空字符串"},
        ]
        
        for case in test_cases:
            desktop_id = case["desktop_id"]
            expected_type = case["expected_type"]
            description = case["description"]
            
            print(f"\n{description}...")
            
            runtime_resource = session_service._build_runtime_resource(
                desktop_id=desktop_id,
                auth_code="test_auth_code",
                auth_context=mock_context
            )
            
            print(f"   ✓ 类型: {runtime_resource.type}")
            print(f"   ✓ 云资源ID: {runtime_resource.cloud_resource_id}")
            print(f"   ✓ 区域: {runtime_resource.region}")
            
            # 验证结果
            assert runtime_resource.type == expected_type, f"类型应该是 {expected_type}，实际是 {runtime_resource.type}"
            
            if expected_type == "agentbay":
                assert runtime_resource.cloud_resource_id == "akm-2f6c64e0-ba79-47cb-9847-d265773c7873", "AgentBay 云资源ID不正确"
            elif expected_type == "none":
                assert runtime_resource.cloud_resource_id is None, "None 类型的云资源ID应该是 None"
            
            print(f"   ✓ 验证通过")
        
        return True
        
    except Exception as e:
        print(f"\n✗ 特殊桌面ID测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("开始测试桌面验证和 region_id 获取功能...\n")
    
    test_functions = [
        test_build_runtime_resource_with_valid_desktop,
        test_build_runtime_resource_with_invalid_desktop,
        test_build_runtime_resource_without_auth_context,
        test_build_runtime_resource_user_service_error,
        test_special_desktop_ids,
    ]
    
    results = []
    
    for test_func in test_functions:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"测试函数 {test_func.__name__} 执行异常: {e}")
            results.append(False)
    
    # 汇总结果
    passed = sum(results)
    total = len(results)
    
    print(f"\n=== 最终测试结果 ===")
    print(f"通过: {passed}/{total}")
    print(f"失败: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！桌面验证和 region_id 获取功能正常。")
        print("\n✅ 功能实现总结:")
        print("   1. 有效桌面ID能正确获取 region_id")
        print("   2. 无效桌面ID会抛出 ValueError 异常")
        print("   3. 没有认证上下文时使用默认配置")
        print("   4. 用户服务异常时使用默认配置")
        print("   5. 特殊桌面ID（AgentBay、None、空字符串）正确处理")
        return True
    else:
        print("❌ 部分测试失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
