#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 ARTIFACT 事件对完成事件判断的影响
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock

from src.domain.services.message_processor import MessageProcessor


class TestArtifactFinishEventLogic:
    """测试 ARTIFACT 事件对完成事件判断的影响"""

    @pytest.fixture
    def message_processor(self):
        """创建 MessageProcessor 实例"""
        processor = MessageProcessor()
        
        # 模拟 SSE 管理器
        mock_sse_manager = Mock()
        mock_sse_manager.sse_connections = {}
        mock_sse_manager.push_to_sse = AsyncMock()
        processor.set_sse_manager(mock_sse_manager)
        
        # 模拟 Session 管理器
        mock_session_manager = Mock()
        processor.set_session_manager(mock_session_manager)
        
        return processor

    @pytest.fixture
    def mock_finish_event(self):
        """创建模拟的完成事件"""
        mock_event = Mock()
        mock_event.session_id = "test_session"
        mock_event.run_id = "test_run"
        mock_event.type = Mock()
        mock_event.type.value = "run_finished"
        return mock_event

    @pytest.fixture
    def mock_artifact_event(self):
        """创建模拟的 ARTIFACT 事件"""
        mock_event = Mock()
        mock_event.session_id = "test_session"
        mock_event.run_id = "test_run"
        mock_event.type = Mock()
        mock_event.type.value = "artifact"
        return mock_event

    @pytest.fixture
    def mock_message_event(self):
        """创建模拟的普通消息事件"""
        mock_event = Mock()
        mock_event.session_id = "test_session"
        mock_event.run_id = "test_run"
        mock_event.type = Mock()
        mock_event.type.value = "message"
        return mock_event

    @pytest.mark.asyncio
    async def test_finish_event_after_artifact_should_not_finish(self, message_processor, mock_finish_event, mock_artifact_event):
        """测试：如果前一个事件是 ARTIFACT，完成事件不应该生效"""
        
        # 模拟 memory_sdk 返回事件列表：最新的是完成事件，前一个是 ARTIFACT 事件
        mock_events_result = {
            "events": [mock_finish_event, mock_artifact_event],  # [最新, 倒数第二]
            "has_more": False
        }
        
        with patch('src.infrastructure.memory.memory_sdk.memory_sdk') as mock_memory_sdk:
            mock_memory_sdk.get_events.return_value = mock_events_result
            
            # 调用判断方法
            result = await message_processor._is_session_finished_event_with_context("test_session", mock_finish_event)
            
            # 验证结果：应该返回 False（不是完成事件）
            assert result is False
            
            # 验证调用了 memory_sdk.get_events
            mock_memory_sdk.get_events.assert_called_once_with(session_id="test_session", limit=2)

    @pytest.mark.asyncio
    async def test_finish_event_after_message_should_finish(self, message_processor, mock_finish_event, mock_message_event):
        """测试：如果前一个事件是普通消息，完成事件应该生效"""
        
        # 模拟 memory_sdk 返回事件列表：最新的是完成事件，前一个是普通消息事件
        mock_events_result = {
            "events": [mock_finish_event, mock_message_event],  # [最新, 倒数第二]
            "has_more": False
        }
        
        with patch('src.infrastructure.memory.memory_sdk.memory_sdk') as mock_memory_sdk:
            mock_memory_sdk.get_events.return_value = mock_events_result

            # 调用判断方法
            result = await message_processor._is_session_finished_event_with_context("test_session", mock_finish_event)

            # 验证结果：应该返回 True（是完成事件）
            assert result is True

            # 验证调用了 memory_sdk.get_events
            mock_memory_sdk.get_events.assert_called_once_with(session_id="test_session", limit=2)

    @pytest.mark.asyncio
    async def test_finish_event_without_previous_should_finish(self, message_processor, mock_finish_event):
        """测试：如果没有前一个事件，完成事件应该生效"""

        # 模拟 memory_sdk 返回事件列表：只有一个完成事件
        mock_events_result = {
            "events": [mock_finish_event],  # 只有一个事件
            "has_more": False
        }

        with patch('src.infrastructure.memory.memory_sdk.memory_sdk') as mock_memory_sdk:
            mock_memory_sdk.get_events.return_value = mock_events_result
            
            # 调用判断方法
            result = await message_processor._is_session_finished_event_with_context("test_session", mock_finish_event)
            
            # 验证结果：应该返回 True（是完成事件）
            assert result is True

    @pytest.mark.asyncio
    async def test_non_finish_event_should_not_finish(self, message_processor, mock_message_event):
        """测试：非完成类型的事件不应该被认为是完成事件"""
        
        # 调用判断方法
        result = await message_processor._is_session_finished_event_with_context("test_session", mock_message_event)
        
        # 验证结果：应该返回 False（不是完成事件）
        assert result is False

    @pytest.mark.asyncio
    async def test_get_previous_event_with_multiple_events(self, message_processor, mock_finish_event, mock_artifact_event):
        """测试获取前一个事件的方法"""
        
        # 模拟 memory_sdk 返回事件列表
        mock_events_result = {
            "events": [mock_finish_event, mock_artifact_event],  # [最新, 倒数第二]
            "has_more": False
        }
        
        with patch('src.infrastructure.memory.memory_sdk.memory_sdk') as mock_memory_sdk:
            mock_memory_sdk.get_events.return_value = mock_events_result
            
            # 调用获取前一个事件的方法
            previous_event = await message_processor._get_previous_event("test_session")
            
            # 验证结果：应该返回倒数第二个事件
            assert previous_event == mock_artifact_event
            
            # 验证调用了 memory_sdk.get_events
            mock_memory_sdk.get_events.assert_called_once_with(session_id="test_session", limit=2)

    @pytest.mark.asyncio
    async def test_get_previous_event_with_single_event(self, message_processor, mock_finish_event):
        """测试只有一个事件时获取前一个事件"""
        
        # 模拟 memory_sdk 返回事件列表：只有一个事件
        mock_events_result = {
            "events": [mock_finish_event],  # 只有一个事件
            "has_more": False
        }
        
        with patch('src.infrastructure.memory.memory_sdk.memory_sdk') as mock_memory_sdk:
            mock_memory_sdk.get_events.return_value = mock_events_result

            # 调用获取前一个事件的方法
            previous_event = await message_processor._get_previous_event("test_session")

            # 验证结果：应该返回 None
            assert previous_event is None

    @pytest.mark.asyncio
    async def test_get_previous_event_with_no_events(self, message_processor):
        """测试没有事件时获取前一个事件"""

        # 模拟 memory_sdk 返回空事件列表
        mock_events_result = {
            "events": [],
            "has_more": False
        }

        with patch('src.infrastructure.memory.memory_sdk.memory_sdk') as mock_memory_sdk:
            mock_memory_sdk.get_events.return_value = mock_events_result

            # 调用获取前一个事件的方法
            previous_event = await message_processor._get_previous_event("test_session")

            # 验证结果：应该返回 None
            assert previous_event is None

    @pytest.mark.asyncio
    async def test_memory_sdk_error_handling(self, message_processor, mock_finish_event):
        """测试 memory_sdk 异常处理"""

        with patch('src.infrastructure.memory.memory_sdk.memory_sdk') as mock_memory_sdk:
            mock_memory_sdk.get_events.side_effect = Exception("Memory SDK 连接失败")
            
            # 调用判断方法（不应该抛出异常）
            result = await message_processor._is_session_finished_event_with_context("test_session", mock_finish_event)
            
            # 验证结果：异常时应该返回 False
            assert result is False

    def test_event_type_values(self):
        """测试事件类型值的正确性"""
        from memory.events import EventType
        
        # 验证事件类型值
        run_finished_value = EventType.RUN_FINISHED.value if hasattr(EventType.RUN_FINISHED, 'value') else str(EventType.RUN_FINISHED)
        run_error_value = EventType.RUN_ERROR.value if hasattr(EventType.RUN_ERROR, 'value') else str(EventType.RUN_ERROR)
        run_stopped_value = EventType.RUN_STOPPED.value if hasattr(EventType.RUN_STOPPED, 'value') else str(EventType.RUN_STOPPED)
        artifact_value = EventType.ARTIFACT.value if hasattr(EventType.ARTIFACT, 'value') else str(EventType.ARTIFACT)
        
        # 验证完成类型事件
        finish_types = [run_finished_value, run_error_value, run_stopped_value]
        assert len(finish_types) == 3
        assert artifact_value not in finish_types
        
        print(f"✅ 完成事件类型: {finish_types}")
        print(f"✅ ARTIFACT 事件类型: {artifact_value}")


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
