#!/usr/bin/env python3
"""
测试会话标题更新修复
"""

import os


def test_update_session_title_async_fix():
    """测试_update_session_title_async方法修复"""
    print("🧪 测试 _update_session_title_async 方法修复")
    print("=" * 50)
    
    try:
        # 读取session_service.py文件
        service_file = os.path.join(os.path.dirname(__file__), '..', 'src', 'domain', 'kms', 'session_service.py')
        
        with open(service_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查修复的关键代码
        fix_checks = [
            ('await self.session_db_service.update_session_title_async(session_id, title)', "使用正确的异步方法调用"),
            ('async def _update_session_title_async(self, session_id: str, title: str):', "方法定义正确"),
            ('logger.info(f"[SessionService] 会话标题已更新:', "成功日志"),
            ('logger.error(f"[SessionService] 更新会话标题失败:', "错误日志")
        ]
        
        print("📝 修复检查:")
        all_passed = True
        
        for check, desc in fix_checks:
            if check in content:
                print(f"   ✅ {desc}")
            else:
                print(f"   ❌ {desc}")
                all_passed = False
        
        # 检查是否移除了错误的调用
        error_checks = [
            ('self.session_repository.update_session_title', "移除了错误的session_repository调用"),
            ('self.session_db_service.update_session_title(', "移除了同步方法调用")
        ]
        
        print("\n📝 错误代码移除检查:")
        for check, desc in error_checks:
            if check not in content:
                print(f"   ✅ {desc}")
            else:
                print(f"   ❌ {desc} - 仍然存在")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_session_service_attributes():
    """测试SessionService的属性"""
    print("\n🧪 测试SessionService的属性")
    print("=" * 50)
    
    try:
        # 读取session_service.py文件
        service_file = os.path.join(os.path.dirname(__file__), '..', 'src', 'domain', 'kms', 'session_service.py')
        
        with open(service_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找SessionService的__init__方法
        init_start = content.find('def __init__(self):')
        if init_start == -1:
            print("❌ 未找到SessionService的__init__方法")
            return False
        
        # 找到__init__方法的结束位置
        init_end = content.find('def ', init_start + 1)
        if init_end == -1:
            init_end = len(content)
        
        init_method = content[init_start:init_end]
        
        # 检查正确的属性初始化
        attribute_checks = [
            ('self.session_db_service = session_db_service', "session_db_service属性"),
            ('self.memory_sdk = memory_sdk', "memory_sdk属性"),
            ('self.sse_stream_manager = SSEManager()', "sse_stream_manager属性"),
            ('self.message_processor = MessageProcessor()', "message_processor属性")
        ]
        
        print("📝 SessionService属性检查:")
        all_passed = True
        
        for check, desc in attribute_checks:
            if check in init_method:
                print(f"   ✅ {desc}")
            else:
                print(f"   ❌ {desc}")
                all_passed = False
        
        # 检查是否有错误的属性
        error_attributes = [
            'self.session_repository',
            'self.session_repo'
        ]
        
        print("\n📝 错误属性检查:")
        for attr in error_attributes:
            if attr not in init_method:
                print(f"   ✅ 没有错误的属性: {attr}")
            else:
                print(f"   ❌ 仍有错误的属性: {attr}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 属性测试失败: {e}")
        return False


def test_other_title_update_methods():
    """测试其他标题更新方法的一致性"""
    print("\n🧪 测试其他标题更新方法的一致性")
    print("=" * 50)
    
    try:
        # 读取session_service.py文件
        service_file = os.path.join(os.path.dirname(__file__), '..', 'src', 'domain', 'kms', 'session_service.py')
        
        with open(service_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查其他地方的标题更新调用是否一致
        consistency_checks = [
            # 在rename_session方法中
            ('await self.session_db_service.update_session_title_async(session_id, new_title)', "rename_session中的调用"),
            # 在_generate_session_title_async方法中
            ('success = await self.session_db_service.update_session_title_async(session_id, title)', "_generate_session_title_async中的调用")
        ]
        
        print("📝 一致性检查:")
        all_passed = True
        
        for check, desc in consistency_checks:
            if check in content:
                print(f"   ✅ {desc}")
            else:
                print(f"   ❌ {desc}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 一致性测试失败: {e}")
        return False


def test_method_usage_pattern():
    """测试方法使用模式"""
    print("\n🧪 测试方法使用模式")
    print("=" * 50)
    
    try:
        # 读取session_service.py文件
        service_file = os.path.join(os.path.dirname(__file__), '..', 'src', 'domain', 'kms', 'session_service.py')
        
        with open(service_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找_update_session_title_async方法的调用
        usage_pattern = 'await self._update_session_title_async(session.session_id, temp_title)'
        
        if usage_pattern in content:
            print("✅ _update_session_title_async方法被正确调用")
            
            # 检查调用上下文
            call_start = content.find(usage_pattern)
            context_start = max(0, call_start - 200)
            context_end = min(len(content), call_start + 200)
            context = content[context_start:context_end]
            
            # 检查是否在正确的位置调用
            if 'temp_title = self._generate_temp_title_from_prompt(prompt)' in context:
                print("✅ 在生成临时标题后正确调用")
            else:
                print("❌ 调用位置可能不正确")
                return False
                
            if 'asyncio.create_task(self._generate_session_title_async' in context:
                print("✅ 在异步生成正式标题之前调用")
            else:
                print("❌ 与异步标题生成的顺序可能不正确")
                return False
                
        else:
            print("❌ _update_session_title_async方法未被调用")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 使用模式测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试会话标题更新修复")
    
    tests = [
        test_update_session_title_async_fix,
        test_session_service_attributes,
        test_other_title_update_methods,
        test_method_usage_pattern
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试 {test.__name__} 异常: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print("✅ 所有测试通过！会话标题更新修复成功")
    else:
        print(f"❌ {total - passed} 个测试失败，{passed} 个测试通过")
    
    print(f"\n📊 测试结果: {passed}/{total}")
    
    print("\n📋 修复总结:")
    print("1. ✅ 修复了属性名错误：session_repository → session_db_service")
    print("2. ✅ 修复了方法调用错误：同步方法 → 异步方法")
    print("3. ✅ 保持了与其他方法的一致性")
    print("4. ✅ 确保了正确的await调用")
    
    print("\n🔧 修复详情:")
    print("- 错误代码: self.session_repository.update_session_title(session_id, title)")
    print("- 正确代码: await self.session_db_service.update_session_title_async(session_id, title)")
    
    print("\n🎯 修复原因:")
    print("- SessionService类中的属性是session_db_service，不是session_repository")
    print("- 需要使用异步方法update_session_title_async，并添加await关键字")
    print("- 保持与rename_session和_generate_session_title_async方法的一致性")
    
    print("\n✅ 现在临时标题更新功能应该可以正常工作了！")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
