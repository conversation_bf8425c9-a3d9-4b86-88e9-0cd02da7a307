#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试会话列表API的多Agent ID过滤功能集成测试
"""

import pytest
import json
from typing import List
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from fastapi import Query

# 导入需要测试的模块
from src.presentation.api.routes.session_routes import router
from src.domain.services.auth_service import AuthContext
from src.domain.services.session_service import SessionListResult, SessionInfo
from src.infrastructure.database.models.session_models import SessionModel


class TestSessionListAPIIntegration:
    """测试会话列表API的集成功能"""

    def test_api_parameter_parsing_single_agent_id(self):
        """测试API参数解析：单个Agent ID"""
        # 模拟FastAPI的Query参数解析
        # 当传入单个agent_id时，FastAPI会将其解析为包含一个元素的列表
        
        # 模拟URL: /api/sessions/list?agent_id=agent_123
        agent_id_param = ["agent_123"]  # FastAPI会将单个值包装成列表
        
        assert isinstance(agent_id_param, list)
        assert len(agent_id_param) == 1
        assert agent_id_param[0] == "agent_123"

    def test_api_parameter_parsing_multiple_agent_ids(self):
        """测试API参数解析：多个Agent ID"""
        # 模拟FastAPI的Query参数解析
        # 当传入多个agent_id时，FastAPI会将其解析为列表
        
        # 模拟URL: /api/sessions/list?agent_id=agent_123&agent_id=agent_456&agent_id=agent_789
        agent_id_param = ["agent_123", "agent_456", "agent_789"]
        
        assert isinstance(agent_id_param, list)
        assert len(agent_id_param) == 3
        assert "agent_123" in agent_id_param
        assert "agent_456" in agent_id_param
        assert "agent_789" in agent_id_param

    def test_api_parameter_parsing_none_agent_id(self):
        """测试API参数解析：无Agent ID"""
        # 模拟FastAPI的Query参数解析
        # 当没有传入agent_id时，FastAPI会将其设置为None
        
        # 模拟URL: /api/sessions/list
        agent_id_param = None
        
        assert agent_id_param is None

    @pytest.mark.asyncio
    async def test_session_service_integration_with_multiple_agent_ids(self):
        """测试会话服务与多个Agent ID的集成"""
        from src.domain.services.session_service import session_service, SessionListParams
        
        # 模拟认证上下文
        mock_context = Mock(spec=AuthContext)
        mock_context.ali_uid = "123456789"
        mock_context.user_key = "test_user"
        
        # 创建查询参数
        params = SessionListParams(
            page_size=20,
            agent_id=["agent_123", "agent_456"]
        )
        
        # 模拟数据库返回结果
        mock_sessions = [
            SessionModel(
                id=1,
                session_id="session_1",
                ali_uid="123456789",
                agent_id="agent_123",
                title="Test Session 1",
                status="ACTIVE"
            ),
            SessionModel(
                id=2,
                session_id="session_2",
                ali_uid="123456789",
                agent_id="agent_456",
                title="Test Session 2",
                status="ACTIVE"
            )
        ]
        
        # 模拟会话服务的数据库调用
        with patch.object(session_service, 'session_db_service') as mock_db_service:
            mock_db_service.list_sessions_async = AsyncMock(return_value=mock_sessions)
            mock_db_service.count_sessions_async = AsyncMock(return_value=2)
            
            # 模拟知识库服务
            with patch('src.domain.kms.session_service.knowledgebase_service') as mock_kb_service:
                mock_kb_service.is_knowledge_base_session.return_value = {}
                
                # 调用会话服务
                result = await session_service.get_user_sessions(mock_context, params)
                
                # 验证结果
                assert isinstance(result, SessionListResult)
                assert len(result.sessions) == 2
                assert result.total_count == 2
                
                # 验证数据库服务被正确调用
                mock_db_service.list_sessions_async.assert_called_once_with(
                    limit=21,  # page_size + 1
                    ali_uid="123456789",
                    agent_id=["agent_123", "agent_456"],
                    next_token=None,
                    search_keyword=None,
                    status_filter="!DELETED"
                )
                
                mock_db_service.count_sessions_async.assert_called_once_with(
                    ali_uid="123456789",
                    agent_id=["agent_123", "agent_456"],
                    search_keyword=None,
                    status_filter="!DELETE"
                )

    def test_backward_compatibility_with_existing_code(self):
        """测试向后兼容性：确保现有代码仍然工作"""
        from src.domain.services.session_service import SessionListParams
        
        # 测试1：传入None（现有代码的默认行为）
        params1 = SessionListParams(agent_id=None)
        assert params1.agent_id is None
        
        # 测试2：传入单个字符串（可能的现有用法）
        params2 = SessionListParams()
        params2.agent_id = "single_agent"  # 直接赋值字符串
        assert params2.agent_id == "single_agent"
        
        # 测试3：传入列表（新的用法）
        params3 = SessionListParams(agent_id=["agent_1", "agent_2"])
        assert params3.agent_id == ["agent_1", "agent_2"]

    def test_database_query_logic_compatibility(self):
        """测试数据库查询逻辑的兼容性"""
        # 这个测试验证我们的数据库查询逻辑能够处理不同类型的agent_id参数
        
        # 测试数据
        test_cases = [
            {
                "name": "None agent_id",
                "agent_id": None,
                "expected_filter_called": False
            },
            {
                "name": "Empty list agent_id",
                "agent_id": [],
                "expected_filter_called": False
            },
            {
                "name": "Single string agent_id (backward compatibility)",
                "agent_id": "agent_123",
                "expected_filter_called": True,
                "expected_filter_type": "equals"
            },
            {
                "name": "Single item list agent_id",
                "agent_id": ["agent_123"],
                "expected_filter_called": True,
                "expected_filter_type": "in"
            },
            {
                "name": "Multiple items list agent_id",
                "agent_id": ["agent_123", "agent_456"],
                "expected_filter_called": True,
                "expected_filter_type": "in"
            }
        ]
        
        for case in test_cases:
            # 模拟数据库查询逻辑
            agent_id = case["agent_id"]
            filter_called = False
            filter_type = None
            
            # 复制我们在数据库层实现的逻辑
            if agent_id:
                if isinstance(agent_id, list) and len(agent_id) > 0:
                    filter_called = True
                    filter_type = "in"
                elif isinstance(agent_id, str):
                    filter_called = True
                    filter_type = "equals"
            
            # 验证结果
            assert filter_called == case["expected_filter_called"], f"Failed for case: {case['name']}"
            if case.get("expected_filter_type"):
                assert filter_type == case["expected_filter_type"], f"Failed for case: {case['name']}"

    def test_api_response_format(self):
        """测试API响应格式保持一致"""
        # 这个测试确保API响应格式没有因为参数类型变化而改变
        
        # 模拟API响应数据结构
        mock_response_data = {
            "code": "200",
            "success": True,
            "total_count": 2,
            "next_token": None,
            "data": {
                "sessions": [
                    {
                        "session_id": "session_1",
                        "agent_id": "agent_123",
                        "title": "Test Session 1",
                        "status": "ACTIVE"
                    },
                    {
                        "session_id": "session_2", 
                        "agent_id": "agent_456",
                        "title": "Test Session 2",
                        "status": "ACTIVE"
                    }
                ],
                "page_size": 20,
                "total_count": 2,
                "next_token": None,
                "has_more": False
            },
            "request_id": "test_request_id"
        }
        
        # 验证响应结构
        assert "code" in mock_response_data
        assert "success" in mock_response_data
        assert "data" in mock_response_data
        assert "sessions" in mock_response_data["data"]
        
        # 验证会话数据结构
        sessions = mock_response_data["data"]["sessions"]
        for session in sessions:
            assert "session_id" in session
            assert "agent_id" in session
            assert "title" in session
            assert "status" in session


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
