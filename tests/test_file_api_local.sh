curl -X POST "http://localhost:8000/api/files/list?LoginToken=mock_login_token&RegionId=mock_region_id" \
    -H "Content-Type: application/json" \
    -d '{
            "session_id":"sess_ea8056f7566848c39272d40e0fb15962", 
            "artifact_types": ["sessionFile", "artifactFile"], 
            "max_results": 10
    }'


curl -X POST "http://localhost:8000/api/files/list?LoginToken=mock_login_token&RegionId=mock_region_id" \
    -H "Content-Type: application/json" \
    -d '{
            "session_id":"sess_ea8056f7566848c39272d40e0fb15962", 
            "artifact_types": ["artifactFile"],  
            "max_results": 10,
            "LoginToken": "mock_login_token",
            "RegionId": "mock_region_id"
    }'


curl -X POST "http://localhost:8000/api/files/list?LoginToken=mock_login_token&RegionId=mock_region_id" \
    -H "Content-Type: application/json" \
    -d '{
            "SessionId": "test_session_123", 
            "ArtifactTypes": ["resultArtifact", "processArtifact", "sessionFile"],  
            "MaxResults": 100
    }'


# 获取文件下载链接 - 单个文件
curl -X POST "http://localhost:8000/api/files/download-urls?LoginToken=mock_login_token&RegionId=mock_region_id" \
    -H "Content-Type: application/json" \
    -d '{
            "session_id": "sess_716e9feaa5bf4e9394db03e2d766f8ef",
            "artifact_ids": ["artifact-b2f67052872b43ccb3c9710831f67f04"],
            "LoginToken": "mock_login_token",
            "RegionId": "mock_region_id"
    }'


curl -X POST "http://localhost:8000/api/files/download-urls?LoginToken=mock_login_token&RegionId=mock_region_id" \
    -H "Content-Type: application/json" \
    -d '{
            "session_id": "sess_78c4c7148c1b4378901145aa3feb9560",
            "artifact_ids": ["artifact-420f87ad047d43188a400fc15b5c5189"],
            "LoginToken": "mock_login_token",
            "RegionId": "mock_region_id"
    }'


curl -X POST "http://localhost:8000/api/files/download-urls?LoginToken=mock_login_token&RegionId=mock_region_id" \
    -H "Content-Type: application/json" \
    -d '{
            "session_id": "sess_3bab7a5872fb4028bb5362398ab90b97",
            "artifact_ids": ["artifact-6d6f5ebbeb494b3d9dc0f2f1bfdc635e"],
            "LoginToken": "mock_login_token",
            "RegionId": "mock_region_id"
    }'


curl -X POST "http://localhost:8000/api/files/get-artifact-preview?LoginToken=mock_login_token&RegionId=mock_region_id" \
    -H "Content-Type: application/json" \
    -d '{
            "artifact_id": "artifact-6ad0d8a91efc40319a13f670c4a0e6ac",
            "LoginToken": "mock_login_token",
            "RegionId": "mock_region_id"
    }'


# 检查文件是否存在 - 测试存在的文件
curl -X POST "http://localhost:8000/api/files/is-artifact-existed?LoginToken=mock_login_token&RegionId=mock_region_id" \
    -H "Content-Type: application/json" \
    -d '{
            "artifact_id": "artifact-b2f67052872b43ccb3c9710831f67f04"
    }'


# 检查文件是否存在 - 测试不存在的文件
curl -X POST "http://localhost:8000/api/files/is-artifact-existed?LoginToken=mock_login_token&RegionId=mock_region_id" \
    -H "Content-Type: application/json" \
    -d '{
            "artifact_id": "non-existent-artifact-123"
    }'


# 批量删除文件 - 正常删除多个文件
curl -X POST "http://localhost:8000/api/files/batch-delete?loginToken=mock_login_token&regionId=mock_region_id" \
    -H "Content-Type: application/json" \
    -d '{
            "artifact_ids": ["artifact-8a1883dc71a74e19bec4bea92262a1f7"],
            "LoginToken": "mock_login_token",
            "RegionId": "mock_region_id"
    }'


# 批量删除文件 - 删除单个文件
curl -X POST "http://localhost:8000/api/files/batch-delete" \
    -H "Content-Type: application/json" \
    -d '{
            "ArtifactIds": ["artifact-52b15da6666f4ee6a21ada644747460f"],
            "DeleteFromOss": true,
            "LoginToken": "mock_login_token",
            "RegionId": "mock_region_id"
    }'



curl -X POST "http://localhost:8000/api/files/sync-to-storage?LoginToken=mock_login_token&RegionId=mock_region_id" \
    -H "Content-Type: application/json" \
    -d '{
            "session_id": "sess_0359d3c4f6b24d17aedecbeaf9b0062e",
            "artifact_ids": ["artifact-9418a2824d0b4262ba510a8e78e1966e"]
    }'



curl -X POST "http://localhost:8000/api/files/sync-task-status?LoginToken=mock_login_token&RegionId=mock_region_id" \
    -H "Content-Type: application/json" \
    -d '{
            "task_id": "sync-db76cb8497414f59"
    }'
