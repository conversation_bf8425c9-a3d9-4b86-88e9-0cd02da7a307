#!/usr/bin/env python3
"""
测试 terminate_request app_id 设置修复
"""

import os


def test_syntax_fix():
    """测试语法修复"""
    print("🧪 测试语法修复")
    print("=" * 50)
    
    try:
        # 读取session_service.py文件
        service_file = os.path.join(os.path.dirname(__file__), '..', 'src', 'domain', 'kms', 'session_service.py')
        
        with open(service_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查语法修复
        syntax_checks = [
            ('session_model = await self.get_session_with_permission_check_async(', "正确的赋值语法"),
            ('logger.info(f"[SessionService] 会话权限验证通过: session_id={session_id}")', "权限验证成功日志"),
            ('except Exception as perm_error:', "权限异常处理"),
            ('logger.error(f"[SessionService] 会话权限验证失败:', "权限验证失败日志"),
            ('raise', "权限异常重新抛出")
        ]
        
        print("📝 语法修复检查:")
        all_passed = True
        
        for check, desc in syntax_checks:
            if check in content:
                print(f"   ✅ {desc}")
            else:
                print(f"   ❌ {desc}")
                all_passed = False
        
        # 检查是否移除了错误的语法
        error_syntax_checks = [
            ('await session_model =', "移除了错误的await赋值语法"),
            ('session_model = None', "移除了多余的初始化")
        ]
        
        print("\n📝 错误语法移除检查:")
        for check, desc in error_syntax_checks:
            if check not in content:
                print(f"   ✅ {desc}")
            else:
                print(f"   ❌ {desc} - 仍然存在")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 语法修复测试失败: {e}")
        return False


def test_app_id_setting():
    """测试app_id设置"""
    print("\n🧪 测试app_id设置")
    print("=" * 50)
    
    try:
        # 读取session_service.py文件
        service_file = os.path.join(os.path.dirname(__file__), '..', 'src', 'domain', 'kms', 'session_service.py')
        
        with open(service_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查app_id设置
        app_id_checks = [
            ('terminate_request = waiy_models.TerminateAsyncRequest()', "创建终止请求对象"),
            ('terminate_request.run_id = run_id', "设置run_id"),
            ('terminate_request.app_id = session_model.agent_id', "设置app_id为agent_id")
        ]
        
        print("📝 app_id设置检查:")
        all_passed = True
        
        for check, desc in app_id_checks:
            if check in content:
                print(f"   ✅ {desc}")
            else:
                print(f"   ❌ {desc}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ app_id设置测试失败: {e}")
        return False


def test_session_model_usage():
    """测试session_model使用"""
    print("\n🧪 测试session_model使用")
    print("=" * 50)
    
    try:
        # 读取session_service.py文件
        service_file = os.path.join(os.path.dirname(__file__), '..', 'src', 'domain', 'kms', 'session_service.py')
        
        with open(service_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找terminate_session方法
        method_start = content.find('async def terminate_session(')
        if method_start == -1:
            print("❌ 未找到terminate_session方法")
            return False
        
        method_end = content.find('async def ', method_start + 1)
        if method_end == -1:
            method_end = len(content)
        
        method_content = content[method_start:method_end]
        
        # 检查session_model的使用流程
        usage_checks = [
            # 权限验证获取session_model
            ('session_model = await self.get_session_with_permission_check_async(', "获取session_model"),
            
            # 使用session_model设置app_id
            ('terminate_request.app_id = session_model.agent_id', "使用session_model.agent_id"),
            
            # 确保在正确的位置使用
            ('terminate_request.run_id = run_id', "设置run_id在app_id之前")
        ]
        
        print("📝 session_model使用检查:")
        all_passed = True
        
        for check, desc in usage_checks:
            if check in method_content:
                print(f"   ✅ {desc}")
            else:
                print(f"   ❌ {desc}")
                all_passed = False
        
        # 检查使用顺序
        run_id_pos = method_content.find('terminate_request.run_id = run_id')
        app_id_pos = method_content.find('terminate_request.app_id = session_model.agent_id')
        
        if run_id_pos != -1 and app_id_pos != -1:
            if run_id_pos < app_id_pos:
                print("   ✅ 设置顺序正确：先run_id，后app_id")
            else:
                print("   ❌ 设置顺序错误")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ session_model使用测试失败: {e}")
        return False


def test_complete_flow():
    """测试完整流程"""
    print("\n🧪 测试完整流程")
    print("=" * 50)
    
    try:
        # 读取session_service.py文件
        service_file = os.path.join(os.path.dirname(__file__), '..', 'src', 'domain', 'kms', 'session_service.py')
        
        with open(service_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找terminate_session方法
        method_start = content.find('async def terminate_session(')
        method_end = content.find('async def ', method_start + 1)
        if method_end == -1:
            method_end = len(content)
        
        method_content = content[method_start:method_end]
        
        # 检查完整的执行流程
        flow_checks = [
            # 步骤1：权限验证
            ('# 1. 权限验证', "步骤1注释"),
            ('session_model = await self.get_session_with_permission_check_async(', "权限验证获取session_model"),
            
            # 步骤2：获取事件
            ('# 2. 获取会话事件，提取run_id', "步骤2注释"),
            ('events_result = await self.get_raw_events(session_id)', "获取事件"),
            
            # 步骤3：终止异步任务
            ('# 3. 终止异步任务', "步骤3注释"),
            ('terminate_request = waiy_models.TerminateAsyncRequest()', "创建终止请求"),
            ('terminate_request.run_id = run_id', "设置run_id"),
            ('terminate_request.app_id = session_model.agent_id', "设置app_id"),
            ('waiy_client.terminate_async(terminate_request)', "调用终止方法"),
            
            # 步骤4：关闭SSE连接
            ('# 4. 关闭SSE连接', "步骤4注释"),
            ('self.sse_stream_manager.close_session_connection(session_id)', "关闭SSE连接")
        ]
        
        print("📝 完整流程检查:")
        all_passed = True
        
        for check, desc in flow_checks:
            if check in method_content:
                print(f"   ✅ {desc}")
            else:
                print(f"   ❌ {desc}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 完整流程测试失败: {e}")
        return False


def test_error_handling():
    """测试错误处理"""
    print("\n🧪 测试错误处理")
    print("=" * 50)
    
    try:
        # 读取session_service.py文件
        service_file = os.path.join(os.path.dirname(__file__), '..', 'src', 'domain', 'kms', 'session_service.py')
        
        with open(service_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查错误处理
        error_handling_checks = [
            # 权限验证异常处理
            ('except Exception as perm_error:', "权限验证异常捕获"),
            ('logger.error(f"[SessionService] 会话权限验证失败:', "权限验证失败日志"),
            ('raise', "权限异常重新抛出"),
            
            # 终止任务异常处理
            ('except WaiyInfraClientError as client_error:', "客户端异常捕获"),
            ('except Exception as terminate_error:', "终止异常捕获"),
            
            # SSE关闭异常处理
            ('except Exception as close_error:', "SSE关闭异常捕获"),
            
            # 最终异常处理
            ('except Exception as e:', "最终异常捕获"),
            ('logger.error(f"[SessionService] 终止会话失败:', "最终异常日志")
        ]
        
        print("📝 错误处理检查:")
        all_passed = True
        
        for check, desc in error_handling_checks:
            if check in content:
                print(f"   ✅ {desc}")
            else:
                print(f"   ❌ {desc}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试 terminate_request app_id 设置修复")
    
    tests = [
        test_syntax_fix,
        test_app_id_setting,
        test_session_model_usage,
        test_complete_flow,
        test_error_handling
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试 {test.__name__} 异常: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print("✅ 所有测试通过！terminate_request app_id 设置修复成功")
    else:
        print(f"❌ {total - passed} 个测试失败，{passed} 个测试通过")
    
    print(f"\n📊 测试结果: {passed}/{total}")
    
    print("\n📋 修复总结:")
    print("1. ✅ 修复了语法错误：移除了错误的await赋值语法")
    print("2. ✅ 正确获取session_model：从权限验证方法返回")
    print("3. ✅ 设置app_id：terminate_request.app_id = session_model.agent_id")
    print("4. ✅ 保持了完整的错误处理机制")
    
    print("\n🔧 修复详情:")
    print("- 错误语法: await session_model = self.get_session_with_permission_check_async(...)")
    print("- 正确语法: session_model = await self.get_session_with_permission_check_async(...)")
    print("- 新增设置: terminate_request.app_id = session_model.agent_id")
    
    print("\n🎯 修复效果:")
    print("- 🛡️ 权限验证：获取session_model并验证权限")
    print("- 📦 完整请求：terminate_request包含run_id和app_id")
    print("- 🔄 正确映射：app_id设置为session的agent_id")
    print("- 📝 完整日志：记录详细的操作信息")
    
    print("\n✅ 现在terminate_request会包含正确的app_id信息！")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
