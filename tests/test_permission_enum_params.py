# -*- coding: utf-8 -*-
"""
测试权限枚举参数功能
"""
import pytest
from unittest.mock import Mock, patch

from src.domain.services.auth_service import AuthService, AuthContext, _normalize_permission
from src.infrastructure.database.models.auth_models import PermissionType
from src.infrastructure.database.models.permission_utils import PermissionUtils, Permissions, normalize_permission


class TestPermissionEnumParams:
    """测试权限枚举参数功能"""
    
    def test_normalize_permission_with_enum(self):
        """测试枚举权限标准化"""
        # 测试枚举参数
        result = _normalize_permission(PermissionType.READ)
        assert result == "read"
        
        result = _normalize_permission(PermissionType.WRITE)
        assert result == "write"
        
        result = _normalize_permission(PermissionType.ADMIN)
        assert result == "admin"
    
    def test_normalize_permission_with_string(self):
        """测试字符串权限标准化"""
        # 测试字符串参数
        result = _normalize_permission("read")
        assert result == "read"
        
        result = _normalize_permission("write")
        assert result == "write"
        
        result = _normalize_permission("admin")
        assert result == "admin"
    
    def test_permission_utils_normalize(self):
        """测试权限工具类的标准化函数"""
        # 测试枚举参数
        result = normalize_permission(PermissionType.READ)
        assert result == "read"
        
        # 测试字符串参数
        result = normalize_permission("write")
        assert result == "write"
    
    def test_permission_utils_has_permission_with_enum(self):
        """测试权限检查支持枚举参数"""
        user_permissions = ["read", "write"]
        
        # 使用枚举参数
        assert PermissionUtils.has_permission(user_permissions, PermissionType.READ) == True
        assert PermissionUtils.has_permission(user_permissions, PermissionType.WRITE) == True
        assert PermissionUtils.has_permission(user_permissions, PermissionType.DELETE) == False
        
        # 使用字符串参数
        assert PermissionUtils.has_permission(user_permissions, "read") == True
        assert PermissionUtils.has_permission(user_permissions, "delete") == False
    
    def test_permission_utils_has_any_permission_with_enum(self):
        """测试任意权限检查支持枚举参数"""
        user_permissions = ["read", "write"]
        
        # 使用枚举参数
        required_permissions = [PermissionType.READ, PermissionType.DELETE]
        assert PermissionUtils.has_any_permission(user_permissions, required_permissions) == True
        
        required_permissions = [PermissionType.DELETE, PermissionType.ADMIN]
        assert PermissionUtils.has_any_permission(user_permissions, required_permissions) == False
    
    def test_permission_utils_has_all_permissions_with_enum(self):
        """测试全部权限检查支持枚举参数"""
        user_permissions = ["read", "write"]
        
        # 使用枚举参数
        required_permissions = [PermissionType.READ, PermissionType.WRITE]
        assert PermissionUtils.has_all_permissions(user_permissions, required_permissions) == True
        
        required_permissions = [PermissionType.READ, PermissionType.DELETE]
        assert PermissionUtils.has_all_permissions(user_permissions, required_permissions) == False
    
    def test_permission_type_checks_with_enum(self):
        """测试权限类型检查支持枚举参数"""
        # 使用枚举参数
        assert PermissionUtils.is_read_permission(PermissionType.READ) == True
        assert PermissionUtils.is_read_permission(PermissionType.WRITE) == False
        
        assert PermissionUtils.is_write_permission(PermissionType.WRITE) == True
        assert PermissionUtils.is_write_permission(PermissionType.READ) == False
        
        assert PermissionUtils.is_admin_permission(PermissionType.ADMIN) == True
        assert PermissionUtils.is_admin_permission(PermissionType.READ) == False
        
        # 使用字符串参数
        assert PermissionUtils.is_read_permission("read") == True
        assert PermissionUtils.is_read_permission("write") == False
    
    @patch('src.domain.kms.auth_service.auth_repository')
    def test_auth_service_check_resource_permission_with_enum(self, mock_repo):
        """测试AuthService权限检查支持枚举参数"""
        # 设置mock
        mock_resource = Mock()
        mock_resource.owner_ali_uid = 123
        mock_resource.owner_wy_id = "test_user"
        mock_resource.is_public = False
        mock_repo.get_resource.return_value = mock_resource
        
        # 创建服务和上下文
        auth_service = AuthService()
        context = AuthContext(ali_uid=123, wy_id="test_user")
        
        # 测试枚举参数
        result = auth_service.check_resource_permission(
            context=context,
            resource_type=ResourceType.FILE,
            resource_id="123",
            required_permission=PermissionType.READ
        )
        
        assert result.success == True
        assert result.message == "资源所有者"
        
        # 测试字符串参数（向后兼容）
        result = auth_service.check_resource_permission(
            context=context,
            resource_type=ResourceType.FILE,
            resource_id="123",
            required_permission=PermissionType.READ
        )
        
        assert result.success == True
        assert result.message == "资源所有者"
    
    def test_permissions_constants(self):
        """测试权限常量"""
        # 测试单个权限常量
        assert Permissions.READ == "read"
        assert Permissions.WRITE == "write"
        assert Permissions.DELETE == "delete"
        assert Permissions.SHARE == "share"
        assert Permissions.ADMIN == "admin"
        
        # 测试权限组合常量
        assert Permissions.READ_ONLY == ["read"]
        assert Permissions.READ_WRITE == ["read", "write"]
        assert set(Permissions.OWNER) == {"read", "write", "delete", "share", "admin"}
        assert Permissions.PUBLIC == ["read"]
    
    def test_backward_compatibility(self):
        """测试向后兼容性"""
        # 确保所有旧的字符串用法仍然有效
        user_permissions = ["read", "write"]
        
        # 字符串参数应该仍然工作
        assert PermissionUtils.has_permission(user_permissions, "read") == True
        assert PermissionUtils.is_read_permission("read") == True
        
        # 混合使用也应该工作
        required_permissions = ["read", PermissionType.WRITE]
        assert PermissionUtils.has_all_permissions(user_permissions, required_permissions) == True


if __name__ == "__main__":
    pytest.main([__file__])
