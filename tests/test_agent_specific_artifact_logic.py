#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试特定 Agent 的 ARTIFACT 事件逻辑
只有 ai_ppt Agent 才需要检查前一个事件是否为 ARTIFACT
"""

import pytest
from unittest.mock import Mock


class TestAgentSpecificArtifactLogic:
    """测试特定 Agent 的 ARTIFACT 事件逻辑"""

    def test_ai_ppt_agent_with_artifact_previous_should_not_finish(self):
        """测试 ai_ppt Agent：前一个事件是 ARTIFACT，不应该完成"""
        from memory.events import EventType
        
        # 模拟完成事件判断逻辑
        def should_finish_session(current_event, previous_event=None, agent_id=None):
            """模拟完成事件判断逻辑"""
            if not hasattr(current_event, 'type') or not current_event.type:
                return False
            
            # 检查是否是完成类型事件
            finish_types = [
                EventType.RUN_FINISHED.value,
                EventType.RUN_ERROR.value,
                EventType.RUN_STOPPED.value
            ]
            
            current_type = current_event.type.value
            if current_type not in finish_types:
                return False
            
            # 只有当 agent_id 为 ai_ppt 时，才检查前一个事件是否为 ARTIFACT
            if agent_id == "ai_ppt" and previous_event:
                previous_event_type = getattr(previous_event, 'type', None)
                if previous_event_type:
                    previous_type_value = previous_event_type.value if hasattr(previous_event_type, 'value') else str(previous_event_type)
                    if previous_type_value == EventType.ARTIFACT.value:
                        return False
            
            return True
        
        # 创建完成事件
        finish_event = Mock()
        finish_event.type = Mock()
        finish_event.type.value = EventType.RUN_FINISHED.value
        
        # 创建 ARTIFACT 事件
        artifact_event = Mock()
        artifact_event.type = Mock()
        artifact_event.type.value = EventType.ARTIFACT.value
        
        # 测试：ai_ppt Agent，前一个事件是 ARTIFACT，不应该完成
        result = should_finish_session(finish_event, artifact_event, "ai_ppt")
        assert result is False
        
        print("✅ ai_ppt Agent 前一个事件是 ARTIFACT 时不完成")

    def test_ai_ppt_agent_with_message_previous_should_finish(self):
        """测试 ai_ppt Agent：前一个事件是普通消息，应该完成"""
        from memory.events import EventType
        
        # 模拟完成事件判断逻辑
        def should_finish_session(current_event, previous_event=None, agent_id=None):
            """模拟完成事件判断逻辑"""
            if not hasattr(current_event, 'type') or not current_event.type:
                return False
            
            # 检查是否是完成类型事件
            finish_types = [
                EventType.RUN_FINISHED.value,
                EventType.RUN_ERROR.value,
                EventType.RUN_STOPPED.value
            ]
            
            current_type = current_event.type.value
            if current_type not in finish_types:
                return False
            
            # 只有当 agent_id 为 ai_ppt 时，才检查前一个事件是否为 ARTIFACT
            if agent_id == "ai_ppt" and previous_event:
                previous_event_type = getattr(previous_event, 'type', None)
                if previous_event_type:
                    previous_type_value = previous_event_type.value if hasattr(previous_event_type, 'value') else str(previous_event_type)
                    if previous_type_value == EventType.ARTIFACT.value:
                        return False
            
            return True
        
        # 创建完成事件
        finish_event = Mock()
        finish_event.type = Mock()
        finish_event.type.value = EventType.RUN_FINISHED.value
        
        # 创建普通消息事件
        message_event = Mock()
        message_event.type = Mock()
        message_event.type.value = "message"
        
        # 测试：ai_ppt Agent，前一个事件是普通消息，应该完成
        result = should_finish_session(finish_event, message_event, "ai_ppt")
        assert result is True
        
        print("✅ ai_ppt Agent 前一个事件是普通消息时应该完成")

    def test_other_agent_with_artifact_previous_should_finish(self):
        """测试其他 Agent：前一个事件是 ARTIFACT，仍然应该完成"""
        from memory.events import EventType
        
        # 模拟完成事件判断逻辑
        def should_finish_session(current_event, previous_event=None, agent_id=None):
            """模拟完成事件判断逻辑"""
            if not hasattr(current_event, 'type') or not current_event.type:
                return False
            
            # 检查是否是完成类型事件
            finish_types = [
                EventType.RUN_FINISHED.value,
                EventType.RUN_ERROR.value,
                EventType.RUN_STOPPED.value
            ]
            
            current_type = current_event.type.value
            if current_type not in finish_types:
                return False
            
            # 只有当 agent_id 为 ai_ppt 时，才检查前一个事件是否为 ARTIFACT
            if agent_id == "ai_ppt" and previous_event:
                previous_event_type = getattr(previous_event, 'type', None)
                if previous_event_type:
                    previous_type_value = previous_event_type.value if hasattr(previous_event_type, 'value') else str(previous_event_type)
                    if previous_type_value == EventType.ARTIFACT.value:
                        return False
            
            return True
        
        # 创建完成事件
        finish_event = Mock()
        finish_event.type = Mock()
        finish_event.type.value = EventType.RUN_FINISHED.value
        
        # 创建 ARTIFACT 事件
        artifact_event = Mock()
        artifact_event.type = Mock()
        artifact_event.type.value = EventType.ARTIFACT.value
        
        # 测试其他 Agent ID
        other_agents = ["ai_chat", "ai_code", "ai_search", None, ""]
        
        for agent_id in other_agents:
            # 测试：其他 Agent，前一个事件是 ARTIFACT，仍然应该完成
            result = should_finish_session(finish_event, artifact_event, agent_id)
            assert result is True, f"Agent {agent_id} 应该完成会话"
        
        print("✅ 其他 Agent 前一个事件是 ARTIFACT 时仍然应该完成")

    def test_ai_ppt_agent_without_previous_should_finish(self):
        """测试 ai_ppt Agent：没有前一个事件，应该完成"""
        from memory.events import EventType
        
        # 模拟完成事件判断逻辑
        def should_finish_session(current_event, previous_event=None, agent_id=None):
            """模拟完成事件判断逻辑"""
            if not hasattr(current_event, 'type') or not current_event.type:
                return False
            
            # 检查是否是完成类型事件
            finish_types = [
                EventType.RUN_FINISHED.value,
                EventType.RUN_ERROR.value,
                EventType.RUN_STOPPED.value
            ]
            
            current_type = current_event.type.value
            if current_type not in finish_types:
                return False
            
            # 只有当 agent_id 为 ai_ppt 时，才检查前一个事件是否为 ARTIFACT
            if agent_id == "ai_ppt" and previous_event:
                previous_event_type = getattr(previous_event, 'type', None)
                if previous_event_type:
                    previous_type_value = previous_event_type.value if hasattr(previous_event_type, 'value') else str(previous_event_type)
                    if previous_type_value == EventType.ARTIFACT.value:
                        return False
            
            return True
        
        # 创建完成事件
        finish_event = Mock()
        finish_event.type = Mock()
        finish_event.type.value = EventType.RUN_FINISHED.value
        
        # 测试：ai_ppt Agent，没有前一个事件，应该完成
        result = should_finish_session(finish_event, None, "ai_ppt")
        assert result is True
        
        print("✅ ai_ppt Agent 没有前一个事件时应该完成")

    def test_all_finish_event_types_with_ai_ppt(self):
        """测试 ai_ppt Agent 的所有完成事件类型"""
        from memory.events import EventType
        
        # 模拟完成事件判断逻辑
        def should_finish_session(current_event, previous_event=None, agent_id=None):
            """模拟完成事件判断逻辑"""
            if not hasattr(current_event, 'type') or not current_event.type:
                return False
            
            # 检查是否是完成类型事件
            finish_types = [
                EventType.RUN_FINISHED.value,
                EventType.RUN_ERROR.value,
                EventType.RUN_STOPPED.value
            ]
            
            current_type = current_event.type.value
            if current_type not in finish_types:
                return False
            
            # 只有当 agent_id 为 ai_ppt 时，才检查前一个事件是否为 ARTIFACT
            if agent_id == "ai_ppt" and previous_event:
                previous_event_type = getattr(previous_event, 'type', None)
                if previous_event_type:
                    previous_type_value = previous_event_type.value if hasattr(previous_event_type, 'value') else str(previous_event_type)
                    if previous_type_value == EventType.ARTIFACT.value:
                        return False
            
            return True
        
        # 创建 ARTIFACT 事件
        artifact_event = Mock()
        artifact_event.type = Mock()
        artifact_event.type.value = EventType.ARTIFACT.value
        
        # 测试所有完成事件类型
        finish_event_types = [
            EventType.RUN_FINISHED.value,
            EventType.RUN_ERROR.value,
            EventType.RUN_STOPPED.value
        ]
        
        for event_type in finish_event_types:
            # 创建完成事件
            finish_event = Mock()
            finish_event.type = Mock()
            finish_event.type.value = event_type
            
            # 测试：ai_ppt Agent，前一个事件是 ARTIFACT，不应该完成
            result1 = should_finish_session(finish_event, artifact_event, "ai_ppt")
            assert result1 is False, f"ai_ppt Agent 的 {event_type} 事件在 ARTIFACT 后不应该完成"
            
            # 测试：ai_ppt Agent，没有前一个事件，应该完成
            result2 = should_finish_session(finish_event, None, "ai_ppt")
            assert result2 is True, f"ai_ppt Agent 的 {event_type} 事件没有前一个事件时应该完成"
        
        print("✅ ai_ppt Agent 所有完成事件类型的逻辑正确")

    def test_case_sensitivity(self):
        """测试 Agent ID 的大小写敏感性"""
        from memory.events import EventType
        
        # 模拟完成事件判断逻辑
        def should_finish_session(current_event, previous_event=None, agent_id=None):
            """模拟完成事件判断逻辑"""
            if not hasattr(current_event, 'type') or not current_event.type:
                return False
            
            # 检查是否是完成类型事件
            finish_types = [
                EventType.RUN_FINISHED.value,
                EventType.RUN_ERROR.value,
                EventType.RUN_STOPPED.value
            ]
            
            current_type = current_event.type.value
            if current_type not in finish_types:
                return False
            
            # 只有当 agent_id 为 ai_ppt 时，才检查前一个事件是否为 ARTIFACT
            if agent_id == "ai_ppt" and previous_event:
                previous_event_type = getattr(previous_event, 'type', None)
                if previous_event_type:
                    previous_type_value = previous_event_type.value if hasattr(previous_event_type, 'value') else str(previous_event_type)
                    if previous_type_value == EventType.ARTIFACT.value:
                        return False
            
            return True
        
        # 创建完成事件
        finish_event = Mock()
        finish_event.type = Mock()
        finish_event.type.value = EventType.RUN_FINISHED.value
        
        # 创建 ARTIFACT 事件
        artifact_event = Mock()
        artifact_event.type = Mock()
        artifact_event.type.value = EventType.ARTIFACT.value
        
        # 测试大小写变体（这些都不应该触发特殊逻辑）
        case_variants = ["AI_PPT", "Ai_Ppt", "ai_PPT", "AI_ppt"]
        
        for variant in case_variants:
            result = should_finish_session(finish_event, artifact_event, variant)
            assert result is True, f"Agent ID {variant} 应该完成会话（大小写敏感）"
        
        # 只有精确匹配 "ai_ppt" 才不完成
        result = should_finish_session(finish_event, artifact_event, "ai_ppt")
        assert result is False, "只有精确匹配 'ai_ppt' 才不完成会话"
        
        print("✅ Agent ID 大小写敏感性测试正确")


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v", "-s"])
