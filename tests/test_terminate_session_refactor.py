#!/usr/bin/env python3
"""
测试 terminate_session 方法重构 - 将SSE连接关闭也包含在内
"""

import os


def test_terminate_session_refactor():
    """测试terminate_session方法重构"""
    print("🧪 测试 terminate_session 方法重构")
    print("=" * 50)
    
    try:
        # 读取session_service.py文件
        service_file = os.path.join(os.path.dirname(__file__), '..', 'src', 'domain', 'kms', 'session_service.py')
        
        with open(service_file, 'r', encoding='utf-8') as f:
            service_content = f.read()
        
        # 检查方法文档更新
        doc_checks = [
            ('终止会话的异步任务并关闭SSE连接', "更新了方法描述"),
            ('Returns:', "包含返回值说明"),
            ('bool: 操作是否成功', "更新了返回值描述"),
            ('Raises:', "包含异常说明"),
            ('Exception: 当权限验证失败时抛出', "更新了异常描述")
        ]
        
        print("📝 方法文档检查:")
        doc_ok = True
        
        for check, desc in doc_checks:
            if check in service_content:
                print(f"   ✅ {desc}")
            else:
                print(f"   ❌ {desc}")
                doc_ok = False
        
        # 检查新增的SSE连接关闭逻辑
        sse_checks = [
            ('# 4. 关闭SSE连接（无论异步任务终止是否成功）', "添加了SSE关闭步骤注释"),
            ('self.sse_stream_manager.close_session_connection(session_id)', "调用SSE连接关闭"),
            ('logger.info(f"[SessionService] SSE连接关闭成功:', "SSE关闭成功日志"),
            ('except Exception as close_error:', "SSE关闭异常处理"),
            ('logger.warning(f"[SessionService] 关闭SSE连接失败:', "SSE关闭失败日志"),
            ('# SSE连接关闭失败不影响整体操作结果', "SSE关闭容错注释")
        ]
        
        print("\n📝 SSE连接关闭逻辑检查:")
        sse_ok = True
        
        for check, desc in sse_checks:
            if check in service_content:
                print(f"   ✅ {desc}")
            else:
                print(f"   ❌ {desc}")
                sse_ok = False
        
        # 检查异步任务终止的容错处理
        fault_tolerance_checks = [
            ('except WaiyInfraClientError as client_error:', "客户端异常捕获"),
            ('# 即使终止异步任务失败，也要继续关闭SSE连接', "异步任务失败容错注释"),
            ('return True', "方法返回成功")
        ]
        
        print("\n📝 容错处理检查:")
        fault_ok = True
        
        for check, desc in fault_tolerance_checks:
            if check in service_content:
                print(f"   ✅ {desc}")
            else:
                print(f"   ❌ {desc}")
                fault_ok = False
        
        return doc_ok and sse_ok and fault_ok
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_api_interface_simplification():
    """测试API接口简化"""
    print("\n🧪 测试API接口简化")
    print("=" * 50)
    
    try:
        # 读取session_routes.py文件
        routes_file = os.path.join(os.path.dirname(__file__), '..', 'src', 'presentation', 'api', 'routes', 'session_routes.py')
        
        with open(routes_file, 'r', encoding='utf-8') as f:
            routes_content = f.read()
        
        # 检查简化后的接口代码
        simplification_checks = [
            ('# 1. 终止会话异步任务并关闭SSE连接（内部包含权限验证）', "更新了步骤注释"),
            ('await session_service.terminate_session(request.session_id, current_user)', "直接调用terminate_session"),
            ('logger.info(f"[API] 会话终止和SSE连接断开成功:', "更新了成功日志"),
        ]
        
        print("📝 接口简化检查:")
        simplification_ok = True
        
        for check, desc in simplification_checks:
            if check in routes_content:
                print(f"   ✅ {desc}")
            else:
                print(f"   ❌ {desc}")
                simplification_ok = False
        
        # 检查移除的重复代码
        removed_checks = [
            ('try:', "移除了多余的try块"),
            ('except Exception as terminate_error:', "移除了terminate异常处理"),
            ('logger.warning(f"[API] 终止会话异步任务失败，继续断开SSE连接:', "移除了失败警告日志"),
            ('# 2. 调用SSE管理器断开连接', "移除了步骤2注释"),
            ('session_service.sse_stream_manager.close_session_connection(request.session_id)', "移除了重复的SSE关闭调用")
        ]
        
        print("\n📝 重复代码移除检查:")
        removed_ok = True
        
        # 在disconnect_session函数范围内检查
        disconnect_start = routes_content.find('async def disconnect_session(')
        disconnect_end = routes_content.find('async def ', disconnect_start + 1)
        if disconnect_end == -1:
            disconnect_end = len(routes_content)
        
        disconnect_function = routes_content[disconnect_start:disconnect_end]
        
        for check, desc in removed_checks:
            if check not in disconnect_function:
                print(f"   ✅ {desc}")
            else:
                print(f"   ❌ {desc} - 仍然存在")
                removed_ok = False
        
        return simplification_ok and removed_ok
        
    except Exception as e:
        print(f"❌ API接口简化测试失败: {e}")
        return False


def test_permission_security():
    """测试权限安全性"""
    print("\n🧪 测试权限安全性")
    print("=" * 50)
    
    try:
        # 读取session_service.py文件
        service_file = os.path.join(os.path.dirname(__file__), '..', 'src', 'domain', 'kms', 'session_service.py')
        
        with open(service_file, 'r', encoding='utf-8') as f:
            service_content = f.read()
        
        # 检查权限验证逻辑
        permission_checks = [
            ('# 1. 权限验证', "权限验证步骤"),
            ('await self.get_session_with_permission_check_async(', "权限验证调用"),
            ('required_permission=PermissionType.READ', "READ权限要求"),
            ('except Exception as perm_error:', "权限异常处理"),
            ('logger.error(f"[SessionService] 会话权限验证失败:', "权限失败日志"),
            ('raise', "权限异常重新抛出")
        ]
        
        print("📝 权限验证逻辑检查:")
        permission_ok = True
        
        for check, desc in permission_checks:
            if check in service_content:
                print(f"   ✅ {desc}")
            else:
                print(f"   ❌ {desc}")
                permission_ok = False
        
        # 检查权限验证在SSE关闭之前
        terminate_function_start = service_content.find('async def terminate_session(')
        terminate_function_end = service_content.find('async def ', terminate_function_start + 1)
        if terminate_function_end == -1:
            terminate_function_end = len(service_content)
        
        terminate_function = service_content[terminate_function_start:terminate_function_end]
        
        permission_pos = terminate_function.find('get_session_with_permission_check_async')
        sse_close_pos = terminate_function.find('sse_stream_manager.close_session_connection')
        
        if permission_pos != -1 and sse_close_pos != -1:
            if permission_pos < sse_close_pos:
                print("   ✅ 权限验证在SSE关闭之前执行")
                order_ok = True
            else:
                print("   ❌ 权限验证顺序错误")
                order_ok = False
        else:
            print("   ❌ 未找到权限验证或SSE关闭调用")
            order_ok = False
        
        return permission_ok and order_ok
        
    except Exception as e:
        print(f"❌ 权限安全性测试失败: {e}")
        return False


def test_error_handling_flow():
    """测试错误处理流程"""
    print("\n🧪 测试错误处理流程")
    print("=" * 50)
    
    try:
        # 读取session_service.py文件
        service_file = os.path.join(os.path.dirname(__file__), '..', 'src', 'domain', 'kms', 'session_service.py')
        
        with open(service_file, 'r', encoding='utf-8') as f:
            service_content = f.read()
        
        # 检查各种错误处理场景
        error_handling_checks = [
            # 权限验证失败 - 直接抛出异常，不执行后续操作
            ('except Exception as perm_error:', "权限验证异常捕获"),
            ('raise', "权限异常重新抛出"),
            
            # 异步任务终止失败 - 继续执行SSE关闭
            ('except WaiyInfraClientError as client_error:', "客户端异常捕获"),
            ('except Exception as terminate_error:', "终止异常捕获"),
            ('# 即使终止异步任务失败，也要继续关闭SSE连接', "容错处理注释"),
            
            # SSE关闭失败 - 不影响整体结果
            ('except Exception as close_error:', "SSE关闭异常捕获"),
            ('# SSE连接关闭失败不影响整体操作结果', "SSE关闭容错注释"),
            
            # 最终异常处理
            ('except Exception as e:', "最终异常捕获"),
            ('logger.error(f"[SessionService] 终止会话失败:', "最终异常日志")
        ]
        
        print("📝 错误处理流程检查:")
        all_passed = True
        
        for check, desc in error_handling_checks:
            if check in service_content:
                print(f"   ✅ {desc}")
            else:
                print(f"   ❌ {desc}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 错误处理流程测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试 terminate_session 方法重构")
    
    tests = [
        test_terminate_session_refactor,
        test_api_interface_simplification,
        test_permission_security,
        test_error_handling_flow
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试 {test.__name__} 异常: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print("✅ 所有测试通过！terminate_session 方法重构成功")
    else:
        print(f"❌ {total - passed} 个测试失败，{passed} 个测试通过")
    
    print(f"\n📊 测试结果: {passed}/{total}")
    
    print("\n📋 重构总结:")
    print("1. ✅ 将SSE连接关闭逻辑移入 terminate_session 方法")
    print("2. ✅ 确保权限验证在所有操作之前执行")
    print("3. ✅ 简化了API接口层的代码")
    print("4. ✅ 保持了完整的错误处理和容错机制")
    
    print("\n🎯 重构效果:")
    print("- 🛡️ 权限安全：权限验证失败时不会执行任何操作")
    print("- 📦 职责集中：所有会话终止相关操作都在一个方法中")
    print("- 🔄 容错处理：异步任务失败不影响SSE连接关闭")
    print("- 🎨 代码简洁：API接口层代码更简洁清晰")
    
    print("\n🔍 新的执行流程:")
    print("1. 用户调用 /sessions/disconnect 接口")
    print("2. 接口直接调用 session_service.terminate_session()")
    print("3. terminate_session 内部:")
    print("   a. 权限验证（失败则抛出异常，停止执行）")
    print("   b. 获取事件和run_id")
    print("   c. 终止异步任务（失败不影响后续）")
    print("   d. 关闭SSE连接（失败不影响整体结果）")
    print("4. 返回成功响应")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
