#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 send_message 方法中的模型设置功能
"""

import sys
import os
import asyncio
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.domain.services.session_service import SessionService
from src.domain.services.auth_service import AuthContext
from src.application.api_models import UserSettingData, CurrentSetting


def test_model_setting_creation():
    """测试模型设置对象的创建"""
    print("=== 测试模型设置对象创建 ===")
    
    try:
        from alibabacloud_wuyingaiinner20250708 import models as waiy_models
        
        # 测试创建模型设置对象
        model_setting = waiy_models.MessageAsyncRequestContextModelSetting(
            model_level="qwen-plus"
        )
        
        print(f"✓ 模型设置对象创建成功: model_level={model_setting.model_level}")
        return True
        
    except Exception as e:
        print(f"✗ 模型设置对象创建失败: {e}")
        return False


def test_user_setting_integration():
    """测试用户设置集成"""
    print("\n=== 测试用户设置集成 ===")
    
    try:
        from src.domain.services.user_service import user_service
        
        # 创建模拟的认证上下文
        mock_context = Mock(spec=AuthContext)
        mock_context.ali_uid = 12345
        mock_context.wy_id = "test_wy_id"
        mock_context.user_key = "test_user"
        
        # 模拟用户设置数据
        mock_current_setting = CurrentSetting(
            desktop_id="test_desktop",
            model="qwen-max"
        )
        
        mock_setting_data = UserSettingData(
            current_setting=mock_current_setting,
            available_environments=[],
            available_models=["qwen-plus", "qwen-max", "qwen-turbo"],
            version="1.0.0"
        )
        
        # 模拟 user_service.get_user_setting 方法
        with patch.object(user_service, 'get_user_setting', return_value=mock_setting_data):
            setting_data = user_service.get_user_setting(context=mock_context)
            model_level = setting_data.current_setting.model
            
            print(f"✓ 获取用户设置成功: model={model_level}")
            
            # 测试创建模型设置对象
            from alibabacloud_wuyingaiinner20250708 import models as waiy_models
            model_setting = waiy_models.MessageAsyncRequestContextModelSetting(
                model_level=model_level
            )
            
            print(f"✓ 基于用户设置创建模型设置对象成功: model_level={model_setting.model_level}")
            return True
            
    except Exception as e:
        print(f"✗ 用户设置集成测试失败: {e}")
        return False


def test_send_message_model_setting_logic():
    """测试 send_message 方法中的模型设置逻辑"""
    print("\n=== 测试 send_message 模型设置逻辑 ===")
    
    try:
        # 创建 SessionService 实例
        session_service = SessionService()
        
        # 创建模拟的认证上下文
        mock_context = Mock(spec=AuthContext)
        mock_context.ali_uid = 12345
        mock_context.wy_id = "test_wy_id"
        mock_context.user_key = "test_user"
        
        # 模拟用户设置数据
        mock_current_setting = CurrentSetting(
            desktop_id="test_desktop",
            model="claude-3.5-sonnet"
        )
        
        mock_setting_data = UserSettingData(
            current_setting=mock_current_setting,
            available_environments=[],
            available_models=["qwen-plus", "qwen-max", "claude-3.5-sonnet"],
            version="1.0.0"
        )
        
        # 模拟各种依赖
        with patch('src.domain.kms.user_service.user_service') as mock_user_service, \
             patch('src.domain.kms.session_service.create_waiy_infra_client') as mock_waiy_client_factory, \
             patch.object(session_service, 'get_or_create_session_domain') as mock_get_session, \
             patch.object(session_service, '_process_resources') as mock_process_resources, \
             patch.object(session_service, '_build_runtime_resource') as mock_build_runtime, \
             patch.object(session_service, '_generate_session_title_async') as mock_generate_title:
            
            # 设置模拟返回值
            mock_user_service.get_user_setting.return_value = mock_setting_data
            
            # 模拟会话对象
            mock_session = Mock()
            mock_session.session_id = "test_session_123"
            mock_session.title = ""
            mock_session.start_processing = Mock()
            mock_get_session.return_value = mock_session
            
            # 模拟资源处理
            mock_process_resources.return_value = []
            
            # 模拟运行时资源
            mock_runtime_resource = Mock()
            mock_build_runtime.return_value = mock_runtime_resource
            
            # 模拟 WaiyInfra 客户端
            mock_waiy_client = Mock()
            mock_waiy_client_factory.return_value = mock_waiy_client
            
            # 模拟消息上下文创建
            mock_message_context = Mock()
            mock_waiy_client.create_async_message_context.return_value = mock_message_context
            
            # 模拟消息发送响应
            mock_response = Mock()
            mock_response.body = Mock()
            mock_response.body.trace_id = "test_round_456"
            mock_waiy_client.message_async_sync.return_value = mock_response
            
            # 调用 send_message 方法
            result = asyncio.run(session_service.send_message(
                session_id=None,
                prompt="测试消息",
                agent_id="test_agent",
                desktop_id="test_desktop",
                auth_code="test_auth",
                resources=None,
                context=mock_context
            ))
            
            # 验证结果
            session_id, round_id = result
            print(f"✓ send_message 调用成功: session_id={session_id}, round_id={round_id}")
            
            # 验证 user_service.get_user_setting 被调用
            mock_user_service.get_user_setting.assert_called_once_with(context=mock_context)
            print("✓ user_service.get_user_setting 被正确调用")
            
            # 验证 create_async_message_context 被调用，并且传入了 model_setting
            mock_waiy_client.create_async_message_context.assert_called_once()
            call_args = mock_waiy_client.create_async_message_context.call_args
            
            # 检查是否传入了 model_setting 参数
            assert 'model_setting' in call_args.kwargs, "model_setting 参数未传入"
            model_setting = call_args.kwargs['model_setting']
            
            if model_setting is not None:
                print(f"✓ model_setting 参数传入成功: model_level={model_setting.model_level}")
                assert model_setting.model_level == "claude-3.5-sonnet", f"模型级别不匹配: {model_setting.model_level}"
                print("✓ 模型级别验证成功")
            else:
                print("✗ model_setting 参数为 None")
                return False
            
            return True
            
    except Exception as e:
        print(f"✗ send_message 模型设置逻辑测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("开始测试 send_message 方法中的模型设置功能...\n")
    
    test_results = []
    
    # 运行各项测试
    test_results.append(test_model_setting_creation())
    test_results.append(test_user_setting_integration())
    test_results.append(test_send_message_model_setting_logic())
    
    # 汇总结果
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"\n=== 测试结果汇总 ===")
    print(f"通过: {passed}/{total}")
    print(f"失败: {total - passed}/{total}")
    
    if passed == total:
        print("✓ 所有测试通过！")
        return True
    else:
        print("✗ 部分测试失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
