#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PPT Service 测试
"""

import pytest
from unittest.mock import Mock, patch
from src.domain.services.ppt_service import PPTService, PPTServiceError
from src.application.ppt_api_models import GetPPTAuthCodeResponse


def test_get_ppt_auth_code():
    """测试获取PPT认证码"""
    # 创建服务实例
    service = PPTService()

    # 调用API
    result = service.get_ppt_auth_code(ali_uid=1550203943326350)

    # 检查结果
    assert result is not None
    assert hasattr(result, 'code')
    assert hasattr(result, 'time_expire')
    assert hasattr(result, 'api_key')
    assert hasattr(result, 'channel')

    # 检查类型
    assert isinstance(result, GetPPTAuthCodeResponse)
    assert isinstance(result.code, str)
    assert isinstance(result.time_expire, str)
    assert isinstance(result.api_key, str)
    assert isinstance(result.channel, str)


def test_get_aippt_token():
    """测试获取AIPPT token"""
    service = PPTService()

    token = service._get_aippt_token(ali_uid=1550203943326350)

    assert token is not None
    assert isinstance(token, str)
    assert len(token) > 0


@patch('src.domain.kms.ppt_service.get_aippt_client')
@patch.object(PPTService, '_get_memory')
def test_save_ppt_success(mock_get_memory, mock_get_aippt_client):
    """测试保存PPT成功场景"""
    # 设置mock对象
    mock_client = Mock()
    mock_get_aippt_client.return_value = mock_client
    
    # Mock PPT信息
    mock_ppt_info = {
        'name': '测试PPT演示文稿',
        'id': 'ppt_12345'
    }
    mock_client.get_ppt_info.return_value = mock_ppt_info
    
    # Mock 导出任务
    mock_task_key = 'export_task_12345'
    mock_client.export_ppt.return_value = mock_task_key
    
    # Mock Memory SDK
    mock_memory = Mock()
    mock_memory.add_event.return_value = True
    mock_get_memory.return_value = mock_memory
    
    # Mock Redis token
    with patch('src.domain.kms.ppt_service.RedisClient') as mock_redis_client:
        mock_redis = Mock()
        mock_redis.get.return_value = 'mock_token_12345'
        mock_redis_client.return_value = mock_redis
        
        # Mock 轮询结果
        with patch.object(PPTService, '_poll_export_result') as mock_poll:
            mock_download_url = 'https://example.com/download/ppt/12345.pptx'
            mock_poll.return_value = mock_download_url
            
            # 执行测试
            service = PPTService()
            result = service.save_ppt(ppt_id="test_ppt_123", ali_uid=123456)
            
            # 验证结果
            assert result == mock_download_url
            assert isinstance(result, str)
            assert result.startswith('https://')
            
            # 验证调用链
            mock_client.get_ppt_info.assert_called_once_with(ppt_id="test_ppt_123", token='mock_token_12345')
            mock_client.export_ppt.assert_called_once_with(ppt_id="test_ppt_123", token='mock_token_12345', format='ppt')
            mock_poll.assert_called_once_with(mock_task_key, 'mock_token_12345', timeout=60)
            
            # 注意：制品事件更新逻辑已被注释，所以不验证Memory调用
            # mock_memory.add_event 不会被调用


@patch('src.domain.kms.ppt_service.get_aippt_client')
@patch.object(PPTService, '_get_memory')
def test_save_ppt_with_different_formats(mock_get_memory, mock_get_aippt_client):
    """测试保存PPT时使用不同的导出格式"""
    # 设置mock对象
    mock_client = Mock()
    mock_get_aippt_client.return_value = mock_client
    
    # Mock PPT信息
    mock_ppt_info = {
        'name': '测试PPT演示文稿',
        'id': 'ppt_12345'
    }
    mock_client.get_ppt_info.return_value = mock_ppt_info
    
    # Mock 导出任务
    mock_task_key = 'export_task_12345'
    mock_client.export_ppt.return_value = mock_task_key
    
    # Mock Memory SDK
    mock_memory = Mock()
    mock_memory.add_event.return_value = True
    mock_get_memory.return_value = mock_memory
    
    # Mock Redis token
    with patch('src.domain.kms.ppt_service.RedisClient') as mock_redis_client:
        mock_redis = Mock()
        mock_redis.get.return_value = 'mock_token_12345'
        mock_redis_client.return_value = mock_redis
        
        # Mock 轮询结果
        with patch.object(PPTService, '_poll_export_result') as mock_poll:
            mock_download_url = 'https://example.com/download/ppt/12345'
            mock_poll.return_value = mock_download_url
            
            # 测试不同的导出格式
            service = PPTService()
            test_formats = ['ppt', 'pdf', 'png', 'jpeg']
            
            for format_type in test_formats:
                # 重置mock调用计数
                mock_client.export_ppt.reset_mock()
                
                # 执行测试
                result = service.save_ppt(
                    ppt_id="test_ppt_123", 
                    ali_uid=123456, 
                    format=format_type
                )
                
                # 验证结果
                assert result == mock_download_url
                
                # 验证export_ppt被调用时传入了正确的format参数
                mock_client.export_ppt.assert_called_once_with(
                    ppt_id="test_ppt_123", 
                    token='mock_token_12345', 
                    format=format_type
                )


@patch('src.domain.kms.ppt_service.get_aippt_client')
@patch.object(PPTService, '_get_memory')
def test_save_ppt_default_format(mock_get_memory, mock_get_aippt_client):
    """测试保存PPT时使用默认格式（不传format参数）"""
    # 设置mock对象
    mock_client = Mock()
    mock_get_aippt_client.return_value = mock_client
    
    # Mock PPT信息
    mock_ppt_info = {
        'name': '测试PPT演示文稿',
        'id': 'ppt_12345'
    }
    mock_client.get_ppt_info.return_value = mock_ppt_info
    
    # Mock 导出任务
    mock_task_key = 'export_task_12345'
    mock_client.export_ppt.return_value = mock_task_key
    
    # Mock Memory SDK
    mock_memory = Mock()
    mock_memory.add_event.return_value = True
    mock_get_memory.return_value = mock_memory
    
    # Mock Redis token
    with patch('src.domain.kms.ppt_service.RedisClient') as mock_redis_client:
        mock_redis = Mock()
        mock_redis.get.return_value = 'mock_token_12345'
        mock_redis_client.return_value = mock_redis
        
        # Mock 轮询结果
        with patch.object(PPTService, '_poll_export_result') as mock_poll:
            mock_download_url = 'https://example.com/download/ppt/12345.pptx'
            mock_poll.return_value = mock_download_url
            
            # 执行测试（不传format参数，应该使用默认值"ppt"）
            service = PPTService()
            result = service.save_ppt(ppt_id="test_ppt_123", ali_uid=123456)
            
            # 验证结果
            assert result == mock_download_url
            
            # 验证export_ppt被调用时使用了默认的format="ppt"
            mock_client.export_ppt.assert_called_once_with(
                ppt_id="test_ppt_123", 
                token='mock_token_12345', 
                format='ppt'  # 默认值
            )


@patch('src.domain.kms.ppt_service.get_aippt_client')
@patch.object(PPTService, '_get_memory')
def test_save_ppt_invalid_format_handling(mock_get_memory, mock_get_aippt_client):
    """测试save_ppt方法对无效格式的处理（应该按照传入的值处理）"""
    # 设置mock对象
    mock_client = Mock()
    mock_get_aippt_client.return_value = mock_client
    
    # Mock PPT信息
    mock_ppt_info = {
        'name': '测试PPT演示文稿',
        'id': 'ppt_12345'
    }
    mock_client.get_ppt_info.return_value = mock_ppt_info
    
    # Mock 导出任务
    mock_task_key = 'export_task_12345'
    mock_client.export_ppt.return_value = mock_task_key
    
    # Mock Memory SDK
    mock_memory = Mock()
    mock_memory.add_event.return_value = True
    mock_get_memory.return_value = mock_memory
    
    # Mock Redis token
    with patch('src.domain.kms.ppt_service.RedisClient') as mock_redis_client:
        mock_redis = Mock()
        mock_redis.get.return_value = 'mock_token_12345'
        mock_redis_client.return_value = mock_redis
        
        # Mock 轮询结果
        with patch.object(PPTService, '_poll_export_result') as mock_poll:
            mock_download_url = 'https://example.com/download/ppt/12345'
            mock_poll.return_value = mock_download_url
            
            # 执行测试 - 传入一个边界值格式
            service = PPTService()
            result = service.save_ppt(
                ppt_id="test_ppt_123", 
                ali_uid=123456, 
                format="unknown_format"  # 传入未知格式
            )
            
            # 验证结果（PPTService应该原样传递格式参数）
            assert result == mock_download_url
            
            # 验证export_ppt被调用时传入了原始格式参数
            mock_client.export_ppt.assert_called_once_with(
                ppt_id="test_ppt_123", 
                token='mock_token_12345', 
                format="unknown_format"  # 应该原样传递
            )


@patch('src.domain.kms.ppt_service.get_aippt_client')
@patch.object(PPTService, '_get_memory')
def test_save_ppt_empty_format_handling(mock_get_memory, mock_get_aippt_client):
    """测试save_ppt方法对空格式字符串的处理"""
    # 设置mock对象
    mock_client = Mock()
    mock_get_aippt_client.return_value = mock_client
    
    # Mock PPT信息
    mock_ppt_info = {
        'name': '测试PPT演示文稿',
        'id': 'ppt_12345'
    }
    mock_client.get_ppt_info.return_value = mock_ppt_info
    
    # Mock 导出任务
    mock_task_key = 'export_task_12345'
    mock_client.export_ppt.return_value = mock_task_key
    
    # Mock Memory SDK
    mock_memory = Mock()
    mock_memory.add_event.return_value = True
    mock_get_memory.return_value = mock_memory
    
    # Mock Redis token
    with patch('src.domain.kms.ppt_service.RedisClient') as mock_redis_client:
        mock_redis = Mock()
        mock_redis.get.return_value = 'mock_token_12345'
        mock_redis_client.return_value = mock_redis
        
        # Mock 轮询结果
        with patch.object(PPTService, '_poll_export_result') as mock_poll:
            mock_download_url = 'https://example.com/download/ppt/12345'
            mock_poll.return_value = mock_download_url
            
            # 执行测试 - 传入空字符串格式
            service = PPTService()
            result = service.save_ppt(
                ppt_id="test_ppt_123", 
                ali_uid=123456, 
                format=""  # 空字符串
            )
            
            # 验证结果
            assert result == mock_download_url
            
            # 验证export_ppt被调用时传入了空字符串
            mock_client.export_ppt.assert_called_once_with(
                ppt_id="test_ppt_123", 
                token='mock_token_12345', 
                format=""  # 应该原样传递空字符串
            )


@patch('src.domain.kms.ppt_service.get_aippt_client')
def test_save_ppt_export_timeout(mock_get_aippt_client):
    """测试保存PPT导出超时场景"""
    # 设置mock对象
    mock_client = Mock()
    mock_get_aippt_client.return_value = mock_client
    
    # Mock PPT信息
    mock_ppt_info = {'name': '测试PPT', 'id': 'ppt_12345'}
    mock_client.get_ppt_info.return_value = mock_ppt_info
    
    # Mock 导出任务
    mock_task_key = 'export_task_12345'
    mock_client.export_ppt.return_value = mock_task_key
    
    # Mock Redis token
    with patch('src.domain.kms.ppt_service.RedisClient') as mock_redis_client:
        mock_redis = Mock()
        mock_redis.get.return_value = 'mock_token_12345'
        mock_redis_client.return_value = mock_redis
        
        # Mock 轮询结果返回None（超时）
        with patch.object(PPTService, '_poll_export_result') as mock_poll:
            mock_poll.return_value = None  # 模拟超时
            
            # 执行测试并验证异常
            service = PPTService()
            with pytest.raises(PPTServiceError) as exc_info:
                service.save_ppt(ppt_id="test_ppt_123", ali_uid=123456)
            
            # 验证异常信息
            assert "PPT导出超时" in str(exc_info.value)
            
            # 验证调用链
            mock_client.get_ppt_info.assert_called_once()
            mock_client.export_ppt.assert_called_once_with(ppt_id="test_ppt_123", token='mock_token_12345', format='ppt')
            mock_poll.assert_called_once()


@patch('src.domain.kms.ppt_service.get_aippt_client')
@patch.object(PPTService, '_get_memory')
def test_save_ppt_memory_update_failure(mock_get_memory, mock_get_aippt_client):
    """测试保存PPT成功但制品更新失败的场景"""
    # 设置mock对象
    mock_client = Mock()
    mock_get_aippt_client.return_value = mock_client
    
    # Mock PPT信息和导出
    mock_client.get_ppt_info.return_value = {'name': '测试PPT'}
    mock_client.export_ppt.return_value = 'task_123'
    
    # Mock Memory SDK失败
    mock_memory = Mock()
    mock_memory.add_event.return_value = False  # 制品更新失败
    mock_get_memory.return_value = mock_memory
    
    # Mock Redis token
    with patch('src.domain.kms.ppt_service.RedisClient') as mock_redis_client:
        mock_redis = Mock()
        mock_redis.get.return_value = 'mock_token'
        mock_redis_client.return_value = mock_redis
        
        # Mock 轮询成功
        with patch.object(PPTService, '_poll_export_result') as mock_poll:
            mock_download_url = 'https://example.com/download/test.pptx'
            mock_poll.return_value = mock_download_url
            
            # 执行测试
            service = PPTService()
            result = service.save_ppt(ppt_id="test_ppt_123", ali_uid=123456)
            
            # 验证PPT保存成功（制品更新逻辑已被注释掉）
            assert result == mock_download_url
            # 注意：制品事件更新逻辑已被注释，不会调用add_event
            
            # 验证export_ppt调用使用了默认format
            mock_client.export_ppt.assert_called_once_with(ppt_id="test_ppt_123", token='mock_token', format='ppt')


@patch('src.domain.kms.ppt_service.get_aippt_client')
def test_save_ppt_client_error(mock_get_aippt_client):
    """测试保存PPT时客户端错误场景"""
    # Mock 客户端异常
    mock_client = Mock()
    mock_client.get_ppt_info.side_effect = Exception("网络错误")
    mock_get_aippt_client.return_value = mock_client
    
    # Mock Redis token
    with patch('src.domain.kms.ppt_service.RedisClient') as mock_redis_client:
        mock_redis = Mock()
        mock_redis.get.return_value = 'mock_token'
        mock_redis_client.return_value = mock_redis
        
        # 执行测试并验证异常
        service = PPTService()
        with pytest.raises(PPTServiceError) as exc_info:
            service.save_ppt(ppt_id="test_ppt_123", ali_uid=123456)
        
        # 验证异常信息
        assert "保存PPT失败" in str(exc_info.value)
        assert "网络错误" in str(exc_info.value)
        
        # 验证get_ppt_info被调用了（在这里就失败了）
        mock_client.get_ppt_info.assert_called_once_with(ppt_id="test_ppt_123", token='mock_token')


@patch('src.domain.kms.ppt_service.get_aippt_client')
@patch.object(PPTService, '_get_memory')
def test_bind_ppt_to_session_success(mock_get_memory, mock_get_aippt_client):
    """测试绑定PPT到会话成功场景"""
    # Mock AIPPT客户端
    mock_client = Mock()
    mock_get_aippt_client.return_value = mock_client
    
    # Mock PPT信息
    mock_ppt_info = {
        'id': 'test_ppt_456',
        'name': '测试PPT演示文稿'
    }
    mock_client.get_ppt_info.return_value = mock_ppt_info
    
    # Mock Memory SDK
    mock_memory = Mock()
    mock_memory.add_event.return_value = True
    mock_get_memory.return_value = mock_memory
    
    # Mock Redis token
    with patch('src.domain.kms.ppt_service.RedisClient') as mock_redis_client:
        mock_redis = Mock()
        mock_redis.get.return_value = 'mock_token_12345'
        mock_redis_client.return_value = mock_redis
        
        # 执行测试
        service = PPTService()
        result = service.bind_ppt_to_session(
            session_id="test_session_123",
            event_id="test_event_123", 
            ppt_id="test_ppt_456",
            ali_uid=123456
        )
        
        # 验证update_event被调用
        mock_memory.update_event.assert_called_once()
        update_call_args = mock_memory.update_event.call_args
        assert update_call_args[1]['event_id'] == "test_event_123"
        
        # 验证ext_info包含正确的信息
        import json
        ext_info = json.loads(update_call_args[1]['ext_info'])
        assert ext_info['ppt_id'] == "test_ppt_456"
        
        # 验证add_event被调用一次（只有ArtifactEvent）
        assert mock_memory.add_event.call_count == 1
        
        # 验证ArtifactEvent参数
        call_args = mock_memory.add_event.call_args
        artifact_event = call_args[0][0]
        artifact_session_id = call_args[0][1]
        
        assert artifact_session_id == "test_session_123"
        assert artifact_event.session_id == "test_session_123"
        assert artifact_event.artifact_type == "aippt"
        assert artifact_event.file_type == "ppt"
        assert artifact_event.file_name == "测试PPT演示文稿"
        assert artifact_event.content == "test_ppt_456"
        
        # 验证没有返回值（方法没有return语句）
        assert result is None


@patch('src.domain.kms.ppt_service.get_aippt_client')
@patch.object(PPTService, '_get_memory')
def test_bind_ppt_to_session_memory_failure(mock_get_memory, mock_get_aippt_client):
    """测试绑定PPT到会话时Memory失败场景"""
    # Mock AIPPT客户端
    mock_client = Mock()
    mock_get_aippt_client.return_value = mock_client
    
    # Mock PPT信息
    mock_ppt_info = {
        'id': 'test_ppt_456',
        'name': '测试PPT演示文稿'
    }
    mock_client.get_ppt_info.return_value = mock_ppt_info
    
    # Mock Memory SDK失败
    mock_memory = Mock()
    mock_memory.add_event.side_effect = Exception("Memory服务连接失败")
    mock_get_memory.return_value = mock_memory
    
    # Mock Redis token
    with patch('src.domain.kms.ppt_service.RedisClient') as mock_redis_client:
        mock_redis = Mock()
        mock_redis.get.return_value = 'mock_token_12345'
        mock_redis_client.return_value = mock_redis
        
        # 执行测试并验证异常
        service = PPTService()
        with pytest.raises(Exception) as exc_info:
            service.bind_ppt_to_session(
                session_id="test_session_123",
                event_id="test_event_456", 
                ppt_id="test_ppt_456",
                ali_uid=123456
            )
        
        # 验证异常信息
        assert "Memory服务连接失败" in str(exc_info.value)
        
        # 验证Memory被调用（第一次调用就会失败）
        mock_memory.add_event.assert_called_once()


@patch('src.domain.kms.ppt_service.get_aippt_client')
@patch.object(PPTService, '_get_memory')
def test_bind_ppt_to_session_multiple_calls(mock_get_memory, mock_get_aippt_client):
    """测试多次绑定PPT到会话（验证run_id唯一性）"""
    # Mock AIPPT客户端
    mock_client = Mock()
    mock_get_aippt_client.return_value = mock_client
    
    # Mock PPT信息
    mock_ppt_info = {
        'id': 'test_ppt',
        'name': '测试PPT演示文稿'
    }
    mock_client.get_ppt_info.return_value = mock_ppt_info
    
    # Mock Memory SDK
    mock_memory = Mock()
    mock_memory.add_event.return_value = True
    mock_get_memory.return_value = mock_memory
    
    # Mock Redis token
    with patch('src.domain.kms.ppt_service.RedisClient') as mock_redis_client:
        mock_redis = Mock()
        mock_redis.get.return_value = 'mock_token_12345'
        mock_redis_client.return_value = mock_redis
        
        # 执行多次绑定
        service = PPTService()
        service.bind_ppt_to_session("session_1", "event_1", "ppt_1", ali_uid=123456)
        service.bind_ppt_to_session("session_2", "event_2", "ppt_2", ali_uid=123456)
        service.bind_ppt_to_session("session_1", "event_3", "ppt_3", ali_uid=123456)  # 同一session绑定不同ppt
        
        # 验证调用次数（每次绑定调用update_event 1次 + add_event 1次，共3次绑定）
        assert mock_memory.update_event.call_count == 3
        assert mock_memory.add_event.call_count == 3
        
        # 获取所有调用的run_id，验证唯一性（每次绑定生成不同的run_id）
        run_ids = []
        for call in mock_memory.add_event.call_args_list:
            event = call[0][0]
            run_ids.append(event.run_id)
        
        # 验证每次绑定生成不同的run_id
        assert len(set(run_ids)) == 3  # 三次绑定应该有三个不同的run_id
        
        # 验证最后一次调用的参数（第3次绑定的ArtifactEvent）
        last_call_args = mock_memory.add_event.call_args
        last_artifact_event = last_call_args[0][0]
        last_session_id = last_call_args[0][1]
        
        assert last_session_id == "session_1"
        assert last_artifact_event.session_id == "session_1"
        assert last_artifact_event.content == "ppt_3"
        assert last_artifact_event.artifact_type == "aippt"


@patch('src.domain.kms.ppt_service.get_aippt_client')
def test_get_ppt_thumbnail_success(mock_get_aippt_client):
    """测试获取PPT缩略图成功场景"""
    # 设置mock对象
    mock_client = Mock()
    mock_get_aippt_client.return_value = mock_client
    
    # Mock PPT信息（包含封面图URL）
    mock_ppt_info = {
        'id': 'ppt_12345',
        'name': '测试PPT演示文稿',
        'cover_url': 'https://example.com/covers/ppt_12345_cover.jpg'
    }
    mock_client.get_ppt_info.return_value = mock_ppt_info
    
    # Mock Redis token
    with patch('src.domain.kms.ppt_service.RedisClient') as mock_redis_client:
        mock_redis = Mock()
        mock_redis.get.return_value = 'mock_token_12345'
        mock_redis_client.return_value = mock_redis
        
        # 执行测试
        service = PPTService()
        result = service.get_ppt_thumbnail(ppt_id="test_ppt_123", ali_uid=123456)
        
        # 验证结果
        assert result == 'https://example.com/covers/ppt_12345_cover.jpg'
        assert isinstance(result, str)
        assert result.startswith('https://')
        
        # 验证调用链
        mock_client.get_ppt_info.assert_called_once_with(ppt_id="test_ppt_123", token='mock_token_12345')


@patch('src.domain.kms.ppt_service.get_aippt_client')
def test_get_ppt_thumbnail_no_cover_url(mock_get_aippt_client):
    """测试获取PPT缩略图时封面图URL不存在的场景"""
    # 设置mock对象
    mock_client = Mock()
    mock_get_aippt_client.return_value = mock_client
    
    # Mock PPT信息（不包含封面图URL）
    mock_ppt_info = {
        'id': 'ppt_12345',
        'name': '测试PPT演示文稿'
        # 注意：没有 cover_url 字段
    }
    mock_client.get_ppt_info.return_value = mock_ppt_info
    
    # Mock Redis token
    with patch('src.domain.kms.ppt_service.RedisClient') as mock_redis_client:
        mock_redis = Mock()
        mock_redis.get.return_value = 'mock_token_12345'
        mock_redis_client.return_value = mock_redis
        
        # 执行测试
        service = PPTService()
        result = service.get_ppt_thumbnail(ppt_id="test_ppt_123", ali_uid=123456)
        
        # 验证结果（应该返回空字符串）
        assert result == ''
        assert isinstance(result, str)
        
        # 验证调用链
        mock_client.get_ppt_info.assert_called_once_with(ppt_id="test_ppt_123", token='mock_token_12345')


@patch('src.domain.kms.ppt_service.get_aippt_client')
def test_get_ppt_thumbnail_empty_cover_url(mock_get_aippt_client):
    """测试获取PPT缩略图时封面图URL为空的场景"""
    # 设置mock对象
    mock_client = Mock()
    mock_get_aippt_client.return_value = mock_client
    
    # Mock PPT信息（封面图URL为空）
    mock_ppt_info = {
        'id': 'ppt_12345',
        'name': '测试PPT演示文稿',
        'cover_url': ''  # 空字符串
    }
    mock_client.get_ppt_info.return_value = mock_ppt_info
    
    # Mock Redis token
    with patch('src.domain.kms.ppt_service.RedisClient') as mock_redis_client:
        mock_redis = Mock()
        mock_redis.get.return_value = 'mock_token_12345'
        mock_redis_client.return_value = mock_redis
        
        # 执行测试
        service = PPTService()
        result = service.get_ppt_thumbnail(ppt_id="test_ppt_123", ali_uid=123456)
        
        # 验证结果（应该返回空字符串）
        assert result == ''
        assert isinstance(result, str)
        
        # 验证调用链
        mock_client.get_ppt_info.assert_called_once()


@patch('src.domain.kms.ppt_service.get_aippt_client')
def test_get_ppt_thumbnail_client_error(mock_get_aippt_client):
    """测试获取PPT缩略图时客户端错误场景"""
    # Mock 客户端异常
    mock_client = Mock()
    mock_client.get_ppt_info.side_effect = Exception("网络连接失败")
    mock_get_aippt_client.return_value = mock_client
    
    # Mock Redis token
    with patch('src.domain.kms.ppt_service.RedisClient') as mock_redis_client:
        mock_redis = Mock()
        mock_redis.get.return_value = 'mock_token'
        mock_redis_client.return_value = mock_redis
        
        # 执行测试并验证异常
        service = PPTService()
        with pytest.raises(PPTServiceError) as exc_info:
            service.get_ppt_thumbnail(ppt_id="test_ppt_123", ali_uid=123456)
        
        # 验证异常信息
        assert "获取PPT缩略图失败" in str(exc_info.value)
        assert "网络连接失败" in str(exc_info.value)


@patch('src.domain.kms.ppt_service.get_aippt_client')
def test_get_ppt_thumbnail_token_failure(mock_get_aippt_client):
    """测试获取PPT缩略图时token获取失败场景"""
    # 设置mock对象
    mock_client = Mock()
    mock_get_aippt_client.return_value = mock_client
    
    # Mock Redis token失败（返回None）
    with patch('src.domain.kms.ppt_service.RedisClient') as mock_redis_client:
        mock_redis = Mock()
        mock_redis.get.return_value = None  # 缓存中没有token
        mock_redis_client.return_value = mock_redis
        
        # Mock token获取也失败
        with patch.object(PPTService, '_get_aippt_token') as mock_get_token:
            mock_get_token.side_effect = Exception("Token获取失败")
            
            # 执行测试并验证异常
            service = PPTService()
            with pytest.raises(PPTServiceError) as exc_info:
                service.get_ppt_thumbnail(ppt_id="test_ppt_123", ali_uid=123456)
            
            # 验证异常信息
            assert "获取PPT缩略图失败" in str(exc_info.value)
            assert "Token获取失败" in str(exc_info.value)


@patch('src.domain.kms.ppt_service.get_aippt_client')
def test_delete_ppt_success(mock_get_aippt_client):
    """测试删除PPT成功场景"""
    # 设置mock对象
    mock_client = Mock()
    mock_get_aippt_client.return_value = mock_client
    
    # Mock 删除PPT成功
    mock_client.delete_ppt.return_value = True
    
    # Mock Redis token
    with patch('src.domain.kms.ppt_service.RedisClient') as mock_redis_client:
        mock_redis = Mock()
        mock_redis.get.return_value = 'mock_token_12345'
        mock_redis_client.return_value = mock_redis
        
        # 执行测试
        service = PPTService()
        result = service.delete_ppt(ppt_id="test_ppt_123", ali_uid=123456)
        
        # 验证结果
        assert result is True
        assert isinstance(result, bool)
        
        # 验证调用链
        mock_client.delete_ppt.assert_called_once_with(ppt_id="test_ppt_123", token='mock_token_12345')


@patch('src.domain.kms.ppt_service.get_aippt_client')
def test_delete_ppt_client_failure(mock_get_aippt_client):
    """测试删除PPT时客户端返回失败的场景"""
    # 设置mock对象
    mock_client = Mock()
    mock_get_aippt_client.return_value = mock_client
    
    # Mock 删除PPT失败
    mock_client.delete_ppt.return_value = False
    
    # Mock Redis token
    with patch('src.domain.kms.ppt_service.RedisClient') as mock_redis_client:
        mock_redis = Mock()
        mock_redis.get.return_value = 'mock_token_12345'
        mock_redis_client.return_value = mock_redis
        
        # 执行测试并验证异常
        service = PPTService()
        with pytest.raises(PPTServiceError) as exc_info:
            service.delete_ppt(ppt_id="test_ppt_123", ali_uid=123456)
        
        # 验证异常信息
        assert "PPT删除失败" in str(exc_info.value)
        
        # 验证调用链
        mock_client.delete_ppt.assert_called_once_with(ppt_id="test_ppt_123", token='mock_token_12345')


@patch('src.domain.kms.ppt_service.get_aippt_client')
def test_delete_ppt_client_error(mock_get_aippt_client):
    """测试删除PPT时客户端异常场景"""
    # Mock 客户端异常
    mock_client = Mock()
    mock_client.delete_ppt.side_effect = Exception("网络连接失败")
    mock_get_aippt_client.return_value = mock_client
    
    # Mock Redis token
    with patch('src.domain.kms.ppt_service.RedisClient') as mock_redis_client:
        mock_redis = Mock()
        mock_redis.get.return_value = 'mock_token_12345'
        mock_redis_client.return_value = mock_redis
        
        # 执行测试并验证异常
        service = PPTService()
        with pytest.raises(PPTServiceError) as exc_info:
            service.delete_ppt(ppt_id="test_ppt_123", ali_uid=123456)
        
        # 验证异常信息
        assert "删除PPT失败" in str(exc_info.value)
        assert "网络连接失败" in str(exc_info.value)
        
        # 验证调用链
        mock_client.delete_ppt.assert_called_once_with(ppt_id="test_ppt_123", token='mock_token_12345')


@patch('src.domain.kms.ppt_service.get_aippt_client')
def test_delete_ppt_token_failure(mock_get_aippt_client):
    """测试删除PPT时token获取失败场景"""
    # 设置mock对象
    mock_client = Mock()
    mock_get_aippt_client.return_value = mock_client
    
    # Mock Redis token失败（返回None）
    with patch('src.domain.kms.ppt_service.RedisClient') as mock_redis_client:
        mock_redis = Mock()
        mock_redis.get.return_value = None  # 缓存中没有token
        mock_redis_client.return_value = mock_redis
        
        # Mock token获取也失败
        with patch.object(PPTService, '_get_aippt_token') as mock_get_token:
            mock_get_token.side_effect = Exception("Token获取失败")
            
            # 执行测试并验证异常
            service = PPTService()
            with pytest.raises(PPTServiceError) as exc_info:
                service.delete_ppt(ppt_id="test_ppt_123", ali_uid=123456)
            
            # 验证异常信息
            assert "删除PPT失败" in str(exc_info.value)
            assert "Token获取失败" in str(exc_info.value)


@patch('src.domain.kms.ppt_service.get_aippt_client')
def test_delete_ppt_with_different_ppt_ids(mock_get_aippt_client):
    """测试删除不同PPT ID的场景"""
    # 设置mock对象
    mock_client = Mock()
    mock_get_aippt_client.return_value = mock_client
    
    # Mock 删除PPT都成功
    mock_client.delete_ppt.return_value = True
    
    # Mock Redis token
    with patch('src.domain.kms.ppt_service.RedisClient') as mock_redis_client:
        mock_redis = Mock()
        mock_redis.get.return_value = 'mock_token_12345'
        mock_redis_client.return_value = mock_redis
        
        # 执行多次删除测试
        service = PPTService()
        
        # 测试不同的PPT ID
        test_ppt_ids = ["ppt_123", "ppt_456", "ppt_789", "special_ppt_xyz"]
        
        for ppt_id in test_ppt_ids:
            result = service.delete_ppt(ppt_id=ppt_id, ali_uid=123456)
            assert result is True
        
        # 验证调用次数
        assert mock_client.delete_ppt.call_count == len(test_ppt_ids)
        
        # 验证每次调用的参数
        call_args_list = mock_client.delete_ppt.call_args_list
        for i, ppt_id in enumerate(test_ppt_ids):
            call_args = call_args_list[i]
            assert call_args.kwargs['ppt_id'] == ppt_id
            assert call_args.kwargs['token'] == 'mock_token_12345'

@patch.object(PPTService, 'nacos_config_manager')
def test_get_ppt_config_list_from_nacos_success(mock_nacos_manager):
    """测试从配置中心成功获取PPT配置清单"""
    # Mock配置中心返回的数据
    mock_config_data = {
        "pptConfig": {
            "pageRangeOptions": ["5-10", "10-15", "15-20", "20-30", "30-35", "35-40", "不限"],
            "audienceOptions": ["大众", "投资者", "学生", "老师", "老板", "面试官", "同事同行", "在线访客", "组员"],
            "scenarioOptions": ["通用", "个人介绍", "商业计划书", "解决方案", "会议流程", "年度计划", "年度总结", "健康科普", "财务报告", "项目计划书", "商业博文"],
            "toneOptions": ["专业", "励志", "幽默", "亲切", "自信", "温柔"],
            "languageOptions": ["简体中文", "英文", "日语"]
        }
    }
    
    # 设置mock返回值
    mock_nacos_manager.get_config.return_value = mock_config_data
    
    # 执行测试
    service = PPTService()
    config_data = service.get_ppt_config_list()
    
    # 验证结果
    assert config_data is not None
    assert hasattr(config_data, 'page_range_options')
    assert hasattr(config_data, 'audience_options')
    assert hasattr(config_data, 'scenario_options')
    assert hasattr(config_data, 'tone_options')
    assert hasattr(config_data, 'language_options')
    
    # 验证每个选项都有标题和项目列表
    assert config_data.page_range_options.title == "页数范围"
    assert config_data.page_range_options.items == ["5-10", "10-15", "15-20", "20-30", "30-35", "35-40", "不限"]
    
    assert config_data.audience_options.title == "受众"
    assert config_data.audience_options.items == ["大众", "投资者", "学生", "老师", "老板", "面试官", "同事同行", "在线访客", "组员"]
    
    assert config_data.scenario_options.title == "场景"
    assert "通用" in config_data.scenario_options.items
    assert "个人介绍" in config_data.scenario_options.items
    
    assert config_data.tone_options.title == "语气"
    assert "专业" in config_data.tone_options.items
    
    assert config_data.language_options.title == "语言"
    assert "简体中文" in config_data.language_options.items
    
    # 验证调用参数
    mock_nacos_manager.get_config.assert_called_once_with(
        data_id="wuying-alpha-service:aippt_config",
        group="DEFAULT_GROUP"
    )
    
    print("✅ 从配置中心获取PPT配置清单测试通过")


@patch.object(PPTService, 'nacos_config_manager')
def test_get_ppt_config_list_nacos_empty_fallback(mock_nacos_manager):
    """测试配置中心返回空数据时使用降级配置"""
    # Mock配置中心返回空数据
    mock_nacos_manager.get_config.return_value = {}
    
    # 执行测试
    service = PPTService()
    config_data = service.get_ppt_config_list()
    
    # 验证使用了降级配置
    assert config_data is not None
    assert config_data.page_range_options.title == "页数范围"
    assert len(config_data.page_range_options.items) > 0
    assert "不限" in config_data.page_range_options.items
    
    assert config_data.scenario_options.title == "场景"
    assert "通用" in config_data.scenario_options.items
    
    print("✅ 配置中心空数据降级测试通过")


@patch.object(PPTService, 'nacos_config_manager')
def test_get_ppt_config_list_nacos_error_fallback(mock_nacos_manager):
    """测试配置中心异常时使用降级配置"""
    # Mock配置中心抛异常
    mock_nacos_manager.get_config.side_effect = Exception("配置中心连接失败")
    
    # 执行测试
    service = PPTService()
    config_data = service.get_ppt_config_list()
    
    # 验证使用了降级配置
    assert config_data is not None
    assert config_data.page_range_options.title == "页数范围"
    assert len(config_data.page_range_options.items) == 7  # 预期的降级配置数量
    
    assert config_data.audience_options.title == "受众"
    assert len(config_data.audience_options.items) == 9
    
    assert config_data.scenario_options.title == "场景"
    assert len(config_data.scenario_options.items) == 11  # 包含"通用"
    
    assert config_data.tone_options.title == "语气"
    assert len(config_data.tone_options.items) == 6
    
    assert config_data.language_options.title == "语言"
    assert len(config_data.language_options.items) == 3
    
    print("✅ 配置中心异常降级测试通过")


@patch.object(PPTService, 'nacos_config_manager')
def test_get_ppt_config_list_partial_nacos_data(mock_nacos_manager):
    """测试配置中心返回部分数据的处理"""
    # Mock配置中心返回不完整的数据
    mock_config_data = {
        "pptConfig": {
            "pageRangeOptions": ["5-10", "10-15"],
            "audienceOptions": ["大众", "投资者"],
            # 缺少其他配置项
        }
    }
    
    mock_nacos_manager.get_config.return_value = mock_config_data
    
    # 执行测试
    service = PPTService()
    config_data = service.get_ppt_config_list()
    
    # 验证部分数据正确解析
    assert config_data.page_range_options.items == ["5-10", "10-15"]
    assert config_data.audience_options.items == ["大众", "投资者"]
    
    # 验证缺失的配置项返回空列表
    assert config_data.scenario_options.items == []
    assert config_data.tone_options.items == []
    assert config_data.language_options.items == []
    
    print("✅ 配置中心部分数据测试通过")


def test_get_ppt_config_list_fallback_only():
    """测试仅使用降级配置的情况"""
    service = PPTService()
    
    # 执行测试
    config_data = service.get_ppt_config_list()
    
    # 验证降级配置的完整性
    assert config_data is not None
    assert hasattr(config_data, 'page_range_options')
    assert hasattr(config_data, 'audience_options')
    assert hasattr(config_data, 'scenario_options')
    assert hasattr(config_data, 'tone_options')
    assert hasattr(config_data, 'language_options')
    
    # 验证降级配置的具体内容
    assert config_data.page_range_options.title == "页数范围"
    assert "5-10" in config_data.page_range_options.items
    assert "不限" in config_data.page_range_options.items
    
    assert config_data.scenario_options.title == "场景"
    assert "通用" in config_data.scenario_options.items
    assert "个人介绍" in config_data.scenario_options.items
    
    assert config_data.language_options.title == "语言"
    assert "简体中文" in config_data.language_options.items
    assert "英文" in config_data.language_options.items
    assert "日语" in config_data.language_options.items
    
    print("✅ 降级配置测试通过")


@patch.object(PPTService, 'nacos_config_manager')
def test_get_ppt_config_list_invalid_nacos_data_structure(mock_nacos_manager):
    """测试配置中心返回无效数据结构的处理"""
    # Mock配置中心返回错误结构的数据
    mock_config_data = {
        "invalidKey": {
            "someData": "value"
        }
        # 缺少 pptConfig 节点
    }
    
    mock_nacos_manager.get_config.return_value = mock_config_data
    
    # 执行测试
    service = PPTService()
    config_data = service.get_ppt_config_list()
    
    # 验证使用了降级配置
    assert config_data is not None
    assert config_data.page_range_options.title == "页数范围"
    assert len(config_data.page_range_options.items) == 7  # 降级配置数量
    
    print("✅ 配置中心无效数据结构测试通过")


@patch.object(PPTService, 'nacos_config_manager')
def test_get_ppt_config_list_nacos_none_response(mock_nacos_manager):
    """测试配置中心返回None的处理"""
    # Mock配置中心返回None
    mock_nacos_manager.get_config.return_value = None
    
    # 执行测试
    service = PPTService()
    config_data = service.get_ppt_config_list()
    
    # 验证使用了降级配置
    assert config_data is not None
    assert config_data.language_options.title == "语言"
    assert "简体中文" in config_data.language_options.items
    
    print("✅ 配置中心None响应测试通过")


def test_ppt_service_constants():
    """测试PPTService类的常量定义"""
    # 验证常量存在且值正确
    assert hasattr(PPTService, 'PPT_CONFIG_DATA_ID')
    assert hasattr(PPTService, 'PPT_CONFIG_GROUP')
    
    assert PPTService.PPT_CONFIG_DATA_ID == "wuying-alpha-service:aippt_config"
    assert PPTService.PPT_CONFIG_GROUP == "DEFAULT_GROUP"
    
    print("✅ PPTService常量测试通过")


def test_ppt_service_nacos_manager_initialization():
    """测试PPTService中nacos_config_manager的初始化"""
    service = PPTService()
    
    # 验证nacos_config_manager被正确初始化
    assert hasattr(service, 'nacos_config_manager')
    assert service.nacos_config_manager is not None
    
    print("✅ PPTService nacos_config_manager初始化测试通过")


@patch.object(PPTService, 'nacos_config_manager')
def test_get_ppt_config_list_with_extra_nacos_fields(mock_nacos_manager):
    """测试配置中心返回额外字段的处理"""
    # Mock配置中心返回包含额外字段的数据
    mock_config_data = {
        "pptConfig": {
            "pageRangeOptions": ["5-10", "10-15"],
            "audienceOptions": ["大众", "投资者"],
            "scenarioOptions": ["通用", "个人介绍"],
            "toneOptions": ["专业", "幽默"],
            "languageOptions": ["简体中文", "英文"],
            # 额外的字段
            "extraField1": ["extra1", "extra2"],
            "extraField2": "extra_value",
            "extraField3": {"nested": "data"}
        },
        # 额外的根节点
        "extraRootField": "extra_root_value"
    }
    
    mock_nacos_manager.get_config.return_value = mock_config_data
    
    # 执行测试
    service = PPTService()
    config_data = service.get_ppt_config_list()
    
    # 验证正确解析了已知字段，忽略了额外字段
    assert config_data.page_range_options.items == ["5-10", "10-15"]
    assert config_data.audience_options.items == ["大众", "投资者"]
    assert config_data.scenario_options.items == ["通用", "个人介绍"]
    assert config_data.tone_options.items == ["专业", "幽默"]
    assert config_data.language_options.items == ["简体中文", "英文"]
    
    print("✅ 配置中心额外字段处理测试通过")


def __main__():
    test_get_aippt_token()

if __name__ == "__main__":
    __main__()