#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试验证直接数据库更新功能
"""

import pytest
from unittest.mock import patch, Mock


class TestDirectDBUpdate:
    """测试直接数据库更新功能"""

    def test_session_db_service_import(self):
        """测试数据库服务导入"""
        try:
            from src.infrastructure.database.repositories.session_repository import session_db_service
            assert session_db_service is not None
            assert hasattr(session_db_service, 'update_session_status')
            print("✅ session_db_service 导入成功")
        except ImportError as e:
            pytest.fail(f"❌ 无法导入 session_db_service: {e}")

    def test_update_session_status_method_exists(self):
        """测试 update_session_status 方法存在"""
        from src.infrastructure.database.repositories.session_repository import SessionDatabaseService
        
        db_service = SessionDatabaseService()
        assert hasattr(db_service, 'update_session_status')
        
        # 检查方法签名
        import inspect
        sig = inspect.signature(db_service.update_session_status)
        params = list(sig.parameters.keys())
        
        assert 'session_id' in params
        assert 'status' in params
        print("✅ update_session_status 方法签名正确")

    def test_message_processor_import(self):
        """测试 MessageProcessor 导入"""
        try:
            from src.domain.services.message_processor import MessageProcessor
            processor = MessageProcessor()
            assert processor is not None
            print("✅ MessageProcessor 导入成功")
        except ImportError as e:
            pytest.fail(f"❌ 无法导入 MessageProcessor: {e}")

    def test_direct_database_call_logic(self):
        """测试直接数据库调用逻辑"""
        # 模拟数据库更新逻辑
        def mock_update_session_status(session_id: str, status: str) -> bool:
            """模拟数据库更新方法"""
            if not session_id or not status:
                return False
            if status not in ['CREATE', 'ACTIVE', 'CLOSED']:
                return False
            return True
        
        # 测试正常情况
        result1 = mock_update_session_status("test_session", "CLOSED")
        assert result1 is True
        
        # 测试异常情况
        result2 = mock_update_session_status("", "CLOSED")
        assert result2 is False
        
        result3 = mock_update_session_status("test_session", "INVALID")
        assert result3 is False
        
        print("✅ 数据库更新逻辑测试通过")

    def test_message_processor_modification_syntax(self):
        """测试 MessageProcessor 修改后的语法正确性"""
        # 验证修改后的代码语法
        code_snippet = '''
# 直接调用数据库方法更新会话状态为CLOSED
from ...infrastructure.database.repositories.session_repository import session_db_service
try:
    success = session_db_service.update_session_status(session_id, 'CLOSED')
    if success:
        logger.info(f"[MessageProcessor] Session状态已直接更新到数据库: session_id={session_id} -> CLOSED")
    else:
        logger.warning(f"[MessageProcessor] Session状态更新失败: session_id={session_id}")
except Exception as e:
    logger.error(f"[MessageProcessor] 更新Session状态到数据库失败: session_id={session_id}, error={e}")
'''
        
        # 检查代码是否包含关键元素
        assert 'session_db_service.update_session_status' in code_snippet
        assert 'CLOSED' in code_snippet
        assert 'try:' in code_snippet
        assert 'except Exception' in code_snippet
        
        print("✅ MessageProcessor 修改语法正确")

    def test_status_values(self):
        """测试状态值的正确性"""
        valid_statuses = ['CREATE', 'ACTIVE', 'CLOSED']
        
        # 验证 CLOSED 状态是有效的
        assert 'CLOSED' in valid_statuses
        
        # 验证状态字符串格式
        for status in valid_statuses:
            assert isinstance(status, str)
            assert status.isupper()
        
        print("✅ 状态值验证通过")


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
