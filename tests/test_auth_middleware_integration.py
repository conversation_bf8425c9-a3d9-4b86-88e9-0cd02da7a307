# # -*- coding: utf-8 -*-
# """
# 测试AuthMiddleware与LoginVerifyClient的集成
# """
# import pytest
# import asyncio
# from unittest.mock import Mock, patch, AsyncMock
# from fastapi import Request
# from fastapi.security import HTTPAuthorizationCredentials
#
# from src.presentation.middleware.auth_middleware import AuthMiddleware
# from src.domain.kms.auth_service import AuthContext
# from alibabacloud_wuyingaiinner20250718.models import (
#     VerifyLoginTokenResponse,
#     VerifyLoginTokenResponseBody,
#     VerifyLoginTokenResponseBodyData,
#     VerifyLoginTokenResponseBodyDataExtras
# )
#
#
# class TestAuthMiddlewareIntegration:
#     """测试AuthMiddleware与LoginVerifyClient的集成"""
#
#     def setup_method(self):
#         """测试前的设置"""
#         self.middleware = AuthMiddleware()
#
#     def create_mock_request(self, query_params=None, headers=None):
#         """创建模拟请求对象"""
#         request = Mock(spec=Request)
#         request.query_params = Mock()
#         request.headers = Mock()
#
#         # 设置查询参数
#         if query_params:
#             for key, value in query_params.items():
#                 request.query_params.get = Mock(side_effect=lambda k, default=None: query_params.get(k, default))
#         else:
#             request.query_params.get = Mock(return_value=None)
#
#         # 设置请求头
#         if headers:
#             for key, value in headers.items():
#                 request.headers.get = Mock(side_effect=lambda k, default=None: headers.get(k, default))
#         else:
#             request.headers.get = Mock(return_value=None)
#
#         return request
#
#     def create_mock_verify_response(self, success=True, ali_uid=*********, wy_id="test-wy-id"):
#         """创建模拟验证响应"""
#         response = VerifyLoginTokenResponse()
#         response.body = VerifyLoginTokenResponseBody()
#         response.body.success = success
#         response.body.code = "200" if success else "400"
#         response.body.message = "Success" if success else "Invalid token"
#
#         if success:
#             data = VerifyLoginTokenResponseBodyData()
#             data.ali_uid = ali_uid
#             data.wy_id = wy_id
#             data.end_user_id = "test-user"
#             data.account_type = "ALIYUN"
#             data.login_type = "PASSWORD"
#             data.api_key_id = "test-api-key"
#
#             extras = VerifyLoginTokenResponseBodyDataExtras()
#             extras.model = "test-model"
#             data.extras = extras
#
#             response.body.data = data
#
#         return response
#
#     @patch('src.presentation.middleware.auth_middleware.LoginVerifyClient')
#     async def test_get_current_user_with_login_token_success(self, mock_client_class):
#         """测试使用登录令牌成功获取用户"""
#         # 设置模拟客户端
#         mock_client = Mock()
#         mock_response = self.create_mock_verify_response(success=True)
#         mock_client.verify_login_token_async = AsyncMock(return_value=mock_response)
#         mock_client.get_user_info_from_response.return_value = {
#             'ali_uid': *********,
#             'wy_id': 'test-wy-id',
#             'end_user_id': 'test-user',
#             'account_type': 'ALIYUN',
#             'login_type': 'PASSWORD',
#             'api_key_id': 'test-api-key'
#         }
#         mock_client_class.return_value = mock_client
#
#         # 创建新的中间件实例以使用模拟客户端
#         middleware = AuthMiddleware()
#
#         # 创建带有登录令牌的请求
#         request = self.create_mock_request(
#             query_params={
#                 "loginToken": "test-login-token",
#                 "regionId": "cn-hangzhou"
#             }
#         )
#
#         # 测试获取用户
#         context = await middleware.get_current_user(request)
#
#         # 验证结果
#         assert context is not None
#         assert context.ali_uid == *********
#         assert context.wy_id == "test-wy-id"
#
#         # 验证客户端调用
#         mock_client.verify_login_token_async.assert_called_once_with(
#             session_id="test-login-token",
#             region_id="cn-hangzhou"
#         )
#
#     @patch('src.presentation.middleware.auth_middleware.LoginVerifyClient')
#     async def test_get_current_user_with_session_id_success(self, mock_client_class):
#         """测试使用session_id成功获取用户"""
#         # 设置模拟客户端
#         mock_client = Mock()
#         mock_response = self.create_mock_verify_response(success=True)
#         mock_client.verify_login_token_async = AsyncMock(return_value=mock_response)
#         mock_client.get_user_info_from_response.return_value = {
#             'ali_uid': *********,
#             'wy_id': 'test-wy-id-2',
#             'end_user_id': 'test-user-2',
#             'account_type': 'ALIYUN',
#             'login_type': 'PASSWORD',
#             'api_key_id': 'test-api-key-2'
#         }
#         mock_client_class.return_value = mock_client
#
#         # 创建新的中间件实例
#         middleware = AuthMiddleware()
#
#         # 创建带有session_id的请求
#         request = self.create_mock_request(
#             headers={
#                 "X-Session-Id": "test-session-id",
#                 "X-Region-Id": "cn-beijing"
#             }
#         )
#
#         # 测试获取用户
#         context = await middleware.get_current_user(request)
#
#         # 验证结果
#         assert context is not None
#         assert context.ali_uid == *********
#         assert context.wy_id == "test-wy-id-2"
#
#         # 验证客户端调用
#         mock_client.verify_login_token_async.assert_called_once_with(
#             session_id="test-session-id",
#             region_id="cn-beijing"
#         )
#
#     @patch('src.presentation.middleware.auth_middleware.LoginVerifyClient')
#     async def test_get_current_user_with_token_verification_failure(self, mock_client_class):
#         """测试令牌验证失败的情况"""
#         # 设置模拟客户端
#         mock_client = Mock()
#         mock_response = self.create_mock_verify_response(success=False)
#         mock_client.verify_login_token_async = AsyncMock(return_value=mock_response)
#         mock_client_class.return_value = mock_client
#
#         # 创建新的中间件实例
#         middleware = AuthMiddleware()
#
#         # 创建带有无效令牌的请求
#         request = self.create_mock_request(
#             query_params={"loginToken": "invalid-token"}
#         )
#
#         # 测试获取用户
#         context = await middleware.get_current_user(request)
#
#         # 验证结果 - 应该返回None，然后尝试其他认证方式
#         assert context is None
#
#     async def test_get_current_user_fallback_to_headers(self):
#         """测试回退到Header认证方式"""
#         # 创建带有Header认证信息的请求
#         request = self.create_mock_request(
#             headers={
#                 "X-Ali-Uid": "*********",
#                 "X-Wy-Id": "test-wy-id"
#             }
#         )
#
#         # 测试获取用户
#         context = await self.middleware.get_current_user(request)
#
#         # 验证结果
#         assert context is not None
#         assert context.ali_uid == *********
#         assert context.wy_id == "test-wy-id"
#
#     async def test_get_current_user_fallback_to_query_params(self):
#         """测试回退到查询参数认证方式"""
#         # 创建带有查询参数认证信息的请求
#         request = self.create_mock_request(
#             query_params={
#                 "ali_uid": "*********",
#                 "wy_id": "test-wy-id-query"
#             }
#         )
#
#         # 测试获取用户
#         context = await self.middleware.get_current_user(request)
#
#         # 验证结果
#         assert context is not None
#         assert context.ali_uid == *********
#         assert context.wy_id == "test-wy-id-query"
#
#     async def test_get_current_user_no_auth_info(self):
#         """测试没有任何认证信息的情况"""
#         # 创建空请求
#         request = self.create_mock_request()
#
#         # 测试获取用户
#         context = await self.middleware.get_current_user(request)
#
#         # 验证结果
#         assert context is None
#
#     @patch('src.presentation.middleware.auth_middleware.LoginVerifyClient')
#     async def test_get_current_user_priority_session_over_login_token(self, mock_client_class):
#         """测试session_id优先于loginToken"""
#         # 设置模拟客户端
#         mock_client = Mock()
#         mock_response = self.create_mock_verify_response(success=True)
#         mock_client.verify_login_token_async = AsyncMock(return_value=mock_response)
#         mock_client.get_user_info_from_response.return_value = {
#             'ali_uid': *********,
#             'wy_id': 'test-wy-id',
#             'end_user_id': 'test-user',
#             'account_type': 'ALIYUN',
#             'login_type': 'PASSWORD',
#             'api_key_id': 'test-api-key'
#         }
#         mock_client_class.return_value = mock_client
#
#         # 创建新的中间件实例
#         middleware = AuthMiddleware()
#
#         # 创建同时包含session和loginToken的请求
#         request = self.create_mock_request(
#             query_params={
#                 "session": "session-id-123",
#                 "loginToken": "login-token-456",
#                 "regionId": "cn-hangzhou"
#             }
#         )
#
#         # 测试获取用户
#         context = await middleware.get_current_user(request)
#
#         # 验证结果
#         assert context is not None
#
#         # 验证使用的是session_id而不是loginToken
#         mock_client.verify_login_token_async.assert_called_once_with(
#             session_id="session-id-123",  # 应该使用session而不是loginToken
#             region_id="cn-hangzhou"
#         )
