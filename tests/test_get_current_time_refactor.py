#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试get_current_time函数重构
验证函数从file_models.py移动到time_utils.py后所有引用都正确
"""

import sys
import os
from datetime import datetime, timezone, timedelta
from loguru import logger

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))


def test_time_utils_get_current_time():
    """测试time_utils中的get_current_time函数"""
    logger.info("🧪 测试time_utils.get_current_time函数")
    
    try:
        from src.domain.utils.time_utils import get_current_time
        
        current_time = get_current_time()
        logger.info(f"✅ time_utils.get_current_time(): {current_time}")
        logger.info(f"   时区信息: {current_time.tzinfo}")
        
        # 验证是北京时间（UTC+8）
        beijing_tz = timezone(timedelta(hours=8))
        assert current_time.tzinfo == beijing_tz, f"时区应该是UTC+8，实际为: {current_time.tzinfo}"
        
        logger.info("🎉 time_utils.get_current_time函数测试通过！")
        return True
        
    except Exception as e:
        logger.error(f"❌ time_utils.get_current_time函数测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def test_file_models_import():
    """测试file_models中的导入和使用"""
    logger.info("\n🧪 测试file_models中的导入和使用")
    
    try:
        from src.infrastructure.database.models.file_models import AlphaFile, get_current_time
        
        # 创建文件对象
        file_obj = AlphaFile(
            title="test_file.txt",
            oss_bucket="test-bucket",
            oss_object_name="test/file.txt",
            type="sessionFile"
        )
        
        # 测试mark_completed方法（应该使用导入的get_current_time）
        file_obj.mark_completed(file_size=1024)
        completed_time = file_obj.gmt_modified
        logger.info(f"✅ 文件完成时间: {completed_time}")
        logger.info(f"   时区信息: {completed_time.tzinfo}")
        
        # 验证是北京时间
        beijing_tz = timezone(timedelta(hours=8))
        assert completed_time.tzinfo == beijing_tz, f"完成时间应该是北京时间，实际为: {completed_time.tzinfo}"
        
        # 测试mark_failed方法
        file_obj.mark_failed("测试错误")
        failed_time = file_obj.gmt_modified
        logger.info(f"✅ 文件失败时间: {failed_time}")
        logger.info(f"   时区信息: {failed_time.tzinfo}")
        
        # 验证是北京时间
        assert failed_time.tzinfo == beijing_tz, f"失败时间应该是北京时间，实际为: {failed_time.tzinfo}"
        
        logger.info("🎉 file_models导入和使用测试通过！")
        return True
        
    except Exception as e:
        logger.error(f"❌ file_models导入和使用测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def test_auth_models_import():
    """测试auth_models中的导入"""
    logger.info("\n🧪 测试auth_models中的导入")
    
    try:
        # 测试能否正确导入get_current_time
        from src.infrastructure.database.models.auth_models import get_current_time
        
        current_time = get_current_time()
        logger.info(f"✅ auth_models.get_current_time(): {current_time}")
        logger.info(f"   时区信息: {current_time.tzinfo}")
        
        # 验证是北京时间
        beijing_tz = timezone(timedelta(hours=8))
        assert current_time.tzinfo == beijing_tz, f"时区应该是UTC+8，实际为: {current_time.tzinfo}"
        
        logger.info("🎉 auth_models导入测试通过！")
        return True
        
    except Exception as e:
        logger.error(f"❌ auth_models导入测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def test_repositories_import():
    """测试repositories中的导入"""
    logger.info("\n🧪 测试repositories中的导入")
    
    try:
        # 测试file_repository的导入
        from src.infrastructure.database.repositories.file_repository import FileRepository
        logger.info("✅ file_repository导入成功")
        
        # 测试auth_repository的导入
        from src.infrastructure.database.repositories.auth_repository import AuthRepository
        logger.info("✅ auth_repository导入成功")
        
        logger.info("🎉 repositories导入测试通过！")
        return True
        
    except Exception as e:
        logger.error(f"❌ repositories导入测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def test_auth_service_import():
    """测试auth_service中的导入"""
    logger.info("\n🧪 测试auth_service中的导入")
    
    try:
        # 测试auth_service的导入
        from src.domain.services.auth_service import AuthService
        logger.info("✅ auth_service导入成功")
        
        logger.info("🎉 auth_service导入测试通过！")
        return True
        
    except Exception as e:
        logger.error(f"❌ auth_service导入测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def test_function_consistency():
    """测试函数一致性 - 确保所有地方使用的是同一个函数"""
    logger.info("\n🧪 测试函数一致性")
    
    try:
        from src.domain.utils.time_utils import get_current_time as time_utils_func
        from src.infrastructure.database.models.file_models import get_current_time as file_models_func
        from src.infrastructure.database.models.auth_models import get_current_time as auth_models_func
        
        # 验证所有导入的函数都是同一个
        assert time_utils_func is file_models_func, "file_models中的get_current_time应该与time_utils中的是同一个函数"
        assert time_utils_func is auth_models_func, "auth_models中的get_current_time应该与time_utils中的是同一个函数"
        
        # 测试函数调用结果的一致性
        time1 = time_utils_func()
        time2 = file_models_func()
        time3 = auth_models_func()
        
        logger.info(f"✅ time_utils函数结果: {time1}")
        logger.info(f"✅ file_models函数结果: {time2}")
        logger.info(f"✅ auth_models函数结果: {time3}")
        
        # 验证时区一致性
        beijing_tz = timezone(timedelta(hours=8))
        assert time1.tzinfo == beijing_tz, "time_utils函数应该返回北京时间"
        assert time2.tzinfo == beijing_tz, "file_models函数应该返回北京时间"
        assert time3.tzinfo == beijing_tz, "auth_models函数应该返回北京时间"
        
        logger.info("🎉 函数一致性测试通过！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 函数一致性测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def main():
    """主测试函数"""
    logger.info("🚀 开始测试get_current_time函数重构")
    
    success1 = test_time_utils_get_current_time()
    success2 = test_file_models_import()
    success3 = test_auth_models_import()
    success4 = test_repositories_import()
    success5 = test_auth_service_import()
    success6 = test_function_consistency()
    
    logger.info("\n" + "=" * 60)
    if all([success1, success2, success3, success4, success5, success6]):
        logger.info("🎉 所有测试通过！")
        
        logger.info("\n📋 重构总结:")
        logger.info("1. ✅ get_current_time函数已从file_models.py移动到time_utils.py")
        logger.info("2. ✅ 所有相关文件的导入已更新")
        logger.info("3. ✅ 函数功能保持一致，返回北京时间")
        logger.info("4. ✅ 所有模块都能正确导入和使用")
        logger.info("5. ✅ 代码结构更加合理和清晰")
        
        logger.info("\n🎯 重构效果:")
        logger.info("- 函数位置更合理：时间工具函数放在time_utils.py中")
        logger.info("- 代码组织更清晰：相关功能集中管理")
        logger.info("- 维护性更好：统一的时间处理逻辑")
        
        logger.info("\n📝 修改文件:")
        logger.info("- src/domain/utils/time_utils.py - 添加get_current_time函数")
        logger.info("- src/infrastructure/database/models/file_models.py - 移除函数，添加导入")
        logger.info("- src/infrastructure/database/models/auth_models.py - 移除函数，添加导入")
        logger.info("- src/infrastructure/database/repositories/file_repository.py - 更新导入")
        logger.info("- src/infrastructure/database/repositories/auth_repository.py - 更新导入")
        logger.info("- src/domain/kms/auth_service.py - 更新导入")
        
        return True
    else:
        logger.error("❌ 部分测试失败")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
