#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的 RAG 状态查询接口
"""

import asyncio
import unittest
from unittest.mock import Mock, patch
import sys
import os
from datetime import datetime

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.domain.services.file_service import FileService
from src.domain.services.auth_service import AuthContext
from src.infrastructure.database.models.file_models import AlphaFile, UploadStatus
from src.application.api_models import RagStatusResponse


class TestRagStatusFix(unittest.TestCase):
    """测试修复后的 RAG 状态查询功能"""
    
    def setUp(self):
        """测试前准备"""
        self.file_service = FileService()
        self.auth_context = AuthContext(
            user_key="test_user",
            ali_uid=123456,
            wy_id="test_wy_id"
        )
        
    def create_mock_file(self, file_id: int, upload_status: str, **kwargs) -> Mock:
        """创建模拟文件对象"""
        mock_file = Mock(spec=AlphaFile)
        mock_file.id = file_id
        mock_file.ali_uid = self.auth_context.ali_uid
        mock_file.wy_id = self.auth_context.wy_id
        mock_file.upload_status = upload_status
        mock_file.title = kwargs.get('title', 'test_file.pdf')
        mock_file.error_message = kwargs.get('error_message', None)
        return mock_file
    
    @patch('src.domain.kms.file_service.file_repository')
    async def test_get_rag_status_returns_correct_structure(self, mock_repo):
        """测试 get_rag_status 返回正确的数据结构"""
        # 准备测试数据
        file_id = 1
        mock_file = self.create_mock_file(
            file_id=file_id,
            upload_status=UploadStatus.ANALYZING.value,
            title="test_document.pdf"
        )
        mock_repo.get_file_by_id.return_value = mock_file
        
        # 执行测试
        result = await self.file_service.get_rag_status(self.auth_context, file_id)
        
        # 验证返回结构
        self.assertIsNotNone(result)
        self.assertIn('file_id', result)
        self.assertIn('file_name', result)
        self.assertIn('status', result)
        self.assertIn('message', result)
        
        # 验证具体值
        self.assertEqual(result['file_id'], str(file_id))
        self.assertEqual(result['file_name'], 'test_document.pdf')
        self.assertEqual(result['status'], UploadStatus.ANALYZING.value)
        self.assertIsNone(result['message'])
    
    def test_rag_status_response_model_validation(self):
        """测试 RagStatusResponse 模型验证"""
        # 测试正确的数据结构
        valid_data = {
            "file_id": "123",
            "file_name": "test.pdf",
            "status": "analyzing",
            "message": None
        }
        
        # 应该能够成功创建响应对象
        response = RagStatusResponse(**valid_data)
        self.assertEqual(response.file_id, "123")
        self.assertEqual(response.file_name, "test.pdf")
        self.assertEqual(response.status, "analyzing")
        self.assertIsNone(response.message)
    
    def test_rag_status_response_with_error_message(self):
        """测试带错误信息的 RagStatusResponse"""
        data_with_error = {
            "file_id": "456",
            "file_name": "failed.pdf",
            "status": "failed",
            "message": "解析失败：文件格式不支持"
        }
        
        response = RagStatusResponse(**data_with_error)
        self.assertEqual(response.file_id, "456")
        self.assertEqual(response.status, "failed")
        self.assertEqual(response.message, "解析失败：文件格式不支持")
    
    @patch('src.domain.kms.file_service.file_repository')
    async def test_different_upload_statuses(self, mock_repo):
        """测试不同的上传状态"""
        file_id = 1
        
        # 测试各种状态
        test_cases = [
            (UploadStatus.UPLOADING.value, "uploading"),
            (UploadStatus.ANALYZING.value, "analyzing"),
            (UploadStatus.COMPLETED.value, "completed"),
            (UploadStatus.FAILED.value, "failed")
        ]
        
        for upload_status, expected_status in test_cases:
            with self.subTest(upload_status=upload_status):
                mock_file = self.create_mock_file(file_id, upload_status)
                mock_repo.get_file_by_id.return_value = mock_file
                
                result = await self.file_service.get_rag_status(self.auth_context, file_id)
                
                self.assertIsNotNone(result)
                self.assertEqual(result['status'], expected_status)
    
    @patch('src.domain.kms.file_service.file_repository')
    async def test_integration_with_response_model(self, mock_repo):
        """测试与响应模型的集成"""
        # 准备测试数据
        file_id = 1
        mock_file = self.create_mock_file(
            file_id=file_id,
            upload_status=UploadStatus.COMPLETED.value,
            title="completed_file.pdf"
        )
        mock_repo.get_file_by_id.return_value = mock_file
        
        # 获取状态信息
        status_info = await self.file_service.get_rag_status(self.auth_context, file_id)
        
        # 验证能够成功创建响应对象
        response = RagStatusResponse(**status_info)
        self.assertEqual(response.file_id, str(file_id))
        self.assertEqual(response.file_name, "completed_file.pdf")
        self.assertEqual(response.status, "completed")
        self.assertIsNone(response.message)


async def run_async_tests():
    """运行异步测试"""
    print("=" * 60)
    print("RAG 状态查询修复测试")
    print("=" * 60)
    
    test_instance = TestRagStatusFix()
    test_instance.setUp()
    
    print("✓ 测试 get_rag_status 返回结构...")
    await test_instance.test_get_rag_status_returns_correct_structure()
    print("✓ 通过")
    
    print("✓ 测试不同上传状态...")
    await test_instance.test_different_upload_statuses()
    print("✓ 通过")
    
    print("✓ 测试与响应模型集成...")
    await test_instance.test_integration_with_response_model()
    print("✓ 通过")
    
    print("\n" + "=" * 60)
    print("✅ 所有异步测试通过！")
    print("=" * 60)


def run_sync_tests():
    """运行同步测试"""
    print("\n运行同步测试...")
    
    test_instance = TestRagStatusFix()
    test_instance.setUp()
    
    print("✓ 测试 RagStatusResponse 模型验证...")
    test_instance.test_rag_status_response_model_validation()
    print("✓ 通过")
    
    print("✓ 测试带错误信息的响应...")
    test_instance.test_rag_status_response_with_error_message()
    print("✓ 通过")
    
    print("✅ 同步测试通过！")


if __name__ == "__main__":
    # 运行同步测试
    run_sync_tests()
    
    # 运行异步测试
    asyncio.run(run_async_tests())
    
    print("\n🎉 RAG 状态查询修复验证完成！")
    print("\n📋 修复内容:")
    print("1. 修改 RagStatusResponse 模型，匹配 get_rag_status 的返回结构")
    print("2. 简化响应字段：file_id, file_name, status, message")
    print("3. 更新前端文档中的响应格式和代码示例")
    print("4. 移除了不必要的字段：rag_status, upload_status, progress 等")
