#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试POP客户端工厂类的基本功能
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def test_config_reading():
    """测试配置文件读取功能"""
    print("=== 测试配置文件读取 ===")
    
    try:
        from src.shared.config.environments import env_manager
        
        print(f"当前环境: {env_manager.current_env.value}")
        
        # 测试读取各个endpoint配置
        endpoints = {
            "appstream_inner": env_manager.get_config_value("appstream_inner_endpoint"),
            "cloud_storage": env_manager.get_config_value("cloud_storage_endpoint"),
            "pc_inside": env_manager.get_config_value("pc_inside_endpoint"),
            "rag_client": env_manager.get_config_value("rag_client_endpoint"),
            "waiy_infra": env_manager.get_config_value("waiy_infra_endpoint"),
            "login_verify": env_manager.get_config_value("login_verify_endpoint"),
            "aippt": env_manager.get_config_value("aippt_endpoint"),
        }
        
        print("配置的端点:")
        for name, endpoint in endpoints.items():
            print(f"  {name}: {endpoint}")
            
        return True
        
    except Exception as e:
        print(f"配置读取失败: {e}")
        return False


def test_factory_info():
    """测试工厂类信息获取功能"""
    print("\n=== 测试工厂类信息获取 ===")
    
    try:
        from src.popclients.client_factory import PopClientFactory
        
        # 获取客户端信息（不创建实际客户端）
        info = PopClientFactory.get_client_info()
        
        print(f"环境: {info['environment']}")
        print(f"活跃客户端: {info['active_clients']}")
        print("配置的端点:")
        for name, endpoint in info['endpoints'].items():
            print(f"  {name}: {endpoint}")
            
        return True
        
    except Exception as e:
        print(f"工厂类信息获取失败: {e}")
        return False


def test_aippt_client():
    """测试AIPPT客户端（不依赖阿里云SDK）"""
    print("\n=== 测试AIPPT客户端创建 ===")
    
    try:
        from src.popclients.client_factory import PopClientFactory
        
        # AIPPT客户端不依赖阿里云SDK，应该可以正常创建
        aippt_client = PopClientFactory.get_aippt_client()
        print(f"AIPPT客户端创建成功: {type(aippt_client).__name__}")
        
        # 测试客户端信息
        info = PopClientFactory.get_client_info()
        print(f"活跃客户端: {info['active_clients']}")
        
        return True
        
    except Exception as e:
        print(f"AIPPT客户端创建失败: {e}")
        return False


def test_client_reset():
    """测试客户端重置功能"""
    print("\n=== 测试客户端重置功能 ===")
    
    try:
        from src.popclients.client_factory import PopClientFactory
        
        # 先创建AIPPT客户端
        PopClientFactory.get_aippt_client()
        
        info = PopClientFactory.get_client_info()
        print(f"重置前活跃客户端: {info['active_clients']}")
        
        # 重置AIPPT客户端
        PopClientFactory.reset_client('aippt')
        
        info = PopClientFactory.get_client_info()
        print(f"重置aippt后活跃客户端: {info['active_clients']}")
        
        # 重置所有客户端
        PopClientFactory.reset_all_clients()
        
        info = PopClientFactory.get_client_info()
        print(f"重置所有后活跃客户端: {info['active_clients']}")
        
        return True
        
    except Exception as e:
        print(f"客户端重置测试失败: {e}")
        return False


def main():
    """运行所有测试"""
    print("开始测试POP客户端工厂类")
    
    tests = [
        test_config_reading,
        test_factory_info,
        test_aippt_client,
        test_client_reset,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
                print("✅ 测试通过")
            else:
                print("❌ 测试失败")
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return 0
    else:
        print("⚠️  部分测试失败")
        return 1


if __name__ == "__main__":
    sys.exit(main())
