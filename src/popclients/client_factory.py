# -*- coding: utf-8 -*-
"""
POP客户端工厂类
统一管理所有POP客户端的创建和配置，支持分环境配置endpoint
"""
from typing import Optional, Dict, Any
from loguru import logger

from src.shared.config.environments import env_manager


class PopClientFactory:
    """
    POP客户端工厂类
    统一管理所有POP客户端的创建，支持分环境配置endpoint
    """
    
    _instances: Dict[str, Any] = {}
    
    @classmethod
    def get_appstream_inner_client(
        cls,
        endpoint: Optional[str] = None,
        **kwargs
    ) -> 'AppStreamInnerClient':
        """
        获取AppStream内部服务客户端
        
        Args:
            endpoint: 服务端点，如果不提供则从配置文件读取
            **kwargs: 其他配置参数
            
        Returns:
            AppStreamInnerClient: AppStream内部服务客户端实例
        """
        if 'appstream_inner' not in cls._instances:
            from src.popclients.appstream_inner_client import AppStreamInnerClient
            
            if not endpoint:
                endpoint = env_manager.get_config_value("appstream_inner_endpoint")
                
            logger.info(f"创建AppStream内部客户端: endpoint={endpoint}")
            cls._instances['appstream_inner'] = AppStreamInnerClient(
                endpoint=endpoint,
                **kwargs
            )
            
        return cls._instances['appstream_inner']
    
    @classmethod
    def get_cloud_storage_client(
        cls,
        endpoint: Optional[str] = None,
        **kwargs
    ) -> 'CloudStorageClient':
        """
        获取云存储客户端
        
        Args:
            endpoint: 服务端点，如果不提供则从配置文件读取
            **kwargs: 其他配置参数
            
        Returns:
            CloudStorageClient: 云存储客户端实例
        """
        if 'cloud_storage' not in cls._instances:
            from src.popclients.cloud_storage_client import CloudStorageClient
            
            if not endpoint:
                endpoint = env_manager.get_config_value("cloud_storage_endpoint")
                
            logger.info(f"创建云存储客户端: endpoint={endpoint}")
            cls._instances['cloud_storage'] = CloudStorageClient(
                endpoint=endpoint,
                **kwargs
            )
            
        return cls._instances['cloud_storage']
    
    @classmethod
    def get_pc_inside_client(
        cls,
        endpoint: Optional[str] = None,
        **kwargs
    ) -> 'PcInsideClient':
        """
        获取PC内部服务客户端
        
        Args:
            endpoint: 服务端点，如果不提供则从配置文件读取
            **kwargs: 其他配置参数
            
        Returns:
            PcInsideClient: PC内部服务客户端实例
        """
        if 'pc_inside' not in cls._instances:
            from src.popclients.pc_inside_client import PcInsideClient
            
            if not endpoint:
                endpoint = env_manager.get_config_value("pc_inside_endpoint")
                
            logger.info(f"创建PC内部服务客户端: endpoint={endpoint}")
            cls._instances['pc_inside'] = PcInsideClient(
                endpoint=endpoint,
                **kwargs
            )
            
        return cls._instances['pc_inside']
    
    @classmethod
    def get_rag_client(
        cls,
        endpoint: Optional[str] = None,
        **kwargs
    ) -> 'RagClient':
        """
        获取RAG客户端
        
        Args:
            endpoint: 服务端点，如果不提供则从配置文件读取
            **kwargs: 其他配置参数
            
        Returns:
            RagClient: RAG客户端实例
        """
        if 'rag_client' not in cls._instances:
            from src.popclients.rag_client import RagClient
            
            if not endpoint:
                endpoint = env_manager.get_config_value("rag_client_endpoint")
                
            logger.info(f"创建RAG客户端: endpoint={endpoint}")
            cls._instances['rag_client'] = RagClient(
                endpoint=endpoint,
                **kwargs
            )
            
        return cls._instances['rag_client']
    
    @classmethod
    def get_waiy_infra_client(
        cls,
        endpoint: Optional[str] = None,
        **kwargs
    ) -> 'WaiyInfraClient':
        """
        获取无影基础设施客户端
        
        Args:
            endpoint: 服务端点，如果不提供则从配置文件读取
            **kwargs: 其他配置参数
            
        Returns:
            WaiyInfraClient: 无影基础设施客户端实例
        """
        if 'waiy_infra' not in cls._instances:
            from src.popclients.waiy_infra_client import WaiyInfraClient
            
            if not endpoint:
                endpoint = env_manager.get_config_value("waiy_infra_endpoint")
                
            logger.info(f"创建无影基础设施客户端: endpoint={endpoint}")
            cls._instances['waiy_infra'] = WaiyInfraClient(
                endpoint=endpoint,
                **kwargs
            )
            
        return cls._instances['waiy_infra']
    
    @classmethod
    def get_login_verify_client(
        cls,
        endpoint: Optional[str] = None,
        **kwargs
    ) -> 'LoginVerifyClient':
        """
        获取登录验证客户端
        
        Args:
            endpoint: 服务端点，如果不提供则从配置文件读取
            **kwargs: 其他配置参数
            
        Returns:
            LoginVerifyClient: 登录验证客户端实例
        """
        if 'login_verify' not in cls._instances:
            from src.popclients.login_verify_client import LoginVerifyClient
            
            if not endpoint:
                endpoint = env_manager.get_config_value("login_verify_endpoint")
                
            logger.info(f"创建登录验证客户端: endpoint={endpoint}")
            cls._instances['login_verify'] = LoginVerifyClient(
                endpoint=endpoint,
                **kwargs
            )
            
        return cls._instances['login_verify']
    
    @classmethod
    def get_aippt_client(
        cls,
        **kwargs
    ) -> 'AIPPTClient':
        """
        获取AIPPT客户端
        
        Args:
            **kwargs: 其他配置参数
            
        Returns:
            AIPPTClient: AIPPT客户端实例
        """
        if 'aippt' not in cls._instances:
            from src.popclients.aippt_client import AIPPTClient
            
            logger.info("创建AIPPT客户端")
            cls._instances['aippt'] = AIPPTClient(**kwargs)
            
        return cls._instances['aippt']
    
    @classmethod
    def reset_client(cls, client_name: str):
        """
        重置指定客户端实例
        
        Args:
            client_name: 客户端名称
        """
        if client_name in cls._instances:
            del cls._instances[client_name]
            logger.info(f"已重置客户端: {client_name}")
    
    @classmethod
    def reset_all_clients(cls):
        """
        重置所有客户端实例
        """
        cls._instances.clear()
        logger.info("已重置所有客户端实例")
    
    @classmethod
    def get_client_info(cls) -> Dict[str, Any]:
        """
        获取所有客户端的信息
        
        Returns:
            Dict[str, Any]: 客户端信息字典
        """
        info = {
            "environment": env_manager.current_env.value,
            "active_clients": list(cls._instances.keys()),
            "endpoints": {
                "appstream_inner": env_manager.get_config_value("appstream_inner_endpoint"),
                "cloud_storage": env_manager.get_config_value("cloud_storage_endpoint"),
                "pc_inside": env_manager.get_config_value("pc_inside_endpoint"),
                "rag_client": env_manager.get_config_value("rag_client_endpoint"),
                "waiy_infra": env_manager.get_config_value("waiy_infra_endpoint"),
                "login_verify": env_manager.get_config_value("login_verify_endpoint"),
                "aippt": env_manager.get_config_value("aippt_endpoint"),
            }
        }
        return info


# 便捷函数，保持向后兼容
def get_appstream_inner_client(**kwargs):
    """获取AppStream内部服务客户端"""
    return PopClientFactory.get_appstream_inner_client(**kwargs)


def get_cloud_storage_client(**kwargs):
    """获取云存储客户端"""
    return PopClientFactory.get_cloud_storage_client(**kwargs)


def get_pc_inside_client(**kwargs):
    """获取PC内部服务客户端"""
    return PopClientFactory.get_pc_inside_client(**kwargs)


def get_rag_client(**kwargs):
    """获取RAG客户端"""
    return PopClientFactory.get_rag_client(**kwargs)


def get_waiy_infra_client(**kwargs):
    """获取无影基础设施客户端"""
    return PopClientFactory.get_waiy_infra_client(**kwargs)


def get_login_verify_client(**kwargs):
    """获取登录验证客户端"""
    return PopClientFactory.get_login_verify_client(**kwargs)


def get_aippt_client(**kwargs):
    """获取AIPPT客户端"""
    return PopClientFactory.get_aippt_client(**kwargs)
