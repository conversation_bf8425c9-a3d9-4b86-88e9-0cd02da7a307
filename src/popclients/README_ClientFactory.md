# POP客户端工厂模式

## 概述

`PopClientFactory` 是一个统一的客户端工厂类，用于管理所有POP（阿里云产品开放平台）客户端的创建和配置。它支持分环境配置endpoint，提供单例模式管理，并保持向后兼容性。

## 主要特性

- ✅ **统一管理**: 所有POP客户端通过工厂类统一创建和管理
- ✅ **分环境配置**: 支持daily、pre、prod环境的不同endpoint配置
- ✅ **单例模式**: 每种客户端类型只创建一个实例，提高性能
- ✅ **向后兼容**: 保持原有的便捷函数接口
- ✅ **配置灵活**: 支持运行时自定义endpoint
- ✅ **类型安全**: 完整的类型提示支持

## 支持的客户端

| 客户端 | 类名 | 配置键 | 默认端点 |
|--------|------|--------|----------|
| AppStream内部服务 | `AppStreamInnerClient` | `appstream_inner_endpoint` | appstream-center-inner-share.cn-hangzhou.aliyuncs.com |
| 云存储服务 | `CloudStorageClient` | `cloud_storage_endpoint` | eds-storage-inner-share.cn-hangzhou.aliyuncs.com |
| PC内部服务 | `PcInsideClient` | `pc_inside_endpoint` | wuying-pc-inside-vpc-inner-pre-test.cn-hangzhou.aliyuncs.com |
| RAG服务 | `RagClient` | `rag_client_endpoint` | wuyingaiinner-pre.aliyuncs.com |
| 无影基础设施 | `WaiyInfraClient` | `waiy_infra_endpoint` | wuyingaiinner-pre.aliyuncs.com |
| 登录验证服务 | `LoginVerifyClient` | `login_verify_endpoint` | wuyingaiinner-pre.aliyuncs.com |
| AIPPT服务 | `AIPPTClient` | `aippt_endpoint` | co.aippt.cn |

## 配置文件

在 `properties.toml` 中为不同环境配置endpoint：

```toml
# 默认配置
[default]
appstream_inner_endpoint = "appstream-center-inner-share.cn-hangzhou.aliyuncs.com"
cloud_storage_endpoint = "eds-storage-inner-share.cn-hangzhou.aliyuncs.com"
pc_inside_endpoint = "wuying-pc-inside-vpc-inner-pre-test.cn-hangzhou.aliyuncs.com"
rag_client_endpoint = "wuyingaiinner-pre.aliyuncs.com"
waiy_infra_endpoint = "wuyingaiinner-pre.aliyuncs.com"
login_verify_endpoint = "wuyingaiinner-pre.aliyuncs.com"

# Daily环境配置
[daily]
appstream_inner_endpoint = "appstream-center-inner-share.cn-hangzhou.aliyuncs.com"
cloud_storage_endpoint = "eds-storage-inner-share.cn-hangzhou.aliyuncs.com"
pc_inside_endpoint = "wuying-pc-inside-vpc-inner-pre-test.cn-hangzhou.aliyuncs.com"
rag_client_endpoint = "wuyingaiinner-pre.aliyuncs.com"
waiy_infra_endpoint = "wuyingaiinner-pre.aliyuncs.com"
login_verify_endpoint = "wuyingaiinner-pre.aliyuncs.com"

# Pre环境配置
[pre]
appstream_inner_endpoint = "appstream-center-inner-share.cn-hangzhou.aliyuncs.com"
cloud_storage_endpoint = "eds-storage-inner-share.cn-hangzhou.aliyuncs.com"
pc_inside_endpoint = "wuying-pc-inside-vpc-inner-pre-test.cn-hangzhou.aliyuncs.com"
rag_client_endpoint = "wuyingaiinner-pre.aliyuncs.com"
waiy_infra_endpoint = "wuyingaiinner-pre.aliyuncs.com"
login_verify_endpoint = "wuyingaiinner-pre.aliyuncs.com"

# Prod环境配置
[prod]
appstream_inner_endpoint = "appstream-center-inner-share.cn-hangzhou.aliyuncs.com"
cloud_storage_endpoint = "eds-storage-inner-share.cn-hangzhou.aliyuncs.com"
pc_inside_endpoint = "wuying-pc-inside-vpc-inner-prod.cn-hangzhou.aliyuncs.com"
rag_client_endpoint = "wuyingaiinner.aliyuncs.com"
waiy_infra_endpoint = "wuyingaiinner.aliyuncs.com"
login_verify_endpoint = "wuyingaiinner.aliyuncs.com"
```

## 基本使用

### 1. 使用工厂类

```python
from src.popclients.client_factory import PopClientFactory

# 获取各种客户端（自动从配置文件读取endpoint）
appstream_client = PopClientFactory.get_appstream_inner_client()
storage_client = PopClientFactory.get_cloud_storage_client()
rag_client = PopClientFactory.get_rag_client()

# 使用自定义endpoint
custom_client = PopClientFactory.get_rag_client(
    endpoint="custom-endpoint.aliyuncs.com"
)
```

### 2. 使用便捷函数（向后兼容）

```python
from src.popclients import (
    get_appstream_inner_client,
    get_cloud_storage_client,
    get_rag_client
)

appstream_client = get_appstream_inner_client()
storage_client = get_cloud_storage_client()
rag_client = get_rag_client()
```

### 3. 客户端管理

```python
# 获取客户端信息
client_info = PopClientFactory.get_client_info()
print(f"当前环境: {client_info['environment']}")
print(f"活跃客户端: {client_info['active_clients']}")
print(f"配置的端点: {client_info['endpoints']}")

# 重置单个客户端
PopClientFactory.reset_client('rag_client')

# 重置所有客户端
PopClientFactory.reset_all_clients()
```

## 高级用法

### 1. 环境特定配置

```python
from src.shared.config.environments import env_manager

# 查看当前环境
print(f"当前环境: {env_manager.current_env.value}")

# 获取特定配置
endpoint = env_manager.get_config_value("rag_client_endpoint")
print(f"RAG客户端端点: {endpoint}")
```

### 2. 自定义配置

```python
# 传递额外的配置参数
client = PopClientFactory.get_rag_client(
    endpoint="custom-endpoint.aliyuncs.com",
    connect_timeout=15000,
    read_timeout=30000
)
```

### 3. 批量操作

```python
# 预热所有客户端
clients = {
    'appstream': PopClientFactory.get_appstream_inner_client(),
    'storage': PopClientFactory.get_cloud_storage_client(),
    'pc': PopClientFactory.get_pc_inside_client(),
    'rag': PopClientFactory.get_rag_client(),
    'waiy': PopClientFactory.get_waiy_infra_client(),
    'login': PopClientFactory.get_login_verify_client(),
    'aippt': PopClientFactory.get_aippt_client(),
}

print(f"已创建 {len(clients)} 个客户端")
```

## 迁移指南

### 从旧方式迁移

**旧方式:**
```python
from src.popclients.rag_client import RagClient

# 硬编码endpoint
client = RagClient(endpoint="wuyingaiinner-pre.aliyuncs.com")
```

**新方式:**
```python
from src.popclients import get_rag_client

# 自动从配置读取endpoint
client = get_rag_client()

# 或使用工厂类
from src.popclients.client_factory import PopClientFactory
client = PopClientFactory.get_rag_client()
```

### 配置迁移

1. 在 `properties.toml` 中添加endpoint配置
2. 移除代码中的硬编码endpoint
3. 使用工厂类或便捷函数获取客户端

## 最佳实践

1. **使用工厂类**: 优先使用 `PopClientFactory` 获取客户端
2. **配置管理**: 将endpoint配置放在 `properties.toml` 中
3. **环境隔离**: 为不同环境配置不同的endpoint
4. **单例使用**: 利用工厂类的单例特性，避免重复创建客户端
5. **错误处理**: 适当处理客户端创建和使用过程中的异常

## 注意事项

1. 工厂类使用单例模式，同一类型的客户端只会创建一次
2. 如需重新创建客户端（如更换endpoint），使用 `reset_client()` 方法
3. 配置文件中的endpoint会覆盖代码中的默认值
4. 确保在使用客户端前已正确配置相关的认证信息

## 示例代码

完整的使用示例请参考 `client_factory_example.py` 文件。
