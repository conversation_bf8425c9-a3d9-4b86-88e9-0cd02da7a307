# -*- coding: utf-8 -*-
"""
popclients 包
包含各种第三方客户端的封装
"""

# 导入各个客户端类
from .appstream_inner_client import AppStreamInnerClient, AppStreamInnerClientError
from .cloud_storage_client import CloudStorageClient, CloudStorageClientError
from .pc_inside_client import PcInsideClient, PcInsideClientError
from .rag_client import RagClient
from .waiy_infra_client import WaiyInfraClient, WaiyInfraClientError, create_waiy_infra_client
from .login_verify_client import LoginVerifyClient, LoginVerifyClientError
from .aippt_client import AIPPTClient, AIPPTClientError

# 导入工厂类和便捷函数
from .client_factory import (
    PopClientFactory,
    get_appstream_inner_client,
    get_cloud_storage_client,
    get_pc_inside_client,
    get_rag_client,
    get_waiy_infra_client,
    get_login_verify_client,
    get_aippt_client,
)

__all__ = [
    # 客户端类
    "AppStreamInnerClient",
    "AppStreamInnerClientError",
    "CloudStorageClient",
    "CloudStorageClientError",
    "PcInsideClient",
    "PcInsideClientError",
    "RagClient",
    "WaiyInfraClient",
    "WaiyInfraClientError",
    "LoginVerifyClient",
    "LoginVerifyClientError",
    "AIPPTClient",
    "AIPPTClientError",
    # 工厂类
    "PopClientFactory",
    # 便捷函数
    "get_appstream_inner_client",
    "get_cloud_storage_client",
    "get_pc_inside_client",
    "get_rag_client",
    "get_waiy_infra_client",
    "get_login_verify_client",
    "get_aippt_client",
    # 向后兼容
    "create_waiy_infra_client",
]