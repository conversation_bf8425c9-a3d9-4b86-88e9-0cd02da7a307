# -*- coding: utf-8 -*-
"""
AIPPT服务客户端封装
提供简化的接口来访问AIPPT服务
"""
import json
import time
import hashlib
import hmac
import base64
from typing import Optional, Dict, Any
import httpx
from loguru import logger
from src.shared.config.environments import env_manager
from src.application.ppt_api_models import AIPPTTokenResponse, GetPPTAuthCodeResponse


class AIPPTClient:
    """
    AIPPT服务客户端封装类
    """

    def __init__(
        self,
        access_key_id: Optional[str] = None,
        access_key_secret: Optional[str] = None,
        endpoint: Optional[str] = None,
        connect_timeout: int = 10000,
        read_timeout: int = 10000,
        **kwargs,
    ):
        """
        初始化AIPPT服务客户端

        Args:
            access_key_id: aippt访问密钥ID
            access_key_secret: aippt访问密钥Secret
            endpoint: 服务端点，默认为AIPPT服务端点
            connect_timeout: 连接超时时间（毫秒）
            read_timeout: 读取超时时间（毫秒）
            **kwargs: 其他配置参数
        """

        # 从配置文件中获取endpoint
        if endpoint is None:
            endpoint = env_manager.get_config_value("aippt_endpoint")

        # 从配置文件获取密钥
        if not access_key_id:
            access_key_id = env_manager.get_config_value("aippt_access_key")
        if not access_key_secret:
            access_key_secret = env_manager.get_config_value("aippt_secret_key")

        if not access_key_id:
            raise AIPPTClientError(
                "access_key_id不能为空，请在配置文件中设置aippt_access_key"
            )
        if not access_key_secret:
            raise AIPPTClientError(
                "access_key_secret不能为空，请在配置文件中设置aippt_secret_key"
            )

        self.access_key_id = access_key_id
        self.access_key_secret = access_key_secret
        self.endpoint = endpoint
        self.connect_timeout = connect_timeout / 1000  # 转换为秒
        self.read_timeout = read_timeout / 1000  # 转换为秒
        
        # 从配置文件获取channel参数并缓存
        self.channel = env_manager.get_config_value("aippt_channel")

        # 初始化HTTP客户端
        self.http_client = self._create_http_client()

        logger.info(
            f"AIPPT客户端初始化完成: endpoint={endpoint}, access_key_id={access_key_id[:8]}..."
        )

    def _create_http_client(self) -> httpx.Client:
        """
        创建HTTP客户端，配置连接池、重试机制等
        
        Returns:
            httpx.Client: 配置好的HTTP客户端
        """
        # 配置连接池限制
        limits = httpx.Limits(
            max_keepalive_connections=20,  # 最大保持连接数
            max_connections=100,  # 最大连接数
            keepalive_expiry=30.0,  # 连接保持时间
        )
        
        # 配置传输层（包含重试机制）
        transport = httpx.HTTPTransport(
            limits=limits,
            retries=3,  # 自动重试3次
        )
        
        # 配置超时
        timeout = httpx.Timeout(
            connect=self.connect_timeout,
            read=self.read_timeout,
            write=self.connect_timeout,
            pool=self.connect_timeout,
        )
        
        # 创建客户端
        client = httpx.Client(
            timeout=timeout,
            transport=transport,
            trust_env=False,  # 不信任系统环境（避免代理干扰）
            verify=True,  # 启用SSL验证
            follow_redirects=True,  # 跟随重定向
        )
        
        logger.debug("[AIPPT] HTTP客户端创建完成，配置了连接池和重试机制")
        return client

    def _recreate_http_client_if_needed(self):
        """
        在连接错误时重新创建HTTP客户端
        """
        try:
            if self.http_client and not self.http_client.is_closed:
                self.http_client.close()
        except Exception as e:
            logger.warning(f"[AIPPT] 关闭旧HTTP客户端时出错: {e}")
        
        self.http_client = self._create_http_client()
        logger.info("[AIPPT] HTTP客户端已重新创建")

    def _make_request_with_retry(self, method: str, url: str, **kwargs) -> httpx.Response:
        """
        执行HTTP请求，包含错误重试机制
        
        对于文件描述符错误和连接错误，会自动重建HTTP客户端。
        
        Args:
            method: HTTP方法 (GET, POST等)
            url: 请求URL
            **kwargs: 其他请求参数
            
        Returns:
            httpx.Response: HTTP响应
            
        Raises:
            AIPPTClientError: 所有重试失败后抛出
        """
        max_retries = 2  # 最多重试2次（总共3次尝试）
        
        for attempt in range(max_retries + 1):
            try:
                if method.upper() == 'GET':
                    return self.http_client.get(url, **kwargs)
                elif method.upper() == 'POST':
                    return self.http_client.post(url, **kwargs)
                else:
                    return self.http_client.request(method, url, **kwargs)
                    
            except Exception as e:
                error_msg = str(e)
                is_last_attempt = attempt == max_retries
                error_type = type(e).__name__
                
                # 检查是否是文件描述符错误，需要重建客户端
                is_fd_error = "Bad file descriptor" in error_msg or "EBADF" in error_msg
                is_connection_error = isinstance(e, (httpx.ConnectError, OSError))
                
                if is_fd_error:
                    logger.warning(
                        f"[AIPPT] 检测到文件描述符错误 (尝试 {attempt + 1}/{max_retries + 1}): {error_msg}"
                    )
                    
                    if not is_last_attempt:
                        # 重新创建HTTP客户端
                        logger.info("[AIPPT] 重新创建HTTP客户端以解决文件描述符问题")
                        self._recreate_http_client_if_needed()
                        continue
                elif is_connection_error and not is_last_attempt:
                    # 其他连接错误，也可能需要重建客户端
                    logger.warning(
                        f"[AIPPT] 检测到连接错误 (尝试 {attempt + 1}/{max_retries + 1}): {error_msg}"
                    )
                    logger.info("[AIPPT] 重新创建HTTP客户端以解决连接问题")
                    self._recreate_http_client_if_needed()
                    continue
                
                if is_last_attempt:
                    # 最后一次尝试失败，抛出异常
                    logger.error(f"[AIPPT] HTTP请求最终失败 ({error_type}): {error_msg}")
                    raise AIPPTClientError(f"HTTP请求失败 ({error_type}): {error_msg}") from e
                else:
                    # 还有重试机会，记录警告并继续
                    logger.warning(
                        f"[AIPPT] HTTP请求失败，准备重试 (尝试 {attempt + 1}/{max_retries + 1}, {error_type}): {error_msg}"
                    )
                    continue
        
        # 理论上不会到达这里
        raise AIPPTClientError("HTTP请求重试失败")

    def _generate_signature(self, method: str, uri: str, timestamp: int) -> str:
        """
        根据AIPPT官方文档生成签名

        签名算法：
        1. 构造规范请求字符串：HttpRequestMethod + "@" + ApiUri + "@" + Timestamp
        2. URI路径必须以"/"结尾，如 /api/grant/code/
        3. 时间戳单位为秒
        4. 使用HMAC-SHA1算法和API密钥对字符串进行签名
        5. 对签名结果进行Base64编码

        Args:
            method: HTTP请求方法，如 GET、POST
            uri: 请求资源路径，如 /api/grant/code
            timestamp: 时间戳（秒）

        Returns:
            签名字符串
        """
        # 1. 确保URI以"/"结尾
        if not uri.endswith('/'):
            uri = uri + '/'

        # 2. 构建待签名字符串：HttpRequestMethod + "@" + ApiUri + "@" + Timestamp
        string_to_sign = f"{method}@{uri}@{timestamp}"

        logger.debug(f"[AIPPT签名] 待签名字符串: {string_to_sign}")

        # 3. 使用HMAC-SHA1生成签名
        signature = hmac.new(
            self.access_key_secret.encode('utf-8'),
            string_to_sign.encode('utf-8'),
            hashlib.sha1,  # 使用SHA1算法
        ).digest()

        # 4. Base64编码
        signature_b64 = base64.b64encode(signature).decode('utf-8')

        logger.debug(f"[AIPPT签名] 生成签名: {signature_b64}")

        return signature_b64

    def get_aippt_auth_code(
        self,
        ali_uid: int,
    ) -> GetPPTAuthCodeResponse:
        """
        获取PPT认证code

        根据AIPPT官方文档：
        - 接口地址: /api/grant/code
        - 请求方式: GET
        - 参数: uid（用户ID，对应ali_uid）、channel（渠道，传空）、type（类型，不传）
        - 认证: HTTP Header中的 x-api-key、x-timestamp、x-signature

        Args:
            ali_uid: 用户ID（对应AIPPT的uid参数）

        Returns:
            GetPPTAuthCodeResponse: 认证码响应
        """

        try:
            logger.info(f"[AIPPT API] 开始获取PPT认证code: uid={ali_uid}")

            params = {'uid': ali_uid, 'channel': self.channel}
            timestamp = int(time.time())
            signature = self._generate_signature('GET', '/api/grant/code', timestamp)
            url = f"https://{self.endpoint}/api/grant/code"
            headers = {
                'x-api-key': self.access_key_id,
                'x-timestamp': str(timestamp),
                'x-signature': signature,
                'Content-Type': 'application/json',
                'User-Agent': 'Alpha-Service-AIPPT-Client/1.0',
            }

            response = self._make_request_with_retry('GET', url, params=params, headers=headers)

            if response.status_code != 200:
                raise AIPPTClientError(
                    f"HTTP请求失败，状态码: {response.status_code}, 响应: {response.text}"
                )

            try:
                response_data = response.json()
            except json.JSONDecodeError as e:
                raise AIPPTClientError(
                    f"解析JSON响应失败: {str(e)}, 响应内容: {response.text}"
                )

            # AIPPT API中 code: 0 表示成功，非0表示失败
            if 'code' in response_data and response_data.get('code') != 0:
                error_message = response_data.get('msg', '未知错误')
                raise AIPPTClientError(
                    f"AIPPT API调用失败: {error_message} (Code: {response_data.get('code')})"
                )

            data = response_data.get('data', {})
            logger.info(f"[AIPPT API] 成功获取PPT认证code: data={data}")

            auth_code = data.get('code')
            time_expire = data.get('time_expire')
            api_key = data.get('api_key')

            if not auth_code:
                raise AIPPTClientError(f"响应中缺少认证码: {response_data}")

            return GetPPTAuthCodeResponse(
                code=auth_code,
                time_expire=str(time_expire),
                api_key=api_key,
                channel=self.channel,
            )
        except Exception as e:
            raise AIPPTClientError(f"获取PPT认证code失败: {str(e)}") from e

    def get_aippt_token(self, ali_uid: int) -> AIPPTTokenResponse:
        """
        获取AIPPT token

        根据AIPPT官方文档：
        - 接口地址: /api/grant/token
        - 请求方式: GET
        - 参数: uid（用户ID，对应ali_uid）、channel（渠道，从配置中获取）
        - 认证: HTTP Header中的 x-api-key、x-timestamp、x-signature

        Args:
            ali_uid: 用户ID（对应AIPPT的uid参数）

        Returns:
            AIPPTTokenResponse: token响应对象
        """
        try:
            logger.info(f"[AIPPT API] 开始获取AIPPT token: uid={ali_uid}")

            params = {'uid': ali_uid, 'channel': self.channel}
            timestamp = int(time.time())
            signature = self._generate_signature('GET', '/api/grant/token', timestamp)
            url = f"https://{self.endpoint}/api/grant/token"
            headers = {
                'x-api-key': self.access_key_id,
                'x-timestamp': str(timestamp),
                'x-signature': signature,
                'Content-Type': 'application/json',
                'User-Agent': 'Alpha-Service-AIPPT-Client/1.0',
            }

            response = self._make_request_with_retry('GET', url, params=params, headers=headers)

            if response.status_code != 200:
                raise AIPPTClientError(
                    f"HTTP请求失败，状态码: {response.status_code}, 响应: {response.text}"
                )

            try:
                response_data = response.json()
            except json.JSONDecodeError as e:
                raise AIPPTClientError(
                    f"解析JSON响应失败: {str(e)}, 响应内容: {response.text}"
                )

            # AIPPT API中 code: 0 表示成功，非0表示失败
            if 'code' in response_data and response_data.get('code') != 0:
                error_message = response_data.get('msg', '未知错误')
                raise AIPPTClientError(
                    f"AIPPT API调用失败: {error_message} (Code: {response_data.get('code')})"
                )

            data = response_data.get('data', {})
            if not data:
                raise AIPPTClientError(f"响应中缺少数据: {response_data}")
            logger.info(f"[AIPPT API] 成功获取AIPPT token: data={data}")

            token = data.get('token')
            time_expire = data.get('time_expire')

            return AIPPTTokenResponse(
                token=token,
                time_expire=time_expire
            )

        except Exception as e:
            raise AIPPTClientError(f"获取AIPPT token失败: {str(e)}") from e

    def get_ppt_info(self, ppt_id: str, token: str) -> Dict[str, Any]:
        """
        获取作品详情信息

        根据AIPPT官方文档：
        - 接口地址: /api/design/info
        - 请求方式: GET
        - 参数: ppt_id（作品ID）
        - 认证: HTTP Header中的 x-api-key、x-token、x-channel

        Args:
            ppt_id: 作品ID
            token: AIPPT认证token

        Returns:
            Dict[str, Any]: 作品详情
        """
        try:
            logger.info(f"[AIPPT API] 开始获取作品详情: ppt_id={ppt_id}")

            params = {'user_design_id': ppt_id}
            url = f"https://{self.endpoint}/api/design/info"
            headers = {
                'x-api-key': self.access_key_id,
                'x-token': token,
                'x-channel': self.channel,
                'User-Agent': 'Alpha-Service-AIPPT-Client/1.0',
            }

            response = self._make_request_with_retry('GET', url, params=params, headers=headers)

            if response.status_code != 200:
                raise AIPPTClientError(
                    f"HTTP请求失败，状态码: {response.status_code}, 响应: {response.text}"
                )

            try:
                response_data = response.json()
            except json.JSONDecodeError as e:
                raise AIPPTClientError(
                    f"解析JSON响应失败: {str(e)}, 响应内容: {response.text}"
                )

            # AIPPT API中 code: 0 表示成功，非0表示失败
            if 'code' in response_data and response_data.get('code') != 0:
                error_message = response_data.get('msg', '未知错误')
                raise AIPPTClientError(
                    f"AIPPT API调用失败: {error_message} (Code: {response_data.get('code')})"
                )

            data = response_data.get('data', {})
            logger.info(f"[AIPPT API] 成功获取作品详情: data={data}")
            return data

        except Exception as e:
            raise AIPPTClientError(f"获取作品详情失败: {str(e)}") from e

    def export_ppt(
        self,
        ppt_id: str,
        token: str,
        format: str = "ppt",
    ):
        """
        作品导出

        根据AIPPT官方文档：
        - 接口地址: /api/download/export/file
        - 请求方式: POST
        - 参数: id（作品ID）, format（导出格式）, edit（是否可编辑）, files_to_zip（是否压缩）
        - 认证: HTTP Header中的 x-api-key、x-token、x-channel

        Args:
            ppt_id: 作品ID
            token: AIPPT认证token
            export_format: 导出格式，支持：png|jpeg|pdf|ppt，默认为ppt
            edit: 导出的作品是否可编辑，默认为True
            files_to_zip: 导出的图片是否压缩为zip，默认为False

        Returns:
            str: 导出任务标识
        """
        try:
            logger.info(f"[AIPPT API] 开始作品导出: id={ppt_id}")

            payload = {
                'id': ppt_id,
                'format': format,
                'edit': 'true',
                'files_to_zip': 'false',
            }
            url = f"https://{self.endpoint}/api/download/export/file"
            headers = {
                'x-api-key': self.access_key_id,
                'x-token': token,
                'x-channel': self.channel,
                'Content-Type': 'application/x-www-form-urlencoded',
                'User-Agent': 'Alpha-Service-AIPPT-Client/1.0',
            }

            response = self._make_request_with_retry('POST', url, data=payload, headers=headers)

            if response.status_code != 200:
                raise AIPPTClientError(
                    f"HTTP请求失败，状态码: {response.status_code}, 响应: {response.text}"
                )

            try:
                response_data = response.json()
            except json.JSONDecodeError as e:
                raise AIPPTClientError(
                    f"解析JSON响应失败: {str(e)}, 响应内容: {response.text}"
                )

            # AIPPT API中 code: 0 表示成功，非0表示失败
            if 'code' in response_data and response_data.get('code') != 0:
                error_message = response_data.get('msg', '未知错误')
                raise AIPPTClientError(
                    f"AIPPT API调用失败: {error_message} (Code: {response_data.get('code')})"
                )

            task_key = response_data.get('data')
            if not task_key:
                raise AIPPTClientError(f"响应中缺少task_key: {response_data}")

            logger.info(
                f"[AIPPT API] 成功启动作品导出: id={ppt_id}, task_key={task_key}"
            )
            return task_key

        except Exception as e:
            raise AIPPTClientError(f"作品导出失败: {str(e)}") from e

    def get_ppt_export_result(self, task_key: str, token: str) -> Dict[str, Any]:
        """
        获取作品导出结果

        根据AIPPT官方文档：
        - 接口地址: /api/download/export/file/result
        - 请求方式: POST
        - 参数: task_key（作品导出的任务标识）
        - 认证: HTTP Header中的 x-api-key、x-token、x-channel

        Args:
            task_key: 导出任务标识
            token: AIPPT认证token

        Returns:
            Dict[str, Any]: 导出结果响应数据，包含下载链接
        """
        try:
            logger.info(f"[AIPPT API] 开始查询作品导出结果: task_key={task_key}")

            payload = {'task_key': task_key}
            url = f"https://{self.endpoint}/api/download/export/file/result"
            headers = {
                'x-api-key': self.access_key_id,
                'x-token': token,
                'x-channel': self.channel,
                'Content-Type': 'application/x-www-form-urlencoded',
                'User-Agent': 'Alpha-Service-AIPPT-Client/1.0',
            }

            # 使用form-data格式发送请求
            response = self._make_request_with_retry('POST', url, data=payload, headers=headers)

            if response.status_code != 200:
                raise AIPPTClientError(
                    f"HTTP请求失败，状态码: {response.status_code}, 响应: {response.text}"
                )

            try:
                response_data = response.json()
            except json.JSONDecodeError as e:
                raise AIPPTClientError(
                    f"解析JSON响应失败: {str(e)}, 响应内容: {response.text}"
                )

            # AIPPT API中 code: 0 表示成功，非0表示失败
            code = response_data.get('code', -1)
            msg = response_data.get('msg', '未知状态')

            if code == 0:
                download_urls = response_data.get('data', [])
                if download_urls and len(download_urls) > 0:
                    download_url = download_urls[0]
                    logger.info(
                        f"[AIPPT API] 作品导出成功: task_key={task_key}, 下载链接={download_url}"
                    )
                    return {'download_url': download_url, 'message': msg}
                else:
                    logger.warning(
                        f"[AIPPT API] 作品导出完成但无下载链接: task_key={task_key}"
                    )
                    return {'download_url': None, 'message': msg}
            else:
                logger.info(
                    f"[AIPPT API] 作品导出未完成: task_key={task_key}, 状态={msg}"
                )
                return {'download_url': None, 'message': msg}

        except Exception as e:
            raise AIPPTClientError(f"获取作品导出结果失败: {str(e)}") from e

    def delete_ppt(self, ppt_id: str, token: str) -> bool:
        """
        删除作品

        根据AIPPT官方文档：
        - 接口地址: /api/design/delete
        - 请求方式: POST
        - 参数: user_design_id（作品ID）
        - 认证: HTTP Header中的 x-api-key、x-token、x-channel

        Args:
            ppt_id: 作品ID
            token: AIPPT认证token

        Returns:
            bool: 删除是否成功
        """
        try:
            logger.info(f"[AIPPT API] 开始删除作品: ppt_id={ppt_id}")

            payload = {'user_design_id': ppt_id}
            url = f"https://{self.endpoint}/api/design/delete"
            headers = {
                'x-api-key': self.access_key_id,
                'x-token': token,
                'x-channel': self.channel,
                'Content-Type': 'application/x-www-form-urlencoded',
                'User-Agent': 'Alpha-Service-AIPPT-Client/1.0',
            }

            response = self._make_request_with_retry('POST', url, data=payload, headers=headers)

            if response.status_code != 200:
                raise AIPPTClientError(
                    f"HTTP请求失败，状态码: {response.status_code}, 响应: {response.text}"
                )

            try:
                response_data = response.json()
            except json.JSONDecodeError as e:
                raise AIPPTClientError(
                    f"解析JSON响应失败: {str(e)}, 响应内容: {response.text}"
                )

            # AIPPT API中 code: 0 表示成功，非0表示失败
            if 'code' in response_data and response_data.get('code') != 0:
                error_message = response_data.get('msg', '未知错误')
                raise AIPPTClientError(
                    f"AIPPT API调用失败: {error_message} (Code: {response_data.get('code')})"
                )

            logger.info(f"[AIPPT API] 成功删除作品: ppt_id={ppt_id}")
            return True

        except Exception as e:
            logger.error(f"[AIPPT API] 删除作品失败: ppt_id={ppt_id}, error={str(e)}")
            raise AIPPTClientError(f"删除作品失败: {str(e)}") from e

    def close(self):
        """
        关闭HTTP客户端，释放资源
        """
        try:
            if self.http_client and not self.http_client.is_closed:
                self.http_client.close()
                logger.info("[AIPPT] HTTP客户端已关闭")
        except Exception as e:
            logger.warning(f"[AIPPT] 关闭HTTP客户端时出错: {e}")

    def __del__(self):
        """
        析构函数，确保资源被释放
        """
        try:
            self.close()
        except:
            pass  # 避免析构函数中的异常


class AIPPTClientError(Exception):
    """AIPPT客户端异常类"""

    pass


# 全局单例实例
_aippt_client_instance = None


def get_aippt_client(
    access_key_id: Optional[str] = None,
    access_key_secret: Optional[str] = None,
    **kwargs,
) -> AIPPTClient:
    """
    获取AIPPT客户端单例实例

    Args:
        access_key_id: aippt访问密钥ID
        access_key_secret: aippt访问密钥Secret
        endpoint: 服务端点
        **kwargs: 其他配置参数

    Returns:
        AIPPTClient: AIPPT客户端实例
    """
    global _aippt_client_instance

    if _aippt_client_instance is None:
        _aippt_client_instance = AIPPTClient(
            access_key_id=access_key_id,
            access_key_secret=access_key_secret,
            **kwargs,
        )

    return _aippt_client_instance
