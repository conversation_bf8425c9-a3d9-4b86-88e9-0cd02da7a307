# -*- coding: utf-8 -*-
"""
CloudStorageClient - 封装阿里云EDS存储内部服务客户端
提供简化的接口来访问EDS存储内部服务
"""
from ast import Str
from math import log
from operator import ne
from typing import List, Optional, Dict, Any
import sys
import os

# 添加本地eds-storage-inner-20230117包到Python路径
current_dir = os.path.dirname(__file__)
eds_storage_path = os.path.join(current_dir, "eds-storage-inner-20230117")
if eds_storage_path not in sys.path:
    sys.path.insert(0, eds_storage_path)

try:
    from alibabacloud_tea_openapi import models as open_api_models
    from alibabacloud_eds_storage_inner20230117 import client
    from alibabacloud_eds_storage_inner20230117 import models as storage_models
except ImportError as e:
    # 如果无法导入，提供更详细的错误信息
    raise ImportError(
        f"无法导入 alibabacloud_wuyingaiinner20250708 包: {e}\n"
        "请确保已安装: pip install alibabacloud-wuyingaiinner20250708==1.1.0"
    )

from loguru import logger


class CloudStorageClient:
    """
    EDS存储内部服务客户端封装类
    """

    def __init__(
        self,
        endpoint: Optional[str] = None,
        connect_timeout: int = 5000,
        read_timeout: int = 10000,
        **kwargs,
    ):
        """
        初始化EDS存储内部服务客户端

        Args:
            endpoint: 服务端点，默认为EDS存储内部服务端点
            connect_timeout: 连接超时时间（毫秒）
            read_timeout: 读取超时时间（毫秒）
            **kwargs: 其他配置参数
        """
        # 从配置中读取RAM角色ARN
        try:
            # 尝试多种导入方式
            try:
                from src.shared.config.environments import env_manager
            except ImportError:
                import sys
                import os

                # 添加项目根目录到Python路径
                project_root = os.path.abspath(
                    os.path.join(os.path.dirname(__file__), "..", "..")
                )
                if project_root not in sys.path:
                    sys.path.insert(0, project_root)
                from src.shared.config.environments import env_manager

            config = env_manager.get_config()
            ram_role_arn = config.ram_role_arn
            region_id = config.region_id

            if not ram_role_arn:
                raise ValueError("配置中缺少ram_role_arn，无法使用无AK认证")

            logger.info(
                f"从配置中读取RAM角色ARN: {ram_role_arn} - 环境: {env_manager.current_env.value}"
            )
        except Exception as e:
            logger.error(f"无法从配置中读取RAM角色ARN: {e}")
            raise CloudStorageClientError(f"无法从配置中读取RAM角色ARN: {e}")

        # 设置默认端点，优先从配置文件读取
        if not endpoint:
            endpoint = env_manager.get_config_value("cloud_storage_endpoint")
            if not endpoint:
                endpoint = "eds-storage-inner-share.cn-hangzhou.aliyuncs.com"

        self.endpoint = endpoint
        self.connect_timeout = connect_timeout
        self.read_timeout = read_timeout
        self.ram_role_arn = ram_role_arn
        self.region_id = region_id

        # 使用全局无AK认证创建凭证（与其他客户端保持一致）
        try:
            from src.shared.auth import get_akless_credential

            cred_client = get_akless_credential(ram_role_arn)
        except Exception as e:
            logger.error(f"创建无AK凭证失败: {e}")
            raise CloudStorageClientError(f"创建无AK凭证失败: {e}")

        # 创建配置
        self.config = open_api_models.Config(
            credential=cred_client,
            region_id=region_id,
            connect_timeout=connect_timeout,
            read_timeout=read_timeout,
            **kwargs,
        )
        # 确保端点被正确设置
        self.config.endpoint = endpoint

        # 初始化客户端
        self._client = client.Client(self.config)

        # 验证端点设置
        logger.info(f"EDS存储客户端端点设置: {self.config.endpoint}")
        logger.info(
            f"EDS存储客户端实际端点: {getattr(self._client, '_endpoint', 'Unknown')}"
        )

    # 文件上传流程相关方法
    def pre_upload_file(
        self,
        file_name: str,
        user_ali_uid: int,
        product_type: str,
        wy_drive_owner_id: str,
        parent_folder_path: Optional[str] = None,
        parent_folder_id: Optional[str] = None,
        tags: Optional[List[str]] = None,
    ) -> storage_models.PreUploadFileResponse:
        """
        获取上传文件凭证

        Args:
            file_name: 文件名称
            user_ali_uid: 用户阿里云UID
            product_type: 产品类型
            wy_drive_owner_id: 无影云盘所有者ID
            parent_folder_path: 父文件夹路径
            parent_folder_id: 父文件夹ID
            tags: 标签

        Returns:
            PreUploadFileResponse: 预上传文件响应
        """
        try:
            logger.info(
                f"预上传文件: {file_name}, 用户: {user_ali_uid}, 产品类型: {product_type}, 无影云盘所有者ID: {wy_drive_owner_id}, 父文件夹路径: {parent_folder_path}, 父文件夹ID: {parent_folder_id}, 标签: {tags}"
            )
            request = storage_models.PreUploadFileRequest(
                file_name=file_name,
                product_type=product_type,
                user_ali_uid=user_ali_uid,
                wy_drive_owner_id=wy_drive_owner_id,
                parent_folder_path=parent_folder_path,
                parent_folder_id=parent_folder_id,
                tags=tags,
            )
            response = self._client.pre_upload_file(request)
            logger.info(f"预上传文件响应: {response}")
            return response
        except Exception as e:
            logger.exception(f"预上传文件失败: {str(e)}")
            raise CloudStorageClientError(f"预上传文件失败: {str(e)}") from e

    def complete_upload_file(
        self,
        user_ali_uid: int,
        product_type: str,
        wy_drive_owner_id: str,
        file_id: str,
        upload_id: str,
    ) -> storage_models.CompleteUploadFileResponse:
        """
        完成文件上传

        Args:
            file_id: 文件ID
            product_type: 产品类型
            user_ali_uid: 用户阿里云UID
            wy_drive_owner_id: 无影云盘所有者ID

        Returns:
            CompleteUploadFileResponse: 完成上传文件响应
        """
        try:
            logger.info(
                f"完成文件上传: 文件ID: {file_id}, 产品类型: {product_type}, 用户: {user_ali_uid}, 无影云盘所有者ID: {wy_drive_owner_id}"
            )
            request = storage_models.CompleteUploadFileRequest(
                file_id=file_id,
                product_type=product_type,
                upload_id=upload_id,
                user_ali_uid=user_ali_uid,
                wy_drive_owner_id=wy_drive_owner_id,
            )
            response = self._client.complete_upload_file(request)
            logger.info(f"完成文件上传响应: {response}")
            return response
        except Exception as e:
            logger.exception(f"完成文件上传失败: {str(e)}")
            raise CloudStorageClientError(f"完成文件上传失败: {str(e)}") from e

    def get_download_url_by_path(
        self,
        file_path: str,
        product_type: Optional[str] = None,
        user_ali_uid: Optional[str] = None,
        wy_drive_owner_id: Optional[str] = None,
    ) -> storage_models.GetDownloadUrlByPathResponse:
        """
        通过路径获取文件下载URL

        Args:
            file_path: 文件路径
            product_type: 产品类型
            user_ali_uid: 用户阿里云UID
            wy_drive_owner_id: 无影云盘所有者ID

        Returns:
            GetDownloadUrlByPathResponse: 获取下载URL响应
        """
        try:
            request = storage_models.GetDownloadUrlByPathRequest(
                file_path=file_path,
                product_type=product_type,
                user_ali_uid=user_ali_uid,
                wy_drive_owner_id=wy_drive_owner_id,
            )
            logger.info(f"通过路径获取下载URL请求: {request}")
            response = self._client.get_download_url_by_path(request)
            logger.info(f"通过路径获取下载URL返回: {response.body}")
            return response
        except Exception as e:
            logger.exception(f"通过路径获取下载URL失败: {e}")
            raise CloudStorageClientError(f"通过路径获取下载URL失败: {str(e)}") from e

    def get_download_url(
        self,
        file_id: str,
        product_type: str,
        user_ali_uid: int,
        wy_drive_owner_id: str,
    ) -> storage_models.GetDownloadUrlResponse:
        """
        通过文件ID获取文件下载URL

        Args:
            file_id: 文件ID
            product_type: 产品类型
            user_ali_uid: 用户阿里云UID
            wy_drive_owner_id: 无影云盘所有者ID

        Returns:
            GetDownloadUrlByPathResponse: 获取下载URL响应
        """
        try:
            request = storage_models.GetDownloadUrlRequest(
                file_id=file_id,
                product_type=product_type,
                user_ali_uid=user_ali_uid,
                wy_drive_owner_id=wy_drive_owner_id,
            )
            response = self._client.get_download_url(request)
            logger.info(f"通过文件ID获取下载URL响应: {response}")
            return response
        except Exception as e:
            logger.exception(f"通过文件ID获取下载URL失败: {str(e)}")
            raise CloudStorageClientError(f"通过文件ID获取下载URL失败: {str(e)}") from e

    def describe_wy_drive_file_flat(
        self,
        parent_folder: str,
        user_ali_uid: int,
        wy_drive_owner_id: str,
        max_results: int = 100,
        next_token: Optional[str] = None,
    ) -> storage_models.DescribeWyDriveFileFlatResponse:
        """
        查询无影云盘文件（扁平化）

        Args:
            user_ali_uid: 用户阿里云UID
            wy_drive_owner_id: 无影云盘所有者ID

        Returns:
            DescribeWyDriveFileFlatResponse: 查询无影云盘文件响应
        """
        try:
            request = storage_models.DescribeWyDriveFileFlatRequest(
                max_results=max_results,
                next_token=next_token or "",
                parent_folder=parent_folder,
                product_type="AiAgent",
                user_ali_uid=user_ali_uid, 
                wy_drive_owner_id=wy_drive_owner_id
            )
            logger.info(f"查询无影云盘文件请求: {request}")
            response = self._client.describe_wy_drive_file_flat(request)
            logger.info(f"查询无影云盘文件返回: {response.body}")
            return response
        except Exception as e:
            logger.exception(f"查询无影云盘文件失败: {e}")
            raise CloudStorageClientError(f"查询无影云盘文件失败: {str(e)}") from e

    def delete_wy_drive_file(
        self,
        file_path: str,
        product_type: str,
        user_ali_uid: int,
        wy_drive_owner_id: str,
        file_id: Optional[str] = None,
    ) -> storage_models.DeleteWyDriveFileResponse:
        """
        删除无影云盘文件

        Args:
            file_path: 文件路径（必需）
            product_type: 产品类型（必需）
            user_ali_uid: 用户阿里云UID（必需）
            wy_drive_owner_id: 无影云盘所有者ID（必需）
            file_id: 文件ID（可选）

        Returns:
            DeleteWyDriveFileResponse: 删除无影云盘文件响应
        """
        try:
            logger.info(
                f"删除无影云盘文件: 文件路径: {file_path}, 产品类型: {product_type}, "
                f"用户: {user_ali_uid}, 无影云盘所有者ID: {wy_drive_owner_id}, 文件ID: {file_id}"
            )
            request = storage_models.DeleteWyDriveFileRequest(
                file_path=file_path,
                product_type=product_type,
                user_ali_uid=user_ali_uid,
                wy_drive_owner_id=wy_drive_owner_id,
                file_id=file_id,
            )
            response = self._client.delete_wy_drive_file(request)
            logger.info(f"删除无影云盘文件响应: {response}")
            return response
        except Exception as e:
            logger.exception(f"删除无影云盘文件失败: {str(e)}")
            raise CloudStorageClientError(f"删除无影云盘文件失败: {str(e)}") from e

    # 异步方法
    async def pre_upload_file_async(
        self,
        file_path: str,
        product_type: Optional[str] = None,
        user_ali_uid: Optional[str] = None,
        wy_drive_owner_id: Optional[str] = None,
    ) -> storage_models.PreUploadFileResponse:
        """
        异步预上传文件

        Args:
            file_path: 文件路径
            product_type: 产品类型
            user_ali_uid: 用户阿里云UID
            wy_drive_owner_id: 无影云盘所有者ID

        Returns:
            PreUploadFileResponse: 预上传文件响应
        """
        try:
            request = storage_models.PreUploadFileRequest(
                file_path=file_path,
                product_type=product_type,
                user_ali_uid=user_ali_uid,
                wy_drive_owner_id=wy_drive_owner_id,
            )
            logger.info(f"异步预上传文件请求: {request}")
            response = await self._client.pre_upload_file_async(request)
            logger.info(f"异步预上传文件返回: {response.body}")
            return response
        except Exception as e:
            logger.exception(f"异步预上传文件失败: {e}")
            raise CloudStorageClientError(f"异步预上传文件失败: {str(e)}") from e

    async def complete_upload_file_async(
        self,
        file_path: str,
        product_type: Optional[str] = None,
        user_ali_uid: Optional[str] = None,
        wy_drive_owner_id: Optional[str] = None,
    ) -> storage_models.CompleteUploadFileResponse:
        """
        异步完成文件上传

        Args:
            file_path: 文件路径
            product_type: 产品类型
            user_ali_uid: 用户阿里云UID
            wy_drive_owner_id: 无影云盘所有者ID

        Returns:
            CompleteUploadFileResponse: 完成上传文件响应
        """
        try:
            request = storage_models.CompleteUploadFileRequest(
                file_path=file_path,
                product_type=product_type,
                user_ali_uid=user_ali_uid,
                wy_drive_owner_id=wy_drive_owner_id,
            )
            logger.info(f"异步完成文件上传请求: {request}")
            response = await self._client.complete_upload_file_async(request)
            logger.info(f"异步完成文件上传返回: {response.body}")
            return response
        except Exception as e:
            logger.exception(f"异步完成文件上传失败: {e}")
            raise CloudStorageClientError(f"异步完成文件上传失败: {str(e)}") from e

    async def get_download_url_by_path_async(
        self,
        file_path: str,
        product_type: Optional[str] = None,
        user_ali_uid: Optional[str] = None,
        wy_drive_owner_id: Optional[str] = None,
    ) -> storage_models.GetDownloadUrlByPathResponse:
        """
        异步通过路径获取文件下载URL

        Args:
            file_path: 文件路径
            product_type: 产品类型
            user_ali_uid: 用户阿里云UID
            wy_drive_owner_id: 无影云盘所有者ID

        Returns:
            GetDownloadUrlByPathResponse: 获取下载URL响应
        """
        try:
            request = storage_models.GetDownloadUrlByPathRequest(
                file_path=file_path,
                product_type=product_type,
                user_ali_uid=user_ali_uid,
                wy_drive_owner_id=wy_drive_owner_id,
            )
            logger.info(f"异步通过路径获取下载URL请求: {request}")
            response = await self._client.get_download_url_by_path_async(request)
            logger.info(f"异步通过路径获取下载URL返回: {response.body}")
            return response
        except Exception as e:
            logger.exception(f"异步通过路径获取下载URL失败: {e}")
            raise CloudStorageClientError(
                f"异步通过路径获取下载URL失败: {str(e)}"
            ) from e

    async def describe_wy_drive_file_flat_async(
        self,
        user_ali_uid: Optional[str] = None,
        wy_drive_owner_id: Optional[str] = None,
    ) -> storage_models.DescribeWyDriveFileFlatResponse:
        """
        异步查询无影云盘文件（扁平化）

        Args:
            user_ali_uid: 用户阿里云UID
            wy_drive_owner_id: 无影云盘所有者ID

        Returns:
            DescribeWyDriveFileFlatResponse: 查询无影云盘文件响应
        """
        try:
            request = storage_models.DescribeWyDriveFileFlatRequest(
                user_ali_uid=user_ali_uid, wy_drive_owner_id=wy_drive_owner_id
            )
            logger.info(f"异步查询无影云盘文件请求: {request}")
            response = await self._client.describe_wy_drive_file_flat_async(request)
            logger.info(f"异步查询无影云盘文件返回: {response.body}")
            return response
        except Exception as e:
            logger.exception(f"异步查询无影云盘文件失败: {e}")
            raise CloudStorageClientError(f"异步查询无影云盘文件失败: {str(e)}") from e

    async def delete_wy_drive_file_async(
        self,
        file_path: str,
        product_type: str,
        user_ali_uid: int,
        wy_drive_owner_id: str,
        file_id: Optional[str] = None,
    ) -> storage_models.DeleteWyDriveFileResponse:
        """
        异步删除无影云盘文件

        Args:
            file_path: 文件路径（必需）
            product_type: 产品类型（必需）
            user_ali_uid: 用户阿里云UID（必需）
            wy_drive_owner_id: 无影云盘所有者ID（必需）
            file_id: 文件ID（可选）

        Returns:
            DeleteWyDriveFileResponse: 删除无影云盘文件响应
        """
        try:
            logger.info(
                f"异步删除无影云盘文件: 文件路径: {file_path}, 产品类型: {product_type}, "
                f"用户: {user_ali_uid}, 无影云盘所有者ID: {wy_drive_owner_id}, 文件ID: {file_id}"
            )
            request = storage_models.DeleteWyDriveFileRequest(
                file_path=file_path,
                product_type=product_type,
                user_ali_uid=user_ali_uid,
                wy_drive_owner_id=wy_drive_owner_id,
                file_id=file_id,
            )
            response = await self._client.delete_wy_drive_file_async(request)
            logger.info(f"异步删除无影云盘文件响应: {response}")
            return response
        except Exception as e:
            logger.exception(f"异步删除无影云盘文件失败: {str(e)}")
            raise CloudStorageClientError(f"异步删除无影云盘文件失败: {str(e)}") from e

    # 实用方法
    def get_client_info(self) -> Dict[str, Any]:
        """
        获取客户端信息

        Returns:
            Dict[str, Any]: 客户端配置信息
        """
        return {
            "ram_role_arn": self.ram_role_arn,
            "region_id": self.region_id,
            "endpoint": self.endpoint,
            "connect_timeout": self.connect_timeout,
            "read_timeout": self.read_timeout,
        }

    def __str__(self) -> str:
        """返回客户端字符串表示"""
        return f"CloudStorageClient(endpoint={self.endpoint}, ram_role_arn={self.ram_role_arn})"

    def __repr__(self) -> str:
        """返回客户端详细字符串表示"""
        return self.__str__()


class CloudStorageClientError(Exception):
    """CloudStorageClient异常类"""

    pass


# 全局单例实例
_cloud_storage_client_instance = None


def get_cloud_storage_client(
    endpoint: Optional[str] = None, **kwargs
) -> CloudStorageClient:
    """
    获取EDS存储内部服务客户端单例实例

    Args:
        endpoint: 服务端点
        **kwargs: 其他配置参数

    Returns:
        CloudStorageClient: EDS存储内部服务客户端实例
    """
    global _cloud_storage_client_instance

    if _cloud_storage_client_instance is None:
        _cloud_storage_client_instance = CloudStorageClient(endpoint=endpoint, **kwargs)

    return _cloud_storage_client_instance


def reset_cloud_storage_client():
    """重置EDS存储内部服务客户端单例实例"""
    global _cloud_storage_client_instance
    _cloud_storage_client_instance = None