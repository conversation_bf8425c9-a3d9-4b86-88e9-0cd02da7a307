# -*- coding: utf-8 -*-
"""
POP客户端工厂使用示例
展示如何使用PopClientFactory统一管理所有POP客户端
"""
from loguru import logger
from src.popclients.client_factory import PopClientFactory


def example_factory_usage():
    """演示工厂类的基本使用"""
    logger.info("=== POP客户端工厂使用示例 ===")
    
    try:
        # 获取客户端信息
        client_info = PopClientFactory.get_client_info()
        logger.info(f"当前环境: {client_info['environment']}")
        logger.info(f"配置的端点: {client_info['endpoints']}")
        
        # 使用工厂方法获取各种客户端
        logger.info("\n--- 获取各种客户端 ---")
        
        # AppStream内部客户端
        appstream_client = PopClientFactory.get_appstream_inner_client()
        logger.info(f"AppStream客户端: {appstream_client}")
        
        # 云存储客户端
        storage_client = PopClientFactory.get_cloud_storage_client()
        logger.info(f"云存储客户端: {storage_client}")
        
        # PC内部服务客户端
        pc_client = PopClientFactory.get_pc_inside_client()
        logger.info(f"PC内部服务客户端: {pc_client}")
        
        # RAG客户端
        rag_client = PopClientFactory.get_rag_client()
        logger.info(f"RAG客户端: {rag_client}")
        
        # 无影基础设施客户端
        waiy_client = PopClientFactory.get_waiy_infra_client()
        logger.info(f"无影基础设施客户端: {waiy_client}")
        
        # 登录验证客户端
        login_client = PopClientFactory.get_login_verify_client()
        logger.info(f"登录验证客户端: {login_client}")
        
        # AIPPT客户端
        aippt_client = PopClientFactory.get_aippt_client()
        logger.info(f"AIPPT客户端: {aippt_client}")
        
        # 再次获取客户端信息，查看活跃的客户端
        client_info = PopClientFactory.get_client_info()
        logger.info(f"\n活跃的客户端: {client_info['active_clients']}")
        
    except Exception as e:
        logger.error(f"工厂使用示例失败: {e}")


def example_custom_endpoint():
    """演示自定义端点的使用"""
    logger.info("\n=== 自定义端点示例 ===")
    
    try:
        # 重置所有客户端
        PopClientFactory.reset_all_clients()
        
        # 使用自定义端点创建客户端
        custom_appstream_client = PopClientFactory.get_appstream_inner_client(
            endpoint="custom-appstream-endpoint.aliyuncs.com"
        )
        logger.info(f"自定义端点AppStream客户端: {custom_appstream_client}")
        
        custom_rag_client = PopClientFactory.get_rag_client(
            endpoint="custom-rag-endpoint.aliyuncs.com"
        )
        logger.info(f"自定义端点RAG客户端: {custom_rag_client}")
        
    except Exception as e:
        logger.error(f"自定义端点示例失败: {e}")


def example_client_reset():
    """演示客户端重置功能"""
    logger.info("\n=== 客户端重置示例 ===")
    
    try:
        # 获取一些客户端
        PopClientFactory.get_appstream_inner_client()
        PopClientFactory.get_rag_client()
        
        client_info = PopClientFactory.get_client_info()
        logger.info(f"重置前活跃客户端: {client_info['active_clients']}")
        
        # 重置单个客户端
        PopClientFactory.reset_client('appstream_inner')
        
        client_info = PopClientFactory.get_client_info()
        logger.info(f"重置appstream_inner后: {client_info['active_clients']}")
        
        # 重置所有客户端
        PopClientFactory.reset_all_clients()
        
        client_info = PopClientFactory.get_client_info()
        logger.info(f"重置所有客户端后: {client_info['active_clients']}")
        
    except Exception as e:
        logger.error(f"客户端重置示例失败: {e}")


def example_convenience_functions():
    """演示便捷函数的使用"""
    logger.info("\n=== 便捷函数示例 ===")
    
    try:
        # 使用便捷函数（向后兼容）
        from src.popclients.client_factory import (
            get_appstream_inner_client,
            get_rag_client,
            get_aippt_client
        )
        
        appstream_client = get_appstream_inner_client()
        logger.info(f"便捷函数获取AppStream客户端: {appstream_client}")
        
        rag_client = get_rag_client()
        logger.info(f"便捷函数获取RAG客户端: {rag_client}")
        
        aippt_client = get_aippt_client()
        logger.info(f"便捷函数获取AIPPT客户端: {aippt_client}")
        
    except Exception as e:
        logger.error(f"便捷函数示例失败: {e}")


def example_environment_specific_endpoints():
    """演示不同环境的端点配置"""
    logger.info("\n=== 环境特定端点示例 ===")
    
    try:
        from src.shared.config.environments import env_manager
        
        logger.info(f"当前环境: {env_manager.current_env.value}")
        
        # 显示各个客户端在当前环境下的端点配置
        endpoints = {
            "appstream_inner": env_manager.get_config_value("appstream_inner_endpoint"),
            "cloud_storage": env_manager.get_config_value("cloud_storage_endpoint"),
            "pc_inside": env_manager.get_config_value("pc_inside_endpoint"),
            "rag_client": env_manager.get_config_value("rag_client_endpoint"),
            "waiy_infra": env_manager.get_config_value("waiy_infra_endpoint"),
            "login_verify": env_manager.get_config_value("login_verify_endpoint"),
            "aippt": env_manager.get_config_value("aippt_endpoint"),
        }
        
        logger.info("当前环境的端点配置:")
        for client_name, endpoint in endpoints.items():
            logger.info(f"  {client_name}: {endpoint}")
            
    except Exception as e:
        logger.error(f"环境特定端点示例失败: {e}")


if __name__ == "__main__":
    """运行所有示例"""
    logger.info("开始运行POP客户端工厂示例")
    
    try:
        example_factory_usage()
        example_custom_endpoint()
        example_client_reset()
        example_convenience_functions()
        example_environment_specific_endpoints()
        
        logger.info("\n=== 所有示例运行完成 ===")
        
    except Exception as e:
        logger.error(f"示例运行失败: {e}")
    
    logger.info("POP客户端工厂示例结束")
