# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations

from typing import List

from alibabacloud_eds_storage_inner20230117 import models as main_models
from darabonba.model import DaraModel

class DescribeGlobalCloudDriveServicesResponseBody(DaraModel):
    def __init__(
        self,
        cloud_drive_services: List[main_models.DescribeGlobalCloudDriveServicesResponseBodyCloudDriveServices] = None,
        count: int = None,
        next_token: str = None,
        request_id: str = None,
    ):
        self.cloud_drive_services = cloud_drive_services
        self.count = count
        self.next_token = next_token
        self.request_id = request_id

    def validate(self):
        if self.cloud_drive_services:
            for v1 in self.cloud_drive_services:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        result['CloudDriveServices'] = []
        if self.cloud_drive_services is not None:
            for k1 in self.cloud_drive_services:
                result['CloudDriveServices'].append(k1.to_map() if k1 else None)

        if self.count is not None:
            result['Count'] = self.count

        if self.next_token is not None:
            result['NextToken'] = self.next_token

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        self.cloud_drive_services = []
        if m.get('CloudDriveServices') is not None:
            for k1 in m.get('CloudDriveServices'):
                temp_model = main_models.DescribeGlobalCloudDriveServicesResponseBodyCloudDriveServices()
                self.cloud_drive_services.append(temp_model.from_map(k1))

        if m.get('Count') is not None:
            self.count = m.get('Count')

        if m.get('NextToken') is not None:
            self.next_token = m.get('NextToken')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        return self

class DescribeGlobalCloudDriveServicesResponseBodyCloudDriveServices(DaraModel):
    def __init__(
        self,
        ad_office_sites: List[main_models.DescribeGlobalCloudDriveServicesResponseBodyCloudDriveServicesAdOfficeSites] = None,
        ali_uid: int = None,
        allow_client_display: str = None,
        auto_create_user_enable: str = None,
        auto_create_user_max_size: int = None,
        biz_type: int = None,
        cds_charge_status: str = None,
        cds_charge_type: str = None,
        cds_id: str = None,
        cds_name: str = None,
        cds_release_deadline: str = None,
        cds_status: str = None,
        cen_id: str = None,
        create_time: str = None,
        cross_regional: str = None,
        domain_id: str = None,
        domain_name: str = None,
        expire_time: str = None,
        global_status: str = None,
        instance_id: str = None,
        max_size: int = None,
        metered_size: int = None,
        office_site_type: str = None,
        order_id: str = None,
        pds_administrator: str = None,
        pds_endpoint: str = None,
        pds_init_token: str = None,
        pds_subdomain_id: str = None,
        pds_subdomain_name: str = None,
        pds_url: str = None,
        region_id: str = None,
        sid: str = None,
        solution_id: str = None,
        storage_domain_id: str = None,
        total_distribute_size: int = None,
        user_count_quota: int = None,
    ):
        self.ad_office_sites = ad_office_sites
        self.ali_uid = ali_uid
        self.allow_client_display = allow_client_display
        self.auto_create_user_enable = auto_create_user_enable
        self.auto_create_user_max_size = auto_create_user_max_size
        self.biz_type = biz_type
        self.cds_charge_status = cds_charge_status
        self.cds_charge_type = cds_charge_type
        self.cds_id = cds_id
        self.cds_name = cds_name
        self.cds_release_deadline = cds_release_deadline
        self.cds_status = cds_status
        self.cen_id = cen_id
        self.create_time = create_time
        self.cross_regional = cross_regional
        self.domain_id = domain_id
        self.domain_name = domain_name
        self.expire_time = expire_time
        self.global_status = global_status
        self.instance_id = instance_id
        self.max_size = max_size
        self.metered_size = metered_size
        self.office_site_type = office_site_type
        self.order_id = order_id
        self.pds_administrator = pds_administrator
        self.pds_endpoint = pds_endpoint
        self.pds_init_token = pds_init_token
        self.pds_subdomain_id = pds_subdomain_id
        self.pds_subdomain_name = pds_subdomain_name
        self.pds_url = pds_url
        self.region_id = region_id
        self.sid = sid
        self.solution_id = solution_id
        self.storage_domain_id = storage_domain_id
        self.total_distribute_size = total_distribute_size
        self.user_count_quota = user_count_quota

    def validate(self):
        if self.ad_office_sites:
            for v1 in self.ad_office_sites:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        result['AdOfficeSites'] = []
        if self.ad_office_sites is not None:
            for k1 in self.ad_office_sites:
                result['AdOfficeSites'].append(k1.to_map() if k1 else None)

        if self.ali_uid is not None:
            result['AliUid'] = self.ali_uid

        if self.allow_client_display is not None:
            result['AllowClientDisplay'] = self.allow_client_display

        if self.auto_create_user_enable is not None:
            result['AutoCreateUserEnable'] = self.auto_create_user_enable

        if self.auto_create_user_max_size is not None:
            result['AutoCreateUserMaxSize'] = self.auto_create_user_max_size

        if self.biz_type is not None:
            result['BizType'] = self.biz_type

        if self.cds_charge_status is not None:
            result['CdsChargeStatus'] = self.cds_charge_status

        if self.cds_charge_type is not None:
            result['CdsChargeType'] = self.cds_charge_type

        if self.cds_id is not None:
            result['CdsId'] = self.cds_id

        if self.cds_name is not None:
            result['CdsName'] = self.cds_name

        if self.cds_release_deadline is not None:
            result['CdsReleaseDeadline'] = self.cds_release_deadline

        if self.cds_status is not None:
            result['CdsStatus'] = self.cds_status

        if self.cen_id is not None:
            result['CenId'] = self.cen_id

        if self.create_time is not None:
            result['CreateTime'] = self.create_time

        if self.cross_regional is not None:
            result['CrossRegional'] = self.cross_regional

        if self.domain_id is not None:
            result['DomainId'] = self.domain_id

        if self.domain_name is not None:
            result['DomainName'] = self.domain_name

        if self.expire_time is not None:
            result['ExpireTime'] = self.expire_time

        if self.global_status is not None:
            result['GlobalStatus'] = self.global_status

        if self.instance_id is not None:
            result['InstanceId'] = self.instance_id

        if self.max_size is not None:
            result['MaxSize'] = self.max_size

        if self.metered_size is not None:
            result['MeteredSize'] = self.metered_size

        if self.office_site_type is not None:
            result['OfficeSiteType'] = self.office_site_type

        if self.order_id is not None:
            result['OrderId'] = self.order_id

        if self.pds_administrator is not None:
            result['PdsAdministrator'] = self.pds_administrator

        if self.pds_endpoint is not None:
            result['PdsEndpoint'] = self.pds_endpoint

        if self.pds_init_token is not None:
            result['PdsInitToken'] = self.pds_init_token

        if self.pds_subdomain_id is not None:
            result['PdsSubdomainId'] = self.pds_subdomain_id

        if self.pds_subdomain_name is not None:
            result['PdsSubdomainName'] = self.pds_subdomain_name

        if self.pds_url is not None:
            result['PdsUrl'] = self.pds_url

        if self.region_id is not None:
            result['RegionId'] = self.region_id

        if self.sid is not None:
            result['Sid'] = self.sid

        if self.solution_id is not None:
            result['SolutionId'] = self.solution_id

        if self.storage_domain_id is not None:
            result['StorageDomainId'] = self.storage_domain_id

        if self.total_distribute_size is not None:
            result['TotalDistributeSize'] = self.total_distribute_size

        if self.user_count_quota is not None:
            result['UserCountQuota'] = self.user_count_quota

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        self.ad_office_sites = []
        if m.get('AdOfficeSites') is not None:
            for k1 in m.get('AdOfficeSites'):
                temp_model = main_models.DescribeGlobalCloudDriveServicesResponseBodyCloudDriveServicesAdOfficeSites()
                self.ad_office_sites.append(temp_model.from_map(k1))

        if m.get('AliUid') is not None:
            self.ali_uid = m.get('AliUid')

        if m.get('AllowClientDisplay') is not None:
            self.allow_client_display = m.get('AllowClientDisplay')

        if m.get('AutoCreateUserEnable') is not None:
            self.auto_create_user_enable = m.get('AutoCreateUserEnable')

        if m.get('AutoCreateUserMaxSize') is not None:
            self.auto_create_user_max_size = m.get('AutoCreateUserMaxSize')

        if m.get('BizType') is not None:
            self.biz_type = m.get('BizType')

        if m.get('CdsChargeStatus') is not None:
            self.cds_charge_status = m.get('CdsChargeStatus')

        if m.get('CdsChargeType') is not None:
            self.cds_charge_type = m.get('CdsChargeType')

        if m.get('CdsId') is not None:
            self.cds_id = m.get('CdsId')

        if m.get('CdsName') is not None:
            self.cds_name = m.get('CdsName')

        if m.get('CdsReleaseDeadline') is not None:
            self.cds_release_deadline = m.get('CdsReleaseDeadline')

        if m.get('CdsStatus') is not None:
            self.cds_status = m.get('CdsStatus')

        if m.get('CenId') is not None:
            self.cen_id = m.get('CenId')

        if m.get('CreateTime') is not None:
            self.create_time = m.get('CreateTime')

        if m.get('CrossRegional') is not None:
            self.cross_regional = m.get('CrossRegional')

        if m.get('DomainId') is not None:
            self.domain_id = m.get('DomainId')

        if m.get('DomainName') is not None:
            self.domain_name = m.get('DomainName')

        if m.get('ExpireTime') is not None:
            self.expire_time = m.get('ExpireTime')

        if m.get('GlobalStatus') is not None:
            self.global_status = m.get('GlobalStatus')

        if m.get('InstanceId') is not None:
            self.instance_id = m.get('InstanceId')

        if m.get('MaxSize') is not None:
            self.max_size = m.get('MaxSize')

        if m.get('MeteredSize') is not None:
            self.metered_size = m.get('MeteredSize')

        if m.get('OfficeSiteType') is not None:
            self.office_site_type = m.get('OfficeSiteType')

        if m.get('OrderId') is not None:
            self.order_id = m.get('OrderId')

        if m.get('PdsAdministrator') is not None:
            self.pds_administrator = m.get('PdsAdministrator')

        if m.get('PdsEndpoint') is not None:
            self.pds_endpoint = m.get('PdsEndpoint')

        if m.get('PdsInitToken') is not None:
            self.pds_init_token = m.get('PdsInitToken')

        if m.get('PdsSubdomainId') is not None:
            self.pds_subdomain_id = m.get('PdsSubdomainId')

        if m.get('PdsSubdomainName') is not None:
            self.pds_subdomain_name = m.get('PdsSubdomainName')

        if m.get('PdsUrl') is not None:
            self.pds_url = m.get('PdsUrl')

        if m.get('RegionId') is not None:
            self.region_id = m.get('RegionId')

        if m.get('Sid') is not None:
            self.sid = m.get('Sid')

        if m.get('SolutionId') is not None:
            self.solution_id = m.get('SolutionId')

        if m.get('StorageDomainId') is not None:
            self.storage_domain_id = m.get('StorageDomainId')

        if m.get('TotalDistributeSize') is not None:
            self.total_distribute_size = m.get('TotalDistributeSize')

        if m.get('UserCountQuota') is not None:
            self.user_count_quota = m.get('UserCountQuota')

        return self

class DescribeGlobalCloudDriveServicesResponseBodyCloudDriveServicesAdOfficeSites(DaraModel):
    def __init__(
        self,
        office_site_id: str = None,
        office_site_name: str = None,
    ):
        self.office_site_id = office_site_id
        self.office_site_name = office_site_name

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.office_site_id is not None:
            result['OfficeSiteId'] = self.office_site_id

        if self.office_site_name is not None:
            result['OfficeSiteName'] = self.office_site_name

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('OfficeSiteId') is not None:
            self.office_site_id = m.get('OfficeSiteId')

        if m.get('OfficeSiteName') is not None:
            self.office_site_name = m.get('OfficeSiteName')

        return self

