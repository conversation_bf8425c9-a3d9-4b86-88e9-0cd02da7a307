# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations

from typing import List

from alibabacloud_eds_storage_inner20230117 import models as main_models
from darabonba.model import DaraModel

class DescribePeripheralDriverCategoryResponseBody(DaraModel):
    def __init__(
        self,
        brands: List[main_models.DescribePeripheralDriverCategoryResponseBodyBrands] = None,
        device_types: List[main_models.DescribePeripheralDriverCategoryResponseBodyDeviceTypes] = None,
        request_id: str = None,
    ):
        self.brands = brands
        self.device_types = device_types
        self.request_id = request_id

    def validate(self):
        if self.brands:
            for v1 in self.brands:
                 if v1:
                    v1.validate()
        if self.device_types:
            for v1 in self.device_types:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        result['Brands'] = []
        if self.brands is not None:
            for k1 in self.brands:
                result['Brands'].append(k1.to_map() if k1 else None)

        result['DeviceTypes'] = []
        if self.device_types is not None:
            for k1 in self.device_types:
                result['DeviceTypes'].append(k1.to_map() if k1 else None)

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        self.brands = []
        if m.get('Brands') is not None:
            for k1 in m.get('Brands'):
                temp_model = main_models.DescribePeripheralDriverCategoryResponseBodyBrands()
                self.brands.append(temp_model.from_map(k1))

        self.device_types = []
        if m.get('DeviceTypes') is not None:
            for k1 in m.get('DeviceTypes'):
                temp_model = main_models.DescribePeripheralDriverCategoryResponseBodyDeviceTypes()
                self.device_types.append(temp_model.from_map(k1))

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        return self

class DescribePeripheralDriverCategoryResponseBodyDeviceTypes(DaraModel):
    def __init__(
        self,
        name: str = None,
        name_cn: str = None,
    ):
        self.name = name
        self.name_cn = name_cn

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.name is not None:
            result['Name'] = self.name

        if self.name_cn is not None:
            result['NameCN'] = self.name_cn

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Name') is not None:
            self.name = m.get('Name')

        if m.get('NameCN') is not None:
            self.name_cn = m.get('NameCN')

        return self

class DescribePeripheralDriverCategoryResponseBodyBrands(DaraModel):
    def __init__(
        self,
        name: str = None,
        name_cn: str = None,
    ):
        self.name = name
        self.name_cn = name_cn

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.name is not None:
            result['Name'] = self.name

        if self.name_cn is not None:
            result['NameCN'] = self.name_cn

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Name') is not None:
            self.name = m.get('Name')

        if m.get('NameCN') is not None:
            self.name_cn = m.get('NameCN')

        return self

