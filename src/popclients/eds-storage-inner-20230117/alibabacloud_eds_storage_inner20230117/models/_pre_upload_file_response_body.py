# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations

from typing import List

from alibabacloud_eds_storage_inner20230117 import models as main_models
from darabonba.model import DaraModel

class PreUploadFileResponseBody(DaraModel):
    def __init__(
        self,
        etag: str = None,
        file_id: str = None,
        multi_parts_upload_info_list: List[main_models.PreUploadFileResponseBodyMultiPartsUploadInfoList] = None,
        request_id: str = None,
        upload_id: str = None,
    ):
        self.etag = etag
        self.file_id = file_id
        self.multi_parts_upload_info_list = multi_parts_upload_info_list
        self.request_id = request_id
        self.upload_id = upload_id

    def validate(self):
        if self.multi_parts_upload_info_list:
            for v1 in self.multi_parts_upload_info_list:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.etag is not None:
            result['Etag'] = self.etag

        if self.file_id is not None:
            result['FileId'] = self.file_id

        result['MultiPartsUploadInfoList'] = []
        if self.multi_parts_upload_info_list is not None:
            for k1 in self.multi_parts_upload_info_list:
                result['MultiPartsUploadInfoList'].append(k1.to_map() if k1 else None)

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.upload_id is not None:
            result['UploadId'] = self.upload_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Etag') is not None:
            self.etag = m.get('Etag')

        if m.get('FileId') is not None:
            self.file_id = m.get('FileId')

        self.multi_parts_upload_info_list = []
        if m.get('MultiPartsUploadInfoList') is not None:
            for k1 in m.get('MultiPartsUploadInfoList'):
                temp_model = main_models.PreUploadFileResponseBodyMultiPartsUploadInfoList()
                self.multi_parts_upload_info_list.append(temp_model.from_map(k1))

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('UploadId') is not None:
            self.upload_id = m.get('UploadId')

        return self

class PreUploadFileResponseBodyMultiPartsUploadInfoList(DaraModel):
    def __init__(
        self,
        part_number: int = None,
        upload_url: str = None,
    ):
        self.part_number = part_number
        self.upload_url = upload_url

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.part_number is not None:
            result['PartNumber'] = self.part_number

        if self.upload_url is not None:
            result['UploadUrl'] = self.upload_url

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('PartNumber') is not None:
            self.part_number = m.get('PartNumber')

        if m.get('UploadUrl') is not None:
            self.upload_url = m.get('UploadUrl')

        return self

