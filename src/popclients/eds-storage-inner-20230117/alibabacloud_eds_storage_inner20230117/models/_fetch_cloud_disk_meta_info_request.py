# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations

from typing import List

from darabonba.model import DaraModel

class FetchCloudDiskMetaInfoRequest(DaraModel):
    def __init__(
        self,
        cds_ids: List[str] = None,
        user_region_id: str = None,
    ):
        self.cds_ids = cds_ids
        self.user_region_id = user_region_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.cds_ids is not None:
            result['CdsIds'] = self.cds_ids

        if self.user_region_id is not None:
            result['UserRegionId'] = self.user_region_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('CdsIds') is not None:
            self.cds_ids = m.get('CdsIds')

        if m.get('UserRegionId') is not None:
            self.user_region_id = m.get('UserRegionId')

        return self

