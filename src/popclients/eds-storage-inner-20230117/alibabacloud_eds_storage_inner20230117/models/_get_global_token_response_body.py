# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations

from alibabacloud_eds_storage_inner20230117 import models as main_models
from darabonba.model import DaraModel

class GetGlobalTokenResponseBody(DaraModel):
    def __init__(
        self,
        request_id: str = None,
        token: main_models.GetGlobalTokenResponseBodyToken = None,
    ):
        self.request_id = request_id
        self.token = token

    def validate(self):
        if self.token:
            self.token.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.token is not None:
            result['Token'] = self.token.to_map()

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('Token') is not None:
            temp_model = main_models.GetGlobalTokenResponseBodyToken()
            self.token = temp_model.from_map(m.get('Token'))

        return self

class GetGlobalTokenResponseBodyToken(DaraModel):
    def __init__(
        self,
        cds_id: str = None,
        domain_id: str = None,
        expired_after: str = None,
        pds_subdomain_id: str = None,
        region_id: str = None,
        status: str = None,
        token: str = None,
        total_size: int = None,
        used_size: int = None,
    ):
        self.cds_id = cds_id
        self.domain_id = domain_id
        self.expired_after = expired_after
        self.pds_subdomain_id = pds_subdomain_id
        self.region_id = region_id
        self.status = status
        self.token = token
        self.total_size = total_size
        self.used_size = used_size

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.cds_id is not None:
            result['CdsId'] = self.cds_id

        if self.domain_id is not None:
            result['DomainId'] = self.domain_id

        if self.expired_after is not None:
            result['ExpiredAfter'] = self.expired_after

        if self.pds_subdomain_id is not None:
            result['PdsSubdomainId'] = self.pds_subdomain_id

        if self.region_id is not None:
            result['RegionId'] = self.region_id

        if self.status is not None:
            result['Status'] = self.status

        if self.token is not None:
            result['Token'] = self.token

        if self.total_size is not None:
            result['TotalSize'] = self.total_size

        if self.used_size is not None:
            result['UsedSize'] = self.used_size

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('CdsId') is not None:
            self.cds_id = m.get('CdsId')

        if m.get('DomainId') is not None:
            self.domain_id = m.get('DomainId')

        if m.get('ExpiredAfter') is not None:
            self.expired_after = m.get('ExpiredAfter')

        if m.get('PdsSubdomainId') is not None:
            self.pds_subdomain_id = m.get('PdsSubdomainId')

        if m.get('RegionId') is not None:
            self.region_id = m.get('RegionId')

        if m.get('Status') is not None:
            self.status = m.get('Status')

        if m.get('Token') is not None:
            self.token = m.get('Token')

        if m.get('TotalSize') is not None:
            self.total_size = m.get('TotalSize')

        if m.get('UsedSize') is not None:
            self.used_size = m.get('UsedSize')

        return self

