# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations

from typing import List

from alibabacloud_eds_storage_inner20230117 import models as main_models
from darabonba.model import DaraModel

class ListGrayLevelsResponseBody(DaraModel):
    def __init__(
        self,
        gray_levels: List[main_models.ListGrayLevelsResponseBodyGrayLevels] = None,
        request_id: str = None,
    ):
        self.gray_levels = gray_levels
        self.request_id = request_id

    def validate(self):
        if self.gray_levels:
            for v1 in self.gray_levels:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        result['GrayLevels'] = []
        if self.gray_levels is not None:
            for k1 in self.gray_levels:
                result['GrayLevels'].append(k1.to_map() if k1 else None)

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        self.gray_levels = []
        if m.get('GrayLevels') is not None:
            for k1 in m.get('GrayLevels'):
                temp_model = main_models.ListGrayLevelsResponseBodyGrayLevels()
                self.gray_levels.append(temp_model.from_map(k1))

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        return self

class ListGrayLevelsResponseBodyGrayLevels(DaraModel):
    def __init__(
        self,
        gray_level_id: str = None,
        name: str = None,
        os_type: str = None,
    ):
        self.gray_level_id = gray_level_id
        self.name = name
        self.os_type = os_type

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.gray_level_id is not None:
            result['GrayLevelId'] = self.gray_level_id

        if self.name is not None:
            result['Name'] = self.name

        if self.os_type is not None:
            result['OsType'] = self.os_type

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('GrayLevelId') is not None:
            self.gray_level_id = m.get('GrayLevelId')

        if m.get('Name') is not None:
            self.name = m.get('Name')

        if m.get('OsType') is not None:
            self.os_type = m.get('OsType')

        return self

