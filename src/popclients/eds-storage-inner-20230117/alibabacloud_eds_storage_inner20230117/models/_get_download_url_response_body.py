# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations

from darabonba.model import DaraModel

class GetDownloadUrlResponseBody(DaraModel):
    def __init__(
        self,
        expire_time: int = None,
        file_name: str = None,
        request_id: str = None,
        size: int = None,
        success: bool = None,
        url: str = None,
    ):
        self.expire_time = expire_time
        self.file_name = file_name
        self.request_id = request_id
        self.size = size
        self.success = success
        self.url = url

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.expire_time is not None:
            result['ExpireTime'] = self.expire_time

        if self.file_name is not None:
            result['FileName'] = self.file_name

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.size is not None:
            result['Size'] = self.size

        if self.success is not None:
            result['Success'] = self.success

        if self.url is not None:
            result['Url'] = self.url

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ExpireTime') is not None:
            self.expire_time = m.get('ExpireTime')

        if m.get('FileName') is not None:
            self.file_name = m.get('FileName')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('Size') is not None:
            self.size = m.get('Size')

        if m.get('Success') is not None:
            self.success = m.get('Success')

        if m.get('Url') is not None:
            self.url = m.get('Url')

        return self

