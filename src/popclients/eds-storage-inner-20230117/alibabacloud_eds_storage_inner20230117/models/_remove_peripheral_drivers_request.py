# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations

from typing import List

from darabonba.model import DaraModel

class RemovePeripheralDriversRequest(DaraModel):
    def __init__(
        self,
        driver_ids: List[str] = None,
    ):
        self.driver_ids = driver_ids

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.driver_ids is not None:
            result['DriverIds'] = self.driver_ids

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('DriverIds') is not None:
            self.driver_ids = m.get('DriverIds')

        return self

