# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations

from darabonba.model import DaraModel

class DescribeUserCdsDrivesRequest(DaraModel):
    def __init__(
        self,
        ali_uid: int = None,
        directory_id: str = None,
        directory_type: str = None,
        end_user_id: str = None,
        local_search: bool = None,
        user_region_id: str = None,
    ):
        self.ali_uid = ali_uid
        self.directory_id = directory_id
        self.directory_type = directory_type
        self.end_user_id = end_user_id
        self.local_search = local_search
        self.user_region_id = user_region_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.ali_uid is not None:
            result['AliUid'] = self.ali_uid

        if self.directory_id is not None:
            result['DirectoryId'] = self.directory_id

        if self.directory_type is not None:
            result['DirectoryType'] = self.directory_type

        if self.end_user_id is not None:
            result['EndUserId'] = self.end_user_id

        if self.local_search is not None:
            result['LocalSearch'] = self.local_search

        if self.user_region_id is not None:
            result['UserRegionId'] = self.user_region_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AliUid') is not None:
            self.ali_uid = m.get('AliUid')

        if m.get('DirectoryId') is not None:
            self.directory_id = m.get('DirectoryId')

        if m.get('DirectoryType') is not None:
            self.directory_type = m.get('DirectoryType')

        if m.get('EndUserId') is not None:
            self.end_user_id = m.get('EndUserId')

        if m.get('LocalSearch') is not None:
            self.local_search = m.get('LocalSearch')

        if m.get('UserRegionId') is not None:
            self.user_region_id = m.get('UserRegionId')

        return self

