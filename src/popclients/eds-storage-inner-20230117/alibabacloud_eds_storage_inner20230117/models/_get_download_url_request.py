# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations

from darabonba.model import DaraModel

class GetDownloadUrlRequest(DaraModel):
    def __init__(
        self,
        file_id: str = None,
        product_type: str = None,
        user_ali_uid: int = None,
        wy_drive_owner_id: str = None,
    ):
        # This parameter is required.
        self.file_id = file_id
        # This parameter is required.
        self.product_type = product_type
        # This parameter is required.
        self.user_ali_uid = user_ali_uid
        # This parameter is required.
        self.wy_drive_owner_id = wy_drive_owner_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.file_id is not None:
            result['FileId'] = self.file_id

        if self.product_type is not None:
            result['ProductType'] = self.product_type

        if self.user_ali_uid is not None:
            result['UserAliUid'] = self.user_ali_uid

        if self.wy_drive_owner_id is not None:
            result['WyDriveOwnerId'] = self.wy_drive_owner_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('FileId') is not None:
            self.file_id = m.get('FileId')

        if m.get('ProductType') is not None:
            self.product_type = m.get('ProductType')

        if m.get('UserAliUid') is not None:
            self.user_ali_uid = m.get('UserAliUid')

        if m.get('WyDriveOwnerId') is not None:
            self.wy_drive_owner_id = m.get('WyDriveOwnerId')

        return self

