# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations

from darabonba.model import DaraModel

class GetGlobalTokenRequest(DaraModel):
    def __init__(
        self,
        directory_id: str = None,
        directory_type: str = None,
        domain_name: str = None,
        end_user_id: str = None,
        sid: str = None,
        user_ali_uid: int = None,
    ):
        self.directory_id = directory_id
        self.directory_type = directory_type
        self.domain_name = domain_name
        self.end_user_id = end_user_id
        self.sid = sid
        self.user_ali_uid = user_ali_uid

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.directory_id is not None:
            result['DirectoryId'] = self.directory_id

        if self.directory_type is not None:
            result['DirectoryType'] = self.directory_type

        if self.domain_name is not None:
            result['DomainName'] = self.domain_name

        if self.end_user_id is not None:
            result['EndUserId'] = self.end_user_id

        if self.sid is not None:
            result['Sid'] = self.sid

        if self.user_ali_uid is not None:
            result['UserAliUid'] = self.user_ali_uid

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('DirectoryId') is not None:
            self.directory_id = m.get('DirectoryId')

        if m.get('DirectoryType') is not None:
            self.directory_type = m.get('DirectoryType')

        if m.get('DomainName') is not None:
            self.domain_name = m.get('DomainName')

        if m.get('EndUserId') is not None:
            self.end_user_id = m.get('EndUserId')

        if m.get('Sid') is not None:
            self.sid = m.get('Sid')

        if m.get('UserAliUid') is not None:
            self.user_ali_uid = m.get('UserAliUid')

        return self

