# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations

from typing import List

from alibabacloud_eds_storage_inner20230117 import models as main_models
from darabonba.model import DaraModel

class DescribeFileTransferApprovalConfigsRequest(DaraModel):
    def __init__(
        self,
        account_type: str = None,
        ad_domain: str = None,
        scene: str = None,
        task_type: str = None,
        user_ali_uid: int = None,
        user_groups: List[str] = None,
        user_infos: List[main_models.DescribeFileTransferApprovalConfigsRequestUserInfos] = None,
    ):
        self.account_type = account_type
        self.ad_domain = ad_domain
        self.scene = scene
        self.task_type = task_type
        self.user_ali_uid = user_ali_uid
        self.user_groups = user_groups
        self.user_infos = user_infos

    def validate(self):
        if self.user_infos:
            for v1 in self.user_infos:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.account_type is not None:
            result['AccountType'] = self.account_type

        if self.ad_domain is not None:
            result['AdDomain'] = self.ad_domain

        if self.scene is not None:
            result['Scene'] = self.scene

        if self.task_type is not None:
            result['TaskType'] = self.task_type

        if self.user_ali_uid is not None:
            result['UserAliUid'] = self.user_ali_uid

        if self.user_groups is not None:
            result['UserGroups'] = self.user_groups

        result['UserInfos'] = []
        if self.user_infos is not None:
            for k1 in self.user_infos:
                result['UserInfos'].append(k1.to_map() if k1 else None)

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AccountType') is not None:
            self.account_type = m.get('AccountType')

        if m.get('AdDomain') is not None:
            self.ad_domain = m.get('AdDomain')

        if m.get('Scene') is not None:
            self.scene = m.get('Scene')

        if m.get('TaskType') is not None:
            self.task_type = m.get('TaskType')

        if m.get('UserAliUid') is not None:
            self.user_ali_uid = m.get('UserAliUid')

        if m.get('UserGroups') is not None:
            self.user_groups = m.get('UserGroups')

        self.user_infos = []
        if m.get('UserInfos') is not None:
            for k1 in m.get('UserInfos'):
                temp_model = main_models.DescribeFileTransferApprovalConfigsRequestUserInfos()
                self.user_infos.append(temp_model.from_map(k1))

        return self

class DescribeFileTransferApprovalConfigsRequestUserInfos(DaraModel):
    def __init__(
        self,
        group_ids: List[str] = None,
        wy_id: str = None,
    ):
        self.group_ids = group_ids
        self.wy_id = wy_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.group_ids is not None:
            result['GroupIds'] = self.group_ids

        if self.wy_id is not None:
            result['WyId'] = self.wy_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('GroupIds') is not None:
            self.group_ids = m.get('GroupIds')

        if m.get('WyId') is not None:
            self.wy_id = m.get('WyId')

        return self

