# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations

from typing import List

from darabonba.model import DaraModel

class DescribePeripheralDriversRequest(DaraModel):
    def __init__(
        self,
        brand: str = None,
        device_type: str = None,
        driver_ids: List[str] = None,
        driver_name: str = None,
        filter: str = None,
        include_others: bool = None,
        lang: str = None,
        owner_type: str = None,
        page_number: int = None,
        page_size: int = None,
        user_ali_uid: int = None,
    ):
        self.brand = brand
        self.device_type = device_type
        self.driver_ids = driver_ids
        self.driver_name = driver_name
        self.filter = filter
        self.include_others = include_others
        self.lang = lang
        self.owner_type = owner_type
        self.page_number = page_number
        self.page_size = page_size
        self.user_ali_uid = user_ali_uid

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.brand is not None:
            result['Brand'] = self.brand

        if self.device_type is not None:
            result['DeviceType'] = self.device_type

        if self.driver_ids is not None:
            result['DriverIds'] = self.driver_ids

        if self.driver_name is not None:
            result['DriverName'] = self.driver_name

        if self.filter is not None:
            result['Filter'] = self.filter

        if self.include_others is not None:
            result['IncludeOthers'] = self.include_others

        if self.lang is not None:
            result['Lang'] = self.lang

        if self.owner_type is not None:
            result['OwnerType'] = self.owner_type

        if self.page_number is not None:
            result['PageNumber'] = self.page_number

        if self.page_size is not None:
            result['PageSize'] = self.page_size

        if self.user_ali_uid is not None:
            result['UserAliUid'] = self.user_ali_uid

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Brand') is not None:
            self.brand = m.get('Brand')

        if m.get('DeviceType') is not None:
            self.device_type = m.get('DeviceType')

        if m.get('DriverIds') is not None:
            self.driver_ids = m.get('DriverIds')

        if m.get('DriverName') is not None:
            self.driver_name = m.get('DriverName')

        if m.get('Filter') is not None:
            self.filter = m.get('Filter')

        if m.get('IncludeOthers') is not None:
            self.include_others = m.get('IncludeOthers')

        if m.get('Lang') is not None:
            self.lang = m.get('Lang')

        if m.get('OwnerType') is not None:
            self.owner_type = m.get('OwnerType')

        if m.get('PageNumber') is not None:
            self.page_number = m.get('PageNumber')

        if m.get('PageSize') is not None:
            self.page_size = m.get('PageSize')

        if m.get('UserAliUid') is not None:
            self.user_ali_uid = m.get('UserAliUid')

        return self

