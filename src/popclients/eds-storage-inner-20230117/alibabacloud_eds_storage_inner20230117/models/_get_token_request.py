# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations

from darabonba.model import DaraModel

class GetTokenRequest(DaraModel):
    def __init__(
        self,
        cds_id: str = None,
        end_user_id: str = None,
        region_id: str = None,
        user_ali_uid: int = None,
        user_region_id: str = None,
    ):
        self.cds_id = cds_id
        self.end_user_id = end_user_id
        self.region_id = region_id
        self.user_ali_uid = user_ali_uid
        self.user_region_id = user_region_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.cds_id is not None:
            result['CdsId'] = self.cds_id

        if self.end_user_id is not None:
            result['EndUserId'] = self.end_user_id

        if self.region_id is not None:
            result['RegionId'] = self.region_id

        if self.user_ali_uid is not None:
            result['UserAliUid'] = self.user_ali_uid

        if self.user_region_id is not None:
            result['UserRegionId'] = self.user_region_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('CdsId') is not None:
            self.cds_id = m.get('CdsId')

        if m.get('EndUserId') is not None:
            self.end_user_id = m.get('EndUserId')

        if m.get('RegionId') is not None:
            self.region_id = m.get('RegionId')

        if m.get('UserAliUid') is not None:
            self.user_ali_uid = m.get('UserAliUid')

        if m.get('UserRegionId') is not None:
            self.user_region_id = m.get('UserRegionId')

        return self

