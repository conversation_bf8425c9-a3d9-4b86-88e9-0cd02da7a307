# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations

from darabonba.model import DaraModel

class AddPeripheralDriverRequest(DaraModel):
    def __init__(
        self,
        brand: str = None,
        driver_id: str = None,
        driver_name: str = None,
        file_path: str = None,
    ):
        # This parameter is required.
        self.brand = brand
        # This parameter is required.
        self.driver_id = driver_id
        # This parameter is required.
        self.driver_name = driver_name
        # This parameter is required.
        self.file_path = file_path

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.brand is not None:
            result['Brand'] = self.brand

        if self.driver_id is not None:
            result['DriverId'] = self.driver_id

        if self.driver_name is not None:
            result['DriverName'] = self.driver_name

        if self.file_path is not None:
            result['FilePath'] = self.file_path

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Brand') is not None:
            self.brand = m.get('Brand')

        if m.get('DriverId') is not None:
            self.driver_id = m.get('DriverId')

        if m.get('DriverName') is not None:
            self.driver_name = m.get('DriverName')

        if m.get('FilePath') is not None:
            self.file_path = m.get('FilePath')

        return self

