# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations

from typing import List

from alibabacloud_eds_storage_inner20230117 import models as main_models
from darabonba.model import DaraModel

class CreateGrayVersionsRequest(DaraModel):
    def __init__(
        self,
        user_ali_uid: int = None,
        versions: List[main_models.CreateGrayVersionsRequestVersions] = None,
    ):
        # This parameter is required.
        self.user_ali_uid = user_ali_uid
        self.versions = versions

    def validate(self):
        if self.versions:
            for v1 in self.versions:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.user_ali_uid is not None:
            result['UserAliUid'] = self.user_ali_uid

        result['Versions'] = []
        if self.versions is not None:
            for k1 in self.versions:
                result['Versions'].append(k1.to_map() if k1 else None)

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('UserAliUid') is not None:
            self.user_ali_uid = m.get('UserAliUid')

        self.versions = []
        if m.get('Versions') is not None:
            for k1 in m.get('Versions'):
                temp_model = main_models.CreateGrayVersionsRequestVersions()
                self.versions.append(temp_model.from_map(k1))

        return self

class CreateGrayVersionsRequestVersions(DaraModel):
    def __init__(
        self,
        fota_task_id: str = None,
        fota_version: str = None,
        gmt_sys_effect: str = None,
        gray_level_id: str = None,
    ):
        # This parameter is required.
        self.fota_task_id = fota_task_id
        # This parameter is required.
        self.fota_version = fota_version
        self.gmt_sys_effect = gmt_sys_effect
        # This parameter is required.
        self.gray_level_id = gray_level_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.fota_task_id is not None:
            result['FotaTaskId'] = self.fota_task_id

        if self.fota_version is not None:
            result['FotaVersion'] = self.fota_version

        if self.gmt_sys_effect is not None:
            result['GmtSysEffect'] = self.gmt_sys_effect

        if self.gray_level_id is not None:
            result['GrayLevelId'] = self.gray_level_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('FotaTaskId') is not None:
            self.fota_task_id = m.get('FotaTaskId')

        if m.get('FotaVersion') is not None:
            self.fota_version = m.get('FotaVersion')

        if m.get('GmtSysEffect') is not None:
            self.gmt_sys_effect = m.get('GmtSysEffect')

        if m.get('GrayLevelId') is not None:
            self.gray_level_id = m.get('GrayLevelId')

        return self

