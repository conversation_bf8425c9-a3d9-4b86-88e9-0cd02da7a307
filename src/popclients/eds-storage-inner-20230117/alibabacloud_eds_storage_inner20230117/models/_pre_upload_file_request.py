# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations

from typing import List

from darabonba.model import DaraModel

class PreUploadFileRequest(DaraModel):
    def __init__(
        self,
        endpoint_type: str = None,
        file_name: str = None,
        is_folder: bool = None,
        parent_folder_id: str = None,
        parent_folder_path: str = None,
        part_number: int = None,
        product_type: str = None,
        size: int = None,
        tags: List[str] = None,
        user_ali_uid: int = None,
        wy_drive_owner_id: str = None,
    ):
        self.endpoint_type = endpoint_type
        # This parameter is required.
        self.file_name = file_name
        self.is_folder = is_folder
        self.parent_folder_id = parent_folder_id
        self.parent_folder_path = parent_folder_path
        self.part_number = part_number
        # This parameter is required.
        self.product_type = product_type
        self.size = size
        self.tags = tags
        # This parameter is required.
        self.user_ali_uid = user_ali_uid
        # This parameter is required.
        self.wy_drive_owner_id = wy_drive_owner_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.endpoint_type is not None:
            result['EndpointType'] = self.endpoint_type

        if self.file_name is not None:
            result['FileName'] = self.file_name

        if self.is_folder is not None:
            result['IsFolder'] = self.is_folder

        if self.parent_folder_id is not None:
            result['ParentFolderId'] = self.parent_folder_id

        if self.parent_folder_path is not None:
            result['ParentFolderPath'] = self.parent_folder_path

        if self.part_number is not None:
            result['PartNumber'] = self.part_number

        if self.product_type is not None:
            result['ProductType'] = self.product_type

        if self.size is not None:
            result['Size'] = self.size

        if self.tags is not None:
            result['Tags'] = self.tags

        if self.user_ali_uid is not None:
            result['UserAliUid'] = self.user_ali_uid

        if self.wy_drive_owner_id is not None:
            result['WyDriveOwnerId'] = self.wy_drive_owner_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('EndpointType') is not None:
            self.endpoint_type = m.get('EndpointType')

        if m.get('FileName') is not None:
            self.file_name = m.get('FileName')

        if m.get('IsFolder') is not None:
            self.is_folder = m.get('IsFolder')

        if m.get('ParentFolderId') is not None:
            self.parent_folder_id = m.get('ParentFolderId')

        if m.get('ParentFolderPath') is not None:
            self.parent_folder_path = m.get('ParentFolderPath')

        if m.get('PartNumber') is not None:
            self.part_number = m.get('PartNumber')

        if m.get('ProductType') is not None:
            self.product_type = m.get('ProductType')

        if m.get('Size') is not None:
            self.size = m.get('Size')

        if m.get('Tags') is not None:
            self.tags = m.get('Tags')

        if m.get('UserAliUid') is not None:
            self.user_ali_uid = m.get('UserAliUid')

        if m.get('WyDriveOwnerId') is not None:
            self.wy_drive_owner_id = m.get('WyDriveOwnerId')

        return self

