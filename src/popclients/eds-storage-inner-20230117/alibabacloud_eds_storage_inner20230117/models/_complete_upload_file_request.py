# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations

from typing import List

from alibabacloud_eds_storage_inner20230117 import models as main_models
from darabonba.model import DaraModel

class CompleteUploadFileRequest(DaraModel):
    def __init__(
        self,
        complete_uploaded_parts: List[main_models.CompleteUploadFileRequestCompleteUploadedParts] = None,
        file_id: str = None,
        product_type: str = None,
        upload_id: str = None,
        user_ali_uid: int = None,
        wy_drive_owner_id: str = None,
    ):
        self.complete_uploaded_parts = complete_uploaded_parts
        # This parameter is required.
        self.file_id = file_id
        # This parameter is required.
        self.product_type = product_type
        self.upload_id = upload_id
        # This parameter is required.
        self.user_ali_uid = user_ali_uid
        # This parameter is required.
        self.wy_drive_owner_id = wy_drive_owner_id

    def validate(self):
        if self.complete_uploaded_parts:
            for v1 in self.complete_uploaded_parts:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        result['CompleteUploadedParts'] = []
        if self.complete_uploaded_parts is not None:
            for k1 in self.complete_uploaded_parts:
                result['CompleteUploadedParts'].append(k1.to_map() if k1 else None)

        if self.file_id is not None:
            result['FileId'] = self.file_id

        if self.product_type is not None:
            result['ProductType'] = self.product_type

        if self.upload_id is not None:
            result['UploadId'] = self.upload_id

        if self.user_ali_uid is not None:
            result['UserAliUid'] = self.user_ali_uid

        if self.wy_drive_owner_id is not None:
            result['WyDriveOwnerId'] = self.wy_drive_owner_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        self.complete_uploaded_parts = []
        if m.get('CompleteUploadedParts') is not None:
            for k1 in m.get('CompleteUploadedParts'):
                temp_model = main_models.CompleteUploadFileRequestCompleteUploadedParts()
                self.complete_uploaded_parts.append(temp_model.from_map(k1))

        if m.get('FileId') is not None:
            self.file_id = m.get('FileId')

        if m.get('ProductType') is not None:
            self.product_type = m.get('ProductType')

        if m.get('UploadId') is not None:
            self.upload_id = m.get('UploadId')

        if m.get('UserAliUid') is not None:
            self.user_ali_uid = m.get('UserAliUid')

        if m.get('WyDriveOwnerId') is not None:
            self.wy_drive_owner_id = m.get('WyDriveOwnerId')

        return self



class CompleteUploadFileRequestCompleteUploadedParts(DaraModel):
    def __init__(
        self,
        etag: str = None,
        part_number: str = None,
    ):
        self.etag = etag
        self.part_number = part_number

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.etag is not None:
            result['ETag'] = self.etag

        if self.part_number is not None:
            result['PartNumber'] = self.part_number

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ETag') is not None:
            self.etag = m.get('ETag')

        if m.get('PartNumber') is not None:
            self.part_number = m.get('PartNumber')

        return self

