# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations

from typing import List

from alibabacloud_eds_storage_inner20230117 import models as main_models
from darabonba.model import DaraModel

class TurnOnGrayscaleWhitelistRequest(DaraModel):
    def __init__(
        self,
        append: main_models.TurnOnGrayscaleWhitelistRequestAppend = None,
        user_ali_uid: int = None,
    ):
        self.append = append
        # This parameter is required.
        self.user_ali_uid = user_ali_uid

    def validate(self):
        if self.append:
            self.append.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.append is not None:
            result['Append'] = self.append.to_map()

        if self.user_ali_uid is not None:
            result['UserAliUid'] = self.user_ali_uid

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Append') is not None:
            temp_model = main_models.TurnOnGrayscaleWhitelistRequestAppend()
            self.append = temp_model.from_map(m.get('Append'))

        if m.get('UserAliUid') is not None:
            self.user_ali_uid = m.get('UserAliUid')

        return self

class TurnOnGrayscaleWhitelistRequestAppend(DaraModel):
    def __init__(
        self,
        allow_update_cron: str = None,
        os_types: List[str] = None,
    ):
        self.allow_update_cron = allow_update_cron
        # This parameter is required.
        self.os_types = os_types

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.allow_update_cron is not None:
            result['AllowUpdateCron'] = self.allow_update_cron

        if self.os_types is not None:
            result['OsTypes'] = self.os_types

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AllowUpdateCron') is not None:
            self.allow_update_cron = m.get('AllowUpdateCron')

        if m.get('OsTypes') is not None:
            self.os_types = m.get('OsTypes')

        return self

