# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations

from darabonba.model import DaraModel

class DescribeFileTransferApprovalConfigRequest(DaraModel):
    def __init__(
        self,
        account_type: str = None,
        ad_domain: str = None,
        scene: str = None,
        task_type: str = None,
        user_ali_uid: int = None,
        end_user_id: str = None,
        wy_id: str = None,
    ):
        self.account_type = account_type
        self.ad_domain = ad_domain
        self.scene = scene
        self.task_type = task_type
        self.user_ali_uid = user_ali_uid
        self.end_user_id = end_user_id
        self.wy_id = wy_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.account_type is not None:
            result['AccountType'] = self.account_type

        if self.ad_domain is not None:
            result['AdDomain'] = self.ad_domain

        if self.scene is not None:
            result['Scene'] = self.scene

        if self.task_type is not None:
            result['TaskType'] = self.task_type

        if self.user_ali_uid is not None:
            result['UserAliUid'] = self.user_ali_uid

        if self.end_user_id is not None:
            result['endUserId'] = self.end_user_id

        if self.wy_id is not None:
            result['wyId'] = self.wy_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AccountType') is not None:
            self.account_type = m.get('AccountType')

        if m.get('AdDomain') is not None:
            self.ad_domain = m.get('AdDomain')

        if m.get('Scene') is not None:
            self.scene = m.get('Scene')

        if m.get('TaskType') is not None:
            self.task_type = m.get('TaskType')

        if m.get('UserAliUid') is not None:
            self.user_ali_uid = m.get('UserAliUid')

        if m.get('endUserId') is not None:
            self.end_user_id = m.get('endUserId')

        if m.get('wyId') is not None:
            self.wy_id = m.get('wyId')

        return self

