#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PPT业务服务
处理PPT相关的业务逻辑，包括认证、生成、保存和下载等功能
"""
import time
from typing import Optional
import uuid
import json

from memory.events import (
    ArtifactEvent,
    CustomEvent,
    RunStartedEvent,
    RunFinishedEvent,
    RunErrorEvent,
)
from src.application.ppt_api_models import (
    GetPPTAuthCodeResponse,
    PPTConfigOptions,
    OptionConfig,
)
from src.shared.config.nacos_config import nacos_config_manager
from loguru import logger

from src.popclients.aippt_client import get_aippt_client
from src.infrastructure.memory.memory_sdk import MemorySDK
from src.infrastructure.redis.client import RedisClient


class PPTService:
    """PPT业务服务"""

    # PPT配置相关常量
    PPT_CONFIG_DATA_ID = "wuying-alpha-service:aippt_config"
    PPT_CONFIG_GROUP = "DEFAULT_GROUP"

    def __init__(self):
        self.aippt_client = None  # 延迟初始化
        self.memory = None  # 延迟初始化
        self.nacos_config_manager = nacos_config_manager  # 配置管理器
        logger.info("[PPTService] PPT服务初始化完成")

    def _get_aippt_client(self):
        """获取AIPPT客户端实例"""
        if self.aippt_client is None:
            self.aippt_client = get_aippt_client()
        return self.aippt_client

    def _get_memory(self):
        """获取MemorySDK实例"""
        if self.memory is None:
            self.memory = MemorySDK()
        return self.memory

    def _get_aippt_token(self, ali_uid: int):
        """
        获取AIPPT token，优先从缓存获取，如果不存在则重新获取

        Args:
            ali_uid: 用户ID
        """
        logger.info(f"[PPTService] 获取AIPPT token: ali_uid={ali_uid}")

        # 优先从缓存中获取token（按用户缓存）
        redis_client = RedisClient()
        cache_key = f"aippt_token_{ali_uid}"
        token = redis_client.get(cache_key)
        if token:
            logger.info(f"[PPTService] 从缓存中获取AIPPT token: {token}")
            return token

        # 如果缓存中没有token，则重新获取，并缓存
        logger.info(f"[PPTService] 从缓存中没有获取到AIPPT token，重新获取")
        client = self._get_aippt_client()
        response = client.get_aippt_token(ali_uid=ali_uid)
        redis_client.set(cache_key, response.token, response.time_expire - 300)

        return response.token

    def _poll_export_result(
        self, task_key: str, token: str, timeout: int = 60
    ) -> Optional[str]:
        """
        轮询获取导出结果

        Args:
            task_key: 导出任务标识
            token: 认证token
            timeout: 超时时间（秒），默认60秒

        Returns:
            Optional[str]: 下载链接，如果超时则返回None
        """
        logger.info(
            f"[PPTService] 开始轮询导出结果: task_key={task_key}, timeout={timeout}s"
        )

        start_time = time.time()
        poll_interval = 0.5  # 每500毫秒查询一次

        while time.time() - start_time < timeout:
            try:
                client = self._get_aippt_client()
                result = client.get_ppt_export_result(task_key=task_key, token=token)
                download_url = result.get('download_url')
                message = result.get('message', '')

                if download_url:
                    elapsed_time = time.time() - start_time
                    logger.info(
                        f"[PPTService] 导出成功: task_key={task_key}, 耗时={elapsed_time:.1f}s"
                    )
                    return download_url

                elapsed_time = time.time() - start_time
                # logger.info(
                #     f"[PPTService] 导出进行中: task_key={task_key}, 状态={message}, 已等待={elapsed_time:.1f}s"
                # )

                # 等待下次查询
                time.sleep(poll_interval)

            except Exception as e:
                logger.warning(
                    f"[PPTService] 查询导出结果失败: task_key={task_key}, error={str(e)}"
                )
                time.sleep(poll_interval)

        # 超时
        elapsed_time = time.time() - start_time
        logger.error(
            f"[PPTService] 导出超时: task_key={task_key}, 超时时间={elapsed_time:.1f}s"
        )
        return None

    def get_ppt_auth_code(
        self,
        ali_uid: int,
    ) -> GetPPTAuthCodeResponse:
        """
        获取PPT认证code

        Args:
            ali_uid: 用户ID

        Returns:
            GetPPTAuthCodeResponse: 认证码响应
        """
        logger.info(f"[PPTService] 获取PPT认证code: ali_uid={ali_uid}")

        client = self._get_aippt_client()
        response = client.get_aippt_auth_code(ali_uid=ali_uid)

        return response

    def bind_ppt_to_session(
        self,
        session_id: str,
        event_id: Optional[str],
        ppt_id: str,
        ali_uid: int,
    ) -> str:
        """
        绑定PPT到会话

        Args:
            session_id: 会话ID
            event_id: 事件ID，可选，如果提供则更新该事件的扩展信息
            ppt_id: PPT作品ID
            ali_uid: 用户ID

        Returns:
            str: 制品ID
        """
        logger.info(
            f"[PPTService] 绑定PPT到会话: session_id={session_id}, event_id={event_id}, ppt_id={ppt_id}, ali_uid={ali_uid}"
        )

        client = self._get_aippt_client()
        token = self._get_aippt_token(ali_uid=ali_uid)
        memory = self._get_memory()

        # 生成随机run_id
        run_id = str(uuid.uuid4())

        # 1. 创建开始事件
        start_event = RunStartedEvent(session_id=session_id, run_id=run_id)
        memory.add_event(start_event, session_id)
        logger.info(
            f"[PPTService] 创建开始事件: session_id={session_id}, run_id={run_id}"
        )

        try:
            # 获取ppt详情
            ppt_info = client.get_ppt_info(ppt_id=ppt_id, token=token)

            # 2. 创建制品事件
            artifact_event = ArtifactEvent(
                session_id=session_id,
                run_id=run_id,
                artifact_type="aippt",
                file_type="ppt",
                file_name=ppt_info.get('name', 'AI PPT'),
                content=ppt_id,
                is_process_file=False,
            )
            memory.add_event(artifact_event, session_id)

            # 获取制品id
            artifact_id = artifact_event.artifact_id

            # 如果提供了event_id，则更新该事件
            if event_id:
                # 构建要更新的扩展信息
                ext_info = {"ppt_id": ppt_id, "artifact_id": artifact_id}

                # 更新事件
                memory.update_event(event_id=event_id, ext_info=json.dumps(ext_info))
                logger.info(f"[PPTService] 已更新事件: event_id={event_id}")
            else:
                logger.info(f"[PPTService] 未提供event_id，跳过事件更新")

            # 3. 创建完成事件
            finish_event = RunFinishedEvent(session_id=session_id, run_id=run_id)
            memory.add_event(finish_event, session_id)
            logger.info(
                f"[PPTService] 创建完成事件: session_id={session_id}, run_id={run_id}"
            )

            logger.info(
                f"[PPTService] PPT绑定成功: session_id={session_id}, artifact_id={artifact_id}"
            )
            return artifact_id

        except Exception as e:
            # 4. 如果出现错误，创建错误事件
            logger.error(f"[PPTService] PPT绑定失败: {e}")
            error_event = RunErrorEvent(
                session_id=session_id,
                run_id=run_id,
                message=str(e),
                code="PPT_BIND_ERROR",
            )
            memory.add_event(error_event, session_id)
            logger.info(
                f"[PPTService] 创建错误事件: session_id={session_id}, run_id={run_id}, error={e}"
            )
            raise

    def get_ppt_thumbnail(
        self,
        ppt_id: str,
        ali_uid: int,
    ) -> str:
        """
        获取PPT缩略图

        Args:
            ppt_id: PPT作品ID
            ali_uid: 用户ID
        """
        try:
            logger.info(
                f"[PPTService] 获取PPT缩略图: ppt_id={ppt_id}, ali_uid={ali_uid}"
            )

            client = self._get_aippt_client()

            # 获取token
            token = self._get_aippt_token(ali_uid=ali_uid)

            # 获取ppt详情
            ppt_info = client.get_ppt_info(ppt_id=ppt_id, token=token)
            logger.info(
                f"[PPTService] 获取PPT详情成功: {ppt_info.get('name', 'Unknown')}"
            )

            return ppt_info.get('cover_url', '')

        except Exception as e:
            logger.error(
                f"[PPTService] 获取PPT缩略图失败: ppt_id={ppt_id}, error={str(e)}"
            )
            raise PPTServiceError(f"获取PPT缩略图失败: {str(e)}") from e

    def save_ppt(self, ppt_id: str, ali_uid: int, format: str = "ppt") -> str:
        """
        保存PPT

        Args:
            ppt_id: PPT作品ID
            ali_uid: 用户ID
            format: 导出格式，png|jpeg|pdf|ppt，可选，默认ppt

        Returns:
            str: 下载链接
        """
        try:
            logger.info(
                f"[PPTService] 开始保存PPT: ppt_id={ppt_id}, ali_uid={ali_uid}, format={format}"
            )

            client = self._get_aippt_client()

            # 获取token
            token = self._get_aippt_token(ali_uid=ali_uid)

            # 获取ppt详情
            ppt_info = client.get_ppt_info(ppt_id=ppt_id, token=token)
            logger.info(
                f"[PPTService] 获取PPT详情成功: {ppt_info.get('name', 'Unknown')}"
            )

            # 导出ppt
            task_key = client.export_ppt(ppt_id=ppt_id, token=token, format=format)
            logger.info(
                f"[PPTService] 启动PPT导出: task_key={task_key}, format={format}"
            )

            # 轮询获取导出结果（最多1分钟，每秒查询一次）
            download_url = self._poll_export_result(task_key, token, timeout=60)

            if download_url:
                logger.info(
                    f"[PPTService] PPT保存成功: ppt_id={ppt_id}, download_url={download_url}"
                )

                return download_url
            else:
                logger.error(f"[PPTService] PPT保存失败，导出超时: ppt_id={ppt_id}")
                raise PPTServiceError(f"PPT导出超时，请稍后重试")

        except Exception as e:
            logger.error(f"[PPTService] 保存PPT失败: ppt_id={ppt_id}, error={str(e)}")
            raise PPTServiceError(f"保存PPT失败: {str(e)}") from e

    def delete_ppt(
        self,
        ppt_id: str,
        ali_uid: int,
    ) -> bool:
        """
        删除PPT

        Args:
            ppt_id: PPT作品ID
            ali_uid: 用户ID

        Returns:
            bool: 删除是否成功
        """
        try:
            logger.info(f"[PPTService] 开始删除PPT: ppt_id={ppt_id}, ali_uid={ali_uid}")

            client = self._get_aippt_client()

            # 获取token
            token = self._get_aippt_token(ali_uid=ali_uid)

            # 删除ppt
            success = client.delete_ppt(ppt_id=ppt_id, token=token)

            if success:
                logger.info(f"[PPTService] PPT删除成功: ppt_id={ppt_id}")
                return True
            else:
                logger.error(f"[PPTService] PPT删除失败: ppt_id={ppt_id}")
                raise PPTServiceError(f"PPT删除失败")

        except Exception as e:
            logger.error(f"[PPTService] 删除PPT失败: ppt_id={ppt_id}, error={str(e)}")
            raise PPTServiceError(f"删除PPT失败: {str(e)}") from e

    def get_ppt_config_list(self) -> PPTConfigOptions:
        """
        获取PPT配置清单

        Returns:
            PPTConfigOptions: PPT配置选项数据
        """
        logger.info("[PPTService] 获取PPT配置清单")

        # 尝试从配置中心获取配置
        try:
            config = self.nacos_config_manager.get_config(
                data_id=self.PPT_CONFIG_DATA_ID, group=self.PPT_CONFIG_GROUP
            )

            if config and "pptConfig" in config:
                ppt_config = config["pptConfig"]
                logger.info("[PPTService] 从配置中心获取PPT配置成功")

                # 使用配置中心的数据
                config_data = PPTConfigOptions(
                    page_range_options=OptionConfig(
                        title="页数范围", items=ppt_config.get("pageRangeOptions", [])
                    ),
                    audience_options=OptionConfig(
                        title="受众", items=ppt_config.get("audienceOptions", [])
                    ),
                    scenario_options=OptionConfig(
                        title="场景", items=ppt_config.get("scenarioOptions", [])
                    ),
                    tone_options=OptionConfig(
                        title="语气", items=ppt_config.get("toneOptions", [])
                    ),
                    language_options=OptionConfig(
                        title="语言", items=ppt_config.get("languageOptions", [])
                    ),
                )

                logger.info("[PPTService] PPT配置清单获取成功")
                return config_data

        except Exception as e:
            logger.error(f"[PPTService] 从配置中心获取配置失败: {e}")

        # 降级到硬编码的配置数据
        logger.info("[PPTService] 使用降级PPT配置")
        config_data = PPTConfigOptions(
            page_range_options=OptionConfig(
                title="页数范围",
                items=["10-15", "15-20", "20-30", "30-35", "35-40", "不限"],
            ),
            audience_options=OptionConfig(
                title="受众",
                items=[
                    "大众", "投资者", "学生", "老师", "老板", "面试官", "同事同行", "在线访客", "组员"
                ],
            ),
            scenario_options=OptionConfig(
                title="场景",
                items=[
                    "通用", "个人介绍", "商业计划书", "解决方案", "会议流程", "年度计划", "年度总结", "健康科普", "财务报告", "项目计划书", "商业博文"
                ],
            ),
            tone_options=OptionConfig(
                title="语气", items=["专业", "励志", "幽默", "亲切", "自信", "温柔"]
            ),
            language_options=OptionConfig(
                title="语言", items=["简体中文", "英文", "日语"]
            ),
        )

        logger.info("[PPTService] PPT配置清单获取成功")
        return config_data


class PPTServiceError(Exception):
    """PPT服务异常"""

    pass


# 全局单例实例
ppt_service = PPTService()
