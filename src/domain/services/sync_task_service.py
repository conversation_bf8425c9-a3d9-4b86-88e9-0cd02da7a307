#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同步任务管理服务
"""

import asyncio
import threading
import time
import uuid
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime
from typing import Dict, List, Optional
from loguru import logger

from src.application.file_api_models import SyncFileResult, SyncTaskStatus, SyncToStorageResponse
from src.domain.services.auth_service import AuthContext


class SyncTaskManager:
    """同步任务管理器"""
    
    def __init__(self):
        self._tasks: Dict[str, SyncTaskData] = {}
        self._lock = threading.RLock()
        self._executor = ThreadPoolExecutor(max_workers=5, thread_name_prefix="sync-task")
        logger.info("[SyncTaskManager] 同步任务管理器已初始化")
    
    def create_task(
        self, 
        session_id: str, 
        artifact_ids: List[str], 
        context: AuthContext
    ) -> str:
        """创建同步任务并返回任务ID"""
        
        task_id = self._generate_task_id()
        current_time = datetime.now().isoformat()
        
        with self._lock:
            task_data = SyncTaskData(
                task_id=task_id,
                session_id=session_id,
                artifact_ids=artifact_ids,
                context=context,
                status="processing",
                progress=0.0,
                total_count=len(artifact_ids),
                completed_count=0,
                success_count=0,
                failed_count=0,
                message="任务已创建，正在处理中...",
                created_time=current_time,
                updated_time=current_time,
                results=[]
            )
            self._tasks[task_id] = task_data
        
        # 提交任务到线程池执行
        self._executor.submit(self._execute_sync_task, task_id)
        
        logger.info(f"[SyncTaskManager] 已创建同步任务: task_id={task_id}, artifact_count={len(artifact_ids)}")
        return task_id
    
    def get_task_status(self, task_id: str) -> Optional[SyncTaskStatus]:
        """获取任务状态"""
        
        with self._lock:
            task_data = self._tasks.get(task_id)
            if not task_data:
                return None
            
            return SyncTaskStatus(
                task_id=task_data.task_id,
                status=task_data.status,
                progress=task_data.progress,
                total_count=task_data.total_count,
                completed_count=task_data.completed_count,
                success_count=task_data.success_count,
                failed_count=task_data.failed_count,
                message=task_data.message,
                created_time=task_data.created_time,
                updated_time=task_data.updated_time,
                results=task_data.results if task_data.status in ["completed", "failed"] else None
            )
    
    def cleanup_old_tasks(self, hours: int = 24):
        """清理旧任务（默认24小时前的任务）"""
        
        cutoff_time = datetime.now().timestamp() - (hours * 3600)
        
        with self._lock:
            task_ids_to_remove = []
            for task_id, task_data in self._tasks.items():
                try:
                    created_timestamp = datetime.fromisoformat(task_data.created_time).timestamp()
                    if created_timestamp < cutoff_time:
                        task_ids_to_remove.append(task_id)
                except Exception as e:
                    logger.warning(f"[SyncTaskManager] 解析任务创建时间失败: task_id={task_id}, error={e}")
            
            for task_id in task_ids_to_remove:
                del self._tasks[task_id]
                logger.info(f"[SyncTaskManager] 已清理旧任务: task_id={task_id}")
    
    def shutdown(self):
        """关闭任务管理器"""
        logger.info("[SyncTaskManager] 正在关闭同步任务管理器...")
        self._executor.shutdown(wait=True)
        logger.info("[SyncTaskManager] 同步任务管理器已关闭")
    
    def _generate_task_id(self) -> str:
        """生成唯一任务ID"""
        return f"sync-{uuid.uuid4().hex[:16]}"
    
    def _execute_sync_task(self, task_id: str):
        """执行同步任务"""
        
        try:
            logger.info(f"[SyncTaskManager] 开始执行同步任务: task_id={task_id}")
            
            with self._lock:
                task_data = self._tasks.get(task_id)
                if not task_data:
                    logger.error(f"[SyncTaskManager] 任务不存在: task_id={task_id}")
                    return
            
            # 导入文件服务（延迟导入避免循环依赖）
            from src.domain.services.file_service import file_service
            
            # 执行同步操作
            results = file_service.sync_files_to_storage_worker(
                task_data.session_id,
                task_data.artifact_ids,
                task_data.context
            )
            
            # 统计结果
            success_count = sum(1 for r in results if r.status == "success")
            failed_count = len(results) - success_count
            
            # 更新任务状态为完成
            with self._lock:
                if task_id in self._tasks:
                    task_data = self._tasks[task_id]
                    task_data.status = "completed"
                    task_data.progress = 100.0
                    task_data.completed_count = len(results)
                    task_data.success_count = success_count
                    task_data.failed_count = failed_count
                    task_data.results = results
                    task_data.message = f"同步完成，成功{success_count}个，失败{failed_count}个"
                    task_data.updated_time = datetime.now().isoformat()
            
            logger.info(
                f"[SyncTaskManager] 同步任务执行完成: "
                f"task_id={task_id}, success={success_count}, failed={failed_count}"
            )
            
        except Exception as e:
            logger.error(f"[SyncTaskManager] 同步任务执行异常: task_id={task_id}, error={e}", exc_info=True)
            
            # 更新任务状态为失败
            with self._lock:
                if task_id in self._tasks:
                    task_data = self._tasks[task_id]
                    task_data.status = "failed"
                    task_data.progress = 0.0
                    task_data.message = f"任务执行失败: {str(e)}"
                    task_data.updated_time = datetime.now().isoformat()


class SyncTaskData:
    """同步任务数据"""
    
    def __init__(
        self,
        task_id: str,
        session_id: str,
        artifact_ids: List[str],
        context: AuthContext,
        status: str,
        progress: float,
        total_count: int,
        completed_count: int,
        success_count: int,
        failed_count: int,
        message: str,
        created_time: str,
        updated_time: str,
        results: List[SyncFileResult]
    ):
        self.task_id = task_id
        self.session_id = session_id
        self.artifact_ids = artifact_ids
        self.context = context
        self.status = status
        self.progress = progress
        self.total_count = total_count
        self.completed_count = completed_count
        self.success_count = success_count
        self.failed_count = failed_count
        self.message = message
        self.created_time = created_time
        self.updated_time = updated_time
        self.results = results


# 创建全局任务管理器实例
sync_task_manager = SyncTaskManager()