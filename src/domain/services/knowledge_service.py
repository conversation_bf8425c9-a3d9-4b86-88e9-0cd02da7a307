#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识库业务服务
"""

from math import log
import re
from typing import List, Optional, Tuple

from alibabacloud_wuyingaiinner20250709 import models as rag_models
from loguru import logger

from domain.models.enums import KbOperationType, KbState, KbTargetType
from infrastructure.database.models.knowledgebase_models import (
    KbDocumentModel,
    KbDocumentRelationModel,
    KbOperationLogModel,
    KbSessionModel,
    KnowledgeBaseModel,
)
from infrastructure.database.repositories.kb_document_relations_repository import (
    kb_document_relations_repository,
)
from infrastructure.database.repositories.kb_documents_repository import (
    kb_documents_repository,
)
from infrastructure.database.repositories.kb_operation_logs_repository import (
    kb_operation_logs_repository,
)
from infrastructure.database.repositories.kb_sessions_repository import (
    kb_sessions_repository,
)
from infrastructure.database.repositories.knowledgebase_repository import (
    knowledgebase_repository,
)
from popclients.cloud_storage_client import get_cloud_storage_client
from src.application.rag_api_models import *
from src.domain.services.auth_service import AuthContext, auth_service
from src.domain.services.file_service import file_service
from src.domain.utils.check_utils import CheckUtils
from src.domain.utils.next_token_utils import NextTokenUtils
from src.domain.utils.time_utils import TimeUtils
from src.infrastructure.database.models.auth_models import ResourceType, PermissionType
from src.infrastructure.database.saga_transaction import saga_transactional
from src.infrastructure.memory.memory_sdk import memory_sdk
from src.popclients.rag_client import create_rag_client


class KnowledgeService:
    """知识库业务服务"""

    def __init__(self):
        self.kb_operation_logs_repository = kb_operation_logs_repository
        self.knowledgebase_repository = knowledgebase_repository
        self.kb_documents_repository = kb_documents_repository
        self.kb_document_relations_repository = kb_document_relations_repository
        self.kb_sessions_repository = kb_sessions_repository

    # ==================== 知识库和属性关系判断相关操作 ====================

    def is_knowledge_base_session(
            self, kb_id: str, session_id_list: List[str]
    ) -> Dict[str, bool]:
        """
        判断会话是否属于知识库
        """
        CheckUtils.parameter_not_null(kb_id, "kb_id")
        CheckUtils.parameter_not_empty(session_id_list, "session_id_list")

        # 获取会话详情
        sessions = self.kb_sessions_repository.list_sessions(
            kb_id=kb_id,
            session_id_list=session_id_list,
        )
        session_ids_set = set(session.session_id for session in sessions)
        return {
            session_id: session_id in session_ids_set for session_id in session_id_list
        }

    def is_knowledge_base_message(
            self, kb_id: str, session_id: str, message_id_list: List[str]
    ) -> Dict[str, bool]:
        """
        判断消息是否属于知识库
        """
        CheckUtils.parameter_not_null(kb_id, "kb_id")
        CheckUtils.parameter_not_null(session_id, "session_id")
        CheckUtils.parameter_not_empty(message_id_list, "message_id_list")

        # 获取会话详情
        session = self.kb_sessions_repository.get_session(kb_id, session_id)
        CheckUtils.object_exists(session, "session")

        # 获取消息详情
        if session.message_id_list:
            message_ids = set(session.message_id_list.split(","))
            return {
                message_id: message_id in message_ids for message_id in message_id_list
            }
        else:
            return {message_id: False for message_id in message_id_list}

    def is_knowledge_base_file(
            self, kb_id: str, session_id: str, file_id_list: List[str]
    ) -> Dict[str, bool]:
        """
        判断文件是否属于知识库
        """
        CheckUtils.parameter_not_null(session_id, "kb_id")
        CheckUtils.parameter_not_empty(file_id_list, "file_id_list")

        # 获取会话详情
        session = self.kb_sessions_repository.get_session(kb_id, session_id)
        CheckUtils.object_exists(session, "session")

        # 获取知识库文件
        doc_relations = self.kb_document_relations_repository.list_relations(
            kb_id=kb_id,
            session_id=session_id,
            file_id_list=file_id_list,
        )
        file_ids = set(relation.file_id for relation in doc_relations)

        # 判断文件是否属于知识库
        return {file_id: file_id in file_ids for file_id in file_id_list}

    # ==================== 知识库相关操作 ====================

    def create_knowledge_base(
            self,
            name: str,
            auth_context: AuthContext,
            description: Optional[str] = None,
    ) -> str:
        """
        创建知识库（支持 Saga 事务）

        使用 Saga 模式处理分布式事务：
        1. 先调用 RAG 服务创建知识库
        2. 在本地数据库创建记录
        3. 注册资源鉴权
        4. 如果本地操作失败，自动执行补偿操作（删除 RAG 知识库）
        """
        # 查重：同名、同 owner_ali_uid、owner_wy_id（search_knowledge_bases仅支持未删除）
        owner_ali_uid = auth_context.ali_uid
        owner_wy_id = auth_context.wy_id
        existing = self.knowledgebase_repository.list_knowledge_bases(
            owner_ali_uid=owner_ali_uid,
            owner_wy_id=owner_wy_id,
            name=name,
        )
        CheckUtils.object_not_exists_if(len(existing) == 0, "knowledge_base")

        # 检查是否超过限额
        current_kb_count = self.knowledgebase_repository.count_knowledge_bases_by_owner(
            owner_ali_uid=owner_ali_uid, owner_wy_id=owner_wy_id
        )
        CheckUtils.quota_limit(current_kb_count < 5, "knowledge_base")

        # 检查name是否符合正则  /^[a-zA-Z0-9\u4e00-\u9fa5\s_-]*$/
        CheckUtils.parameter_validate(
            re.match(r"^[a-zA-Z0-9\u4e00-\u9fa5\s_-]*$", name) is not None,
            "name",
        )

        # 步骤1: 调用 RAG 服务创建知识库（事务外部）
        logger.info(
            f"调用 RAG 服务创建知识库: {name}, owner_ali_uid: {owner_ali_uid}, owner_wy_id: {owner_wy_id}")
        client = create_rag_client()
        create_rag_response_body = client.create_kb(
            name=name,
            description=description,
            ali_uid=str(owner_ali_uid),
            wy_id=owner_wy_id,
        ).body

        # 检查创建结果是否成功，以及 kb_id 是否存在
        if (
                not create_rag_response_body
                or create_rag_response_body.success is False
                or not getattr(create_rag_response_body, "data", None)
                or not getattr(create_rag_response_body.data, "kb_id", None)
        ):
            # 业务逻辑错误，不需要事务回滚
            raise ValueError(f"RAG服务创建知识库失败: {create_rag_response_body.code}")

        kb_id = create_rag_response_body.data.kb_id
        logger.info(f"RAG服务创建知识库成功: {kb_id}")

        # 步骤2: 使用事务处理本地数据库操作和资源注册
        return self._create_knowledge_base_with_transaction(
            kb_id=kb_id,
            name=name,
            owner_ali_uid=owner_ali_uid,
            owner_wy_id=owner_wy_id,
            description=description,
            auth_context=auth_context,
            client=client,
        )

    @saga_transactional(rollback_on=Exception)  # 排除 ValueError，只对系统异常回滚
    def _create_knowledge_base_with_transaction(
            self,
            kb_id: str,
            name: str,
            owner_ali_uid: int,
            owner_wy_id: str,
            description: Optional[str],
            auth_context: AuthContext,
            client,
            **kwargs,
    ) -> str:
        """
        使用事务创建知识库的本地部分
        """
        # 添加补偿操作：如果后续步骤失败，删除 RAG 知识库
        _saga_manager = kwargs.get("_saga_manager")
        if _saga_manager:

            def delete_rag_kb():
                try:
                    logger.info(f"执行补偿操作：删除 RAG 知识库 {kb_id}")
                    client.delete_kb(kb_id=kb_id)
                    logger.info(f"补偿操作成功：删除 RAG 知识库 {kb_id}")
                except Exception as e:
                    logger.exception(f"补偿操作失败：无法删除 RAG 知识库 {kb_id}: {e}")

            _saga_manager.add_compensation_action(delete_rag_kb)

        # 步骤2: 在本地数据库创建记录
        logger.info(f"在本地数据库创建记录: {kb_id}")
        self.knowledgebase_repository.create_knowledge_base(
            kb_id=kb_id,
            name=name,
            owner_ali_uid=owner_ali_uid,
            owner_wy_id=owner_wy_id,
            description=description,
        )

        # 步骤3: 注册资源鉴权
        logger.info(f"注册资源鉴权: {kb_id}")
        auth_service.register_resource(
            context=auth_context,
            resource_type=ResourceType.KNOWLEDGE_BASE,
            resource_id=str(kb_id),
            resource_name=name,
            is_public=False,
        )

        logger.info(f"知识库创建成功: {kb_id}")
        return kb_id

    """查询知识库列表"""

    def list_knowledge_bases(
            self, owner_ali_uid: int, owner_wy_id: str, max_result: int, next_token: str
    ) -> KnowledgeBaseListPaginationResponse:

        logger.info(
            f"查询知识库列表请求: owner_ali_uid: {owner_ali_uid}, owner_wy_id: {owner_wy_id}, max_result: {max_result}, next_token: {next_token}")

        # 解析 next_token
        pagination_model = NextTokenUtils.decode(next_token)
        less_than_equal_gmt_create = pagination_model.gmt
        less_than_equal_id = pagination_model.id

        # 分页搜索知识库
        dbs = self.knowledgebase_repository.list_knowledge_bases(
            owner_ali_uid=owner_ali_uid,
            owner_wy_id=owner_wy_id,
            limit=max_result + 1,
            less_than_equal_id=less_than_equal_id,
            less_than_equal_gmt_create=less_than_equal_gmt_create,
            order_pairs=[
                KnowledgeBaseModel.gmt_created.desc(),
                KnowledgeBaseModel.id.desc(),
            ],
        )

        # 统计知识库数量
        count = self.knowledgebase_repository.count_knowledge_bases_by_owner(
            owner_ali_uid=owner_ali_uid, owner_wy_id=owner_wy_id
        )

        # 生成 next_token
        next_token = None
        if len(dbs) == max_result + 1:
            last_item = dbs.pop(len(dbs) - 1)
            next_token = NextTokenUtils.encode(
                id=last_item.id, date=last_item.gmt_created
            )

        # 分别统计每个统计知识库的文档数量和会话数量
        data = []
        if len(dbs) > 0:
            kb_ids = [obj.kb_id for obj in dbs]
            data = [KnowledgeBaseListResponse.from_orm_model(obj) for obj in dbs]

            # 批量查询文档数量和会话数量
            document_counts = (
                self.kb_documents_repository.batch_count_documents_by_kb_ids(kb_ids)
            )
            session_counts = self.kb_sessions_repository.batch_count_sessions_by_kb_ids(
                kb_ids
            )

            # 设置统计结果
            for kb in data:
                kb.document_count = document_counts.get(kb.kb_id, 0)
                kb.session_count = session_counts.get(kb.kb_id, 0)

        return KnowledgeBaseListPaginationResponse(
            data=data,
            max_results=count,
            next_token=next_token,
        )

    """查询知识库详情"""

    def get_knowledge_base(
            self,
            kb_id: str,
            auth_context: AuthContext,
    ) -> KnowledgeBaseDetailResponse:

        logger.info(f"查询知识库详情请求: kb_id: {kb_id}, auth_context: {auth_context}")

        # 获取知识库详情
        knowledge_base = self._get_knowledge_base_by_id(kb_id, auth_context)
        CheckUtils.object_exists_if(knowledge_base is not None, "knowledge_base")

        # 查询知识库的文档数量和会话数量
        document_count = self.kb_documents_repository.count_documents_by_kb(kb_id)
        session_count = self.kb_sessions_repository.count_sessions(kb_id=kb_id)
        knowledge_base.document_count = document_count
        knowledge_base.session_count = session_count

        return knowledge_base

    """修改知识库信息"""

    def update_knowledge_base(
            self,
            kb_id: str,
            auth_context: AuthContext,
            name: Optional[str] = None,
            description: Optional[str] = None,
    ):
        """
        修改知识库信息（支持 Saga 事务）
        """

        logger.info(
            f"修改知识库信息请求: kb_id: {kb_id}, auth_context: {auth_context}, name: {name}, description: {description}")

        # 获取知识库详情
        knowledge_base = self._get_knowledge_base_by_id(kb_id, auth_context)

        # 检查name是否符合正则  /^[a-zA-Z0-9\u4e00-\u9fa5\s_-]*$/
        CheckUtils.parameter_validate(
            re.match(r"^[a-zA-Z0-9\u4e00-\u9fa5\s_-]*$", name) is not None,
            "name",
        )

        # 如果更新名称，检查名称是否重复
        if name is not None and name != knowledge_base.name:
            existing = self.knowledgebase_repository.list_knowledge_bases(
                owner_ali_uid=auth_context.ali_uid,
                owner_wy_id=auth_context.wy_id,
                name=name,
            )
            CheckUtils.object_duplicate_if(
                len(existing) == 0 or existing[0].kb_id == kb_id, "knowledge_base"
            )

        # 保存原始值用于补偿操作
        original_name = knowledge_base.name
        original_description = knowledge_base.description

        # 调用 RAG 服务更新知识库
        logger.info(f"调用 RAG 服务更新知识库: {kb_id}")
        client = create_rag_client()
        update_rag_response = client.update_kb(
            kb_id=kb_id,
            name=name,
            description=description,
        )
        if not update_rag_response or not update_rag_response.body:
            raise ValueError("更新知识库失败")

        # 通过事务更新知识库
        self._update_knowledge_base_with_transaction(
            kb_id=kb_id,
            name=name,
            description=description,
            original_name=original_name,
            original_description=original_description,
            auth_context=auth_context,
            client=client,
        )

    @saga_transactional(rollback_on=Exception)  # 排除 ValueError，只对系统异常回滚
    def _update_knowledge_base_with_transaction(
            self,
            kb_id: str,
            name: str,
            description: Optional[str],
            original_name: str,
            original_description: Optional[str],
            client,
            **kwargs,
    ) -> str:
        """
        使用事务创建知识库的本地部分
        """
        # 添加补偿操作：如果后续步骤失败，删除 RAG 知识库
        _saga_manager = kwargs.get("_saga_manager")
        if _saga_manager:

            def restore_rag_kb():
                try:
                    logger.info(f"执行补偿操作：恢复 RAG 知识库 {kb_id} 的原始值")
                    client.update_kb(
                        kb_id=kb_id,
                        name=original_name,
                        description=original_description,
                    )
                    logger.info(f"补偿操作成功：恢复 RAG 知识库 {kb_id} 的原始值")
                except Exception as e:
                    logger.exception(f"补偿操作失败：无法恢复 RAG 知识库 {kb_id}: {e}")

            _saga_manager.add_compensation_action(restore_rag_kb)

        # 步骤2: 更新本地数据库
        logger.info(f"更新本地数据库记录: {kb_id}")
        self.knowledgebase_repository.update_knowledge_base(
            kb_id=kb_id,
            name=name,
            description=description,
            is_update_selective=False,
        )

        logger.info(f"知识库更新成功: {kb_id}")
        return kb_id

    """删除知识库"""

    def delete_knowledge_base(
            self,
            kb_id: str,
            auth_context: AuthContext,
    ):

        logger.info(f"删除知识库请求: kb_id: {kb_id}, auth_context: {auth_context}")

        # 检查参数
        CheckUtils.parameter_not_null(kb_id, "kb_id")

        # 获取知识库详情
        knowledge_base = self._get_knowledge_base_by_id(
            kb_id=kb_id, auth_context=auth_context, is_check_exists=False
        )
        if knowledge_base is None:
            pass

        # 调用 RAG 服务删除知识库
        logger.info(f"调用 RAG 服务删除知识库: {kb_id}")
        client = create_rag_client()
        delete_rag_response = client.delete_kb(kb_id=kb_id)
        if not delete_rag_response or not delete_rag_response.body:
            raise ValueError("删除知识库失败")

        # 软删除本地数据库记录
        logger.info(f"软删除本地数据库记录: {kb_id}")
        self.knowledgebase_repository.soft_delete_knowledge_base(kb_id)
        self.kb_sessions_repository.soft_delete_sessions(kb_id=kb_id)
        self.kb_document_relations_repository.soft_delete_relations(kb_id=kb_id)

        # 删除资源鉴权
        logger.info(f"注册资源鉴权: {kb_id}")
        auth_service.unregister_resource(
            context=auth_context,
            resource_type=ResourceType.KNOWLEDGE_BASE,
            resource_id=kb_id,
        )

        logger.info(f"知识库删除成功: {kb_id}")

    """内部方法，获取知识库详情"""

    def _get_knowledge_base_by_id(
            self, kb_id: str, auth_context: AuthContext, is_check_exists: bool = True
    ) -> Optional[KnowledgeBaseDetailResponse]:
        """
        获取知识库详情
        """
        logger.info(
            f"获取知识库详情请求: kb_id: {kb_id}, auth_context: {auth_context}, is_check_exists: {is_check_exists}")

        # 检查参数
        CheckUtils.parameter_not_null(kb_id, "kb_id")

        # 获取知识库详情
        knowledge_base = self.knowledgebase_repository.get_knowledge_base_by_id(kb_id)

        if is_check_exists:
            CheckUtils.object_exists_if(knowledge_base is not None, "knowledge_base")
        elif knowledge_base is None:
            return None
        response = KnowledgeBaseDetailResponse.from_orm_model(knowledge_base)

        # 权限检查
        auth_service.check_resource_permission(
            context=auth_context,
            resource_type=ResourceType.KNOWLEDGE_BASE,
            resource_id=str(kb_id),
            required_permission=PermissionType.get_owner_permissions(),
        )
        return response

    # ==================== 知识库文档操作 ====================

    def create_documents(
            self,
            auth_context: AuthContext,
            kb_id: str,
            document_list: List[KnowledgeBaseDocumentItem],
    ):
        """
        创建文档（支持同名替换）

        Args:
            auth_context: 用户权限
            kb_id: 知识库ID
            document_list: 文档列表，每个文档包含 oss_path 和 file_name
        """
        logger.info(
            f"创建文档请求: auth_context: {auth_context}, kb_id: {kb_id}, document_list: {document_list}")

        # 检查参数
        CheckUtils.parameter_not_null(kb_id, "kb_id")
        CheckUtils.parameter_not_empty(document_list, "document_list")
        CheckUtils.parameter_not_null(auth_context.ali_uid, "ali_uid")
        CheckUtils.parameter_not_null(auth_context.wy_id, "wy_id")
        for document in document_list:
            CheckUtils.parameter_not_empty(document.file_id, "file_id")
            CheckUtils.parameter_not_empty(document.file_name, "file_name")

        # 检查参数，不能传入重复的file_name
        file_name_set = set(document.file_name for document in document_list)
        CheckUtils.parameter_validate(
            len(file_name_set) == len(document_list),
            "file_name",
        )

        # 获取每个fileid对应的filepath
        cloud_storage_client = get_cloud_storage_client()
        for document in document_list:
            response = cloud_storage_client.get_download_url(
                file_id=document.file_id,
                product_type="CloudDesktop",
                user_ali_uid=auth_context.ali_uid,
                wy_drive_owner_id=auth_context.wy_id,
            )
            document.oss_path = response.body.url
            document.file_size = response.body.size

        # 检查文件是否支持解析
        rag_client = create_rag_client()
        response = rag_client.check_file_parse_able(
            ali_uid=str(auth_context.ali_uid),
            file_infos=[
                rag_models.CheckFileParseAbleRequestFileInfos(
                    file_name=document.file_name,
                    size=document.file_size,
                )
                for document in document_list
            ],
            wy_id=auth_context.wy_id,
        )
        if not response or not response.body:
            raise ValueError("检查文件是否支持解析失败")
        type_parse_not_able_file_names = [
            parse_file_check.file_name
            for parse_file_check in response.body.data
            if not parse_file_check.parse_able and not parse_file_check.type_ok
        ]
        CheckUtils.operation_not_supported(
            condition=len(type_parse_not_able_file_names) == 0,
            parameter_name=str(",".join(type_parse_not_able_file_names)),
            operation_type="文件格式",
        )
        size_parse_not_able_file_names = [
            parse_file_check.file_name
            for parse_file_check in response.body.data
            if not parse_file_check.parse_able and not parse_file_check.size_ok
        ]
        CheckUtils.operation_not_supported(
            condition=len(size_parse_not_able_file_names) == 0,
            parameter_name=str(",".join(size_parse_not_able_file_names)),
            operation_type="文件大小",
        )

        # 获取知识库详情
        self._get_knowledge_base_by_id(kb_id, auth_context)

        # 更新知识库时间
        self.knowledgebase_repository.update_knowledge_base(
            kb_id=kb_id,
            is_update_selective=True,
        )

        # 根据文档列表（file_id + file_name的列表）查询当前系统是否已有文档，处理增量
        current_documents = (
            self.kb_documents_repository.list_kb_documents_with_complex_conditions(
                file_list=document_list,
            )
        )
        logger.info(f"当前系统已有的文档: {current_documents}")

        # 获取未解析的文档，调用 RAG 服务创建文档，插入kb_documents表
        logger.info(
            f"获取未解析的文档，调用 RAG 服务创建文档，插入kb_documents表: {document_list}"
        )
        new_documents = []
        current_document_doc_ids = []
        for new_document in document_list:
            match_flag = False  # True表示该文件ID和文件名有对应的kb_documents记录
            for current_document in current_documents:
                if (
                        current_document.file_id == new_document.file_id
                        and current_document.file_title == new_document.file_name
                ):
                    # 如果已有相同file_id和file_name的文档，则添加到current_document_doc_ids直接进行批量绑定
                    current_document_doc_ids.append(current_document.doc_id)
                    match_flag = True
                    break
            if not match_flag:
                # 如果没有相同file_id和file_name的文档，则调用RAG服务创建文档
                new_document_obj = self._create_rag_document(
                    auth_context,
                    kb_id,
                    new_document.file_name,
                    new_document.oss_path,
                    new_document.file_id,
                )
                new_documents.append(new_document_obj)
                current_documents.append(new_document_obj)
        logger.info(f"插入kb_documents表: {new_documents}")
        if len(new_documents) > 0:
            self.kb_documents_repository.batch_insert_documents(new_documents)

        # 调用RAG服务进行批量绑定操作
        logger.info(f"调用RAG服务进行批量绑定操作: {current_document_doc_ids}")
        if len(current_document_doc_ids) > 0:
            client = create_rag_client()
            client.modify_documents_kb(
                kb_id=kb_id,
                doc_ids=current_document_doc_ids,
                operation="bind",
            )

        # 解决同名冲突问题查询同名文档是否添加到知识库，如果有，判断是否需要根据文档名称更新文档ID，如果没有，则添加
        current_relations = self.kb_document_relations_repository.list_relations(
            kb_id=kb_id,
            file_title_list=[document.file_name for document in document_list],
        )
        current_relations_name_map = {
            relation.file_title: relation for relation in current_relations
        }
        logger.info(f"解决同名冲突问题查询同名文档是否添加到知识库: {current_relations_name_map}")
        batch_added_relations_file_map = dict()  # 需要新增的关系绑定
        unbind_files = []  # 需要rag解绑的文件列表
        for document in document_list:
            if document.file_name in current_relations_name_map:
                # 该知识库有同名的文件绑定关系
                current_relation = current_relations_name_map[document.file_name]
                if document.file_id != current_relation.file_id:
                    # 同名文件下的文件ID发生变更
                    logger.info(
                        f"更新知识库 {kb_id} 文档关联关系: {document.file_name} 已存在，{current_relation.file_id} 更新文件ID: {document.file_id}"
                    )
                    unbind_files.append(
                        KnowledgeBaseDocumentItem(
                            file_id=current_relation.file_id,
                            file_name=current_relation.file_title,
                        )
                    )
                    self.kb_document_relations_repository.update_relation_fileid_by_filetitle(
                        kb_id=kb_id,
                        file_title=document.file_name,
                        file_id=document.file_id,
                    )
            else:
                # 该知识库没有同名文件的绑定关系
                batch_added_relations_file_map[document.file_id] = document.file_name

        # 批量插入知识库和文档关联关系
        logger.info(f"批量插入知识库和文档关联关系: {batch_added_relations_file_map}")
        if len(batch_added_relations_file_map) > 0:
            self.kb_document_relations_repository.batch_create_relations(
                kb_id=kb_id,
                file_map=batch_added_relations_file_map,
            )

        # 批量解绑rag服务中的文档
        logger.info(f"批量解绑rag服务中的文档: {unbind_files}")
        if len(unbind_files) > 0:
            client = create_rag_client()
            unbind_documents = (
                self.kb_documents_repository.list_kb_documents_with_complex_conditions(
                    file_list=unbind_files,
                )
            )
            client.modify_documents_kb_async(
                kb_id=kb_id,
                doc_ids=[
                    unbind_document.doc_id for unbind_document in unbind_documents
                ],
                operation="unbind",
            )

        # 批量记录操作日志
        logs_data = [
            {
                "kb_id": kb_id,
                "ali_uid": auth_context.ali_uid,
                "wy_id": auth_context.wy_id,
                "operation_type": KbOperationType.CREATE.value,
                "target_type": KbTargetType.DOCUMENT.value,
                "target_id": document.file_id,
                "status": KbState.PROCESSING.value,
            }
            for document in document_list
        ]
        logger.info(f"批量记录操作日志: {logs_data}")
        if len(logs_data) > 0:
            self.kb_operation_logs_repository.batch_insert_logs(logs_data)

    @staticmethod
    def _create_rag_document(
            auth_context: AuthContext,
            kb_id: str,
            file_name: str,
            oss_path: str,
            file_id: str = None,
    ) -> KbDocumentModel:
        """
        调用RAG服务创建文档
        """
        try:
            logger.info(
                f"创建文档请求: auth_context: {auth_context}, kb_id: {kb_id}, file_name: {file_name}, oss_path: {oss_path}, file_id: {file_id}")
            # 调用 RAG 服务提交文档
            client = create_rag_client()
            logger.info(
                f"调用 RAG 服务提交文档: url={oss_path}, filename={file_name}, kb_ids={kb_id}, ali_uid={auth_context.ali_uid}, wy_id={auth_context.wy_id}"
            )
            response = client.submit_document_by_url(
                url=oss_path,
                filename=file_name,
                kb_ids=[kb_id],
                ali_uid=str(auth_context.ali_uid),
                wy_id=auth_context.wy_id,
            )
            logger.info(
                f"RAG 服务提交文档响应: url={oss_path}, filename={file_name}, kb_ids={kb_id}, ali_uid={auth_context.ali_uid}, wy_id={auth_context.wy_id}, response={response}"
            )
            if (
                    response
                    and response.body
                    and response.body.data
                    and response.body.data.doc_id
            ):
                document = KbDocumentModel(
                    doc_id=response.body.data.doc_id,
                    # oss_bucket=response.body.data.infos.bucket,
                    # oss_object_name=response.body.data.file_paths.raw,
                    file_title=file_name,
                    # file_size=response.body.data.infos.filesize,
                    status="processing",
                    file_id=file_id,  # 使用传入的file_id默认值
                )
                logger.info(
                    f"创建文档成功: url={oss_path}, filename={file_name}, kb_ids={kb_id}, ali_uid={auth_context.ali_uid}, wy_id={auth_context.wy_id}, document={document}"
                )
                return document
            else:
                raise ValueError("RAG服务返回数据异常")
        except Exception as e:
            logger.exception(
                f"创建文档失败 url={oss_path}, filename={file_name}, kb_ids={kb_id}, ali_uid={auth_context.ali_uid}, wy_id={auth_context.wy_id}, error={e}"
            )
            raise

    def list_documents(
            self,
            auth_context: AuthContext,
            kb_id: str,
            max_results: int,
            next_token: str,
            file_name_list: Optional[List[str]],
    ) -> KnowledgeBaseDocumentListPaginationResponse:
        """
        查询文档列表
        """
        logger.info(
            f"查询文档列表请求: auth_context: {auth_context}, kb_id: {kb_id}, max_results: {max_results}, next_token: {next_token}, file_name_list: {file_name_list}")

        # 检查参数
        CheckUtils.parameter_not_null(kb_id, "kb_id")

        # 获取知识库详情
        self._get_knowledge_base_by_id(kb_id, auth_context)

        # 解析 next_token
        pagination_model = NextTokenUtils.decode(next_token)
        less_than_equal_gmt_create = pagination_model.gmt
        less_than_equal_id = pagination_model.id

        # 分页搜索知识库文档关联
        relations = self.kb_document_relations_repository.list_relations(
            kb_id=kb_id,
            file_title_list=(
                file_name_list if file_name_list and len(file_name_list) > 0 else None
            ),
            session_id_is_null=True,
            limit=max_results + 1,
            less_than_equal_id=less_than_equal_id,
            less_than_equal_gmt_create=less_than_equal_gmt_create,
            order_pairs=[
                KbDocumentRelationModel.gmt_created.desc(),
                KbDocumentRelationModel.id.desc(),
            ],
        )

        # 统计知识库文档关联数量
        count = self.kb_document_relations_repository.count_relations(
            kb_id=kb_id,
            file_title_list=(
                file_name_list if file_name_list and len(file_name_list) > 0 else None
            ),
            session_id_is_null=True,
        )

        # 生成 next_token
        next_token = None
        if len(relations) == max_results + 1:
            last_item = relations.pop(len(relations) - 1)
            next_token = NextTokenUtils.encode(
                id=last_item.id, date=last_item.gmt_created
            )

        # 搜索文档信息
        data = []
        if len(relations) > 0:
            documents = self.kb_documents_repository.list_kb_documents(
                file_id_list=[obj.file_id for obj in relations],
            )
            document_map = {obj.file_id: obj for obj in documents}
            data = [
                KnowledgeBaseDocumentListResponse.from_orm_model(
                    relation,
                    (
                        document_map[relation.file_id]
                        if relation.file_id in document_map
                        else None
                    ),
                )
                for relation in relations
            ]

        # 返回分页结果
        return KnowledgeBaseDocumentListPaginationResponse(
            data=data,
            max_results=count,
            next_token=next_token,
        )

    def describe_document(
            self,
            auth_context: AuthContext,
            kb_id: str,
            file_id: str,
            file_name: Optional[str] = None,
    ) -> KnowledgeBaseDocumentDescribeResponse:
        """
        描述文档
        """
        logger.info(
            f"描述文档请求: auth_context: {auth_context}, kb_id: {kb_id}, file_id: {file_id}, file_name: {file_name}")

        # 检查参数
        CheckUtils.parameter_not_null(kb_id, "kb_id")
        CheckUtils.parameter_not_null(file_id, "document_id")

        # 获取知识库详情
        self._get_knowledge_base_by_id(kb_id, auth_context, is_check_exists=False)

        # 获取文档关联
        relation = self.kb_document_relations_repository.get_relation(
            kb_id=kb_id,
            file_id=file_id,
            file_title=file_name,
            session_id_is_null=True,
        )
        CheckUtils.object_exists_if(relation is not None, "relation")

        # 获取文档详情
        document = self.kb_documents_repository.get_kb_document_by_id(file_id=file_id)

        # 返回文档详情
        return KnowledgeBaseDocumentDescribeResponse(
            document_id=relation.file_id,
            file_name=relation.file_title,
            oss_path=document.oss_object_name,
            status=document.status,
            gmt_created=TimeUtils.to_iso8601_utc(relation.gmt_created),
            gmt_modified=TimeUtils.to_iso8601_utc(relation.gmt_modified),
        )

    def delete_documents(
            self,
            auth_context: AuthContext,
            kb_id: str,
            file_name_list: List[str],
    ):
        """
        删除文档
        """
        logger.info(
            f"删除文档请求: auth_context: {auth_context}, kb_id: {kb_id}, file_name_list: {file_name_list}")

        # 检查参数
        CheckUtils.parameter_not_null(kb_id, "kb_id")
        CheckUtils.parameter_not_empty(file_name_list, "file_name_list")

        # 更新知识库时间
        self.knowledgebase_repository.update_knowledge_base(
            kb_id=kb_id,
            is_update_selective=True,
        )

        # 获取文档关联
        relations = self.kb_document_relations_repository.list_relations(
            kb_id=kb_id,
            file_title_list=file_name_list,
        )

        if len(relations) > 0:
            # 删除文档关联
            self.kb_document_relations_repository.soft_delete_relations(
                kb_id=kb_id,
                file_title_list=file_name_list,
            )

            # 查询文档ID是否还有关联
            file_id_other_relations = (
                self.kb_document_relations_repository.list_relations(
                    kb_id=kb_id,
                    file_id_list=[relation.file_id for relation in relations],
                )
            )

            # 获取还有关联的文件ID集合
            file_id_other_set = set(
                [relation.file_id for relation in file_id_other_relations]
            )

            # 查询没有关联的文档的docID列表，删除RAG服务中的文档关联
            file_id_list = [
                relation.file_id
                for relation in relations
                if relation.file_id not in file_id_other_set
            ]
            if len(file_id_list) > 0:
                documents = self.kb_documents_repository.list_kb_documents(
                    file_id_list=file_id_list,
                )
                if len(documents) > 0:
                    client = create_rag_client()
                    client.modify_documents_kb_async(
                        kb_id=kb_id,
                        doc_ids=[doc.doc_id for doc in documents],
                        operation="unbind",
                    )

    # ==================== 知识库会话操作 ====================

    def create_sessions(
            self,
            auth_context: AuthContext,
            kb_id: str,
            session_id: str,
            message_list: Optional[List[str]] = None,
            file_list: Optional[List[str]] = None,
    ):
        """
        创建会话

        """
        logger.info(
            f"创建会话请求: auth_context: {auth_context}, kb_id: {kb_id}, session_id: {session_id}, message_list: {message_list}, file_list: {file_list}")

        # 检查参数
        CheckUtils.parameter_not_empty(kb_id, "kb_id")
        CheckUtils.parameter_not_empty(session_id, "session_id")

        # 获取知识库详情
        self._get_knowledge_base_by_id(kb_id, auth_context, is_check_exists=True)

        # 更新知识库时间
        self.knowledgebase_repository.update_knowledge_base(
            kb_id=kb_id,
            is_update_selective=True,
        )

        # 判断会话是否添加过知识库
        session = self.kb_sessions_repository.get_session(
            kb_id=kb_id,
            session_id=session_id,
        )
        CheckUtils.object_not_exists(session, "session")

        # 处理消息列表
        logger.info(f"处理 知识库{kb_id} 会话{session_id} 消息列表: {message_list}")
        self._process_session_message_list(
            auth_context, kb_id, session_id, message_list
        )

        # 处理文件列表
        logger.info(f"处理 知识库{kb_id} 会话{session_id} 文件列表: {file_list}")
        self._process_add_session_file_list(auth_context, kb_id, session_id, file_list)

    def _process_session_message_list(
            self,
            auth_context: AuthContext,
            kb_id: str,
            session_id: str,
            message_list: Optional[List[str]],
    ):
        logger.info(f"开始处理 知识库{kb_id} 会话{session_id} 消息列表: {message_list}")
        snippet_id = self._process_rag_snippet(
            auth_context=auth_context, kb_id=kb_id, message_list=message_list
        )

        # 持久化保存会话和日志数据
        logger.info(f"持久化保存 知识库{kb_id} 会话{session_id} 数据: {snippet_id}")
        self.kb_sessions_repository.create_session(
            kb_id=kb_id,
            session_id=session_id,
            message_id_list=",".join(message_list) if message_list else None,
            snippet_id=snippet_id,
            snippet_status=(
                KbState.PROCESSING.value
                if snippet_id is not None
                else KbState.SUCCESS.value
            ),
        )
        self.kb_operation_logs_repository.insert_log(
            kb_id=kb_id,
            ali_uid=auth_context.ali_uid,
            wy_id=auth_context.wy_id,
            operation_type=KbOperationType.CREATE.value,
            target_type=KbTargetType.SESSION.value,
            target_id=session_id,
        )

    def _process_rag_snippet(
            self,
            auth_context: AuthContext,
            kb_id: str,
            message_list: Optional[List[str]] = None,
    ):
        """
        处理RAG片段
        """
        logger.info(f"开始处理 知识库{kb_id} 消息列表: {message_list}")
        rag_messages = []
        snippet_id = None
        if message_list:
            messages = memory_sdk.get_message_by_id_list(message_list)
            for message in messages:
                rag_message = rag_models.SubmitSnippetRagRequestMessages(
                    message_id=message.message_id,
                    content=message.content,
                    role=(
                        message.role.value
                        if hasattr(message.role, "value")
                        else str(message.role)
                    ),
                )
                rag_messages.append(rag_message)

            # 调用 RAG 服务推送会话
            if rag_messages:
                client = create_rag_client()
                response = client.submit_snippet(
                    kb_ids=[kb_id],
                    messages=rag_messages,
                    ali_uid=str(auth_context.ali_uid),
                    wy_id=auth_context.wy_id,
                )
                snippet_id = response.body.data.snippet_id
        return snippet_id

    def _process_delete_rag_snippet(
            self,
            snippet_id: str,
    ):
        """
        删除RAG片段
        """
        logger.info(f"开始删除RAG片段: {snippet_id}")
        client = create_rag_client()
        client.delete_snippet(snippet_id=snippet_id)

    def _process_add_session_file_list(
            self,
            auth_context: AuthContext,
            kb_id: str,
            session_id: str,
            file_id_list: Optional[List[str]],
    ):
        """
        处理会话添加新的文件列表
        """
        logger.info(f"开始处理 知识库{kb_id} 会话{session_id} 文件列表: {file_id_list}")

        # 检查参数
        CheckUtils.parameter_not_empty(kb_id, "kb_id")
        CheckUtils.parameter_not_empty(session_id, "session_id")
        if file_id_list is None or len(file_id_list) == 0:  # 会话没有新增的文件
            return
        file_id_list = list(set(file_id_list))  # 针对file_id_list进行去重

        # 获取已解析的文档
        current_documents = self.kb_documents_repository.list_kb_documents(
            session_id=session_id,
            file_id_list=file_id_list,
        )
        current_documents_set = set(document.file_id for document in current_documents)
        current_file_map = {
            document.file_id: document.file_title
            for document in current_documents  # 制品的文件名不会修改，可以从kb_documents表获取
        }
        # 获取新增解析的文档
        new_file_list = [
            file_id for file_id in file_id_list if file_id not in current_documents_set
        ]

        # 已解析文档，直接和知识库建立关联
        current_doc_ids = [document.doc_id for document in current_documents]
        client = create_rag_client()
        client.modify_documents_kb(
            kb_id=kb_id,
            doc_ids=current_doc_ids,
            operation="bind",
        )

        # 未解析的文档，从文件服务获取每个文件的下载地址后调用RAG服务
        new_document_list = []
        document_download_urls = dict()
        response = file_service.get_download_urls_by_artifact_ids(
            session_id=session_id, artifact_ids=new_file_list
        )
        if response and response.download_links:
            for download_link in response.download_links:
                document = self._create_rag_document(
                    auth_context,
                    kb_id,
                    download_link.file_name,
                    download_link.download_url,
                    download_link.artifact_id,
                )
                document_download_urls[download_link.artifact_id] = (
                    download_link.download_url
                )
                current_file_map[download_link.artifact_id] = download_link.file_name
                new_document_list.append(document)

        # 未解析的文档，持久化保存新文档数据
        for document in new_document_list:
            self.kb_documents_repository.create_kb_document(
                doc_id=document.doc_id,
                oss_bucket=document.oss_bucket,
                oss_object_name=document.oss_object_name,
                file_title=document.file_title,
                file_size=document.file_size,
                status=document.status,
                file_id=document.file_id,
                session_id=session_id,
            )

        # 持久化保存关联，传入的所有记录都会插入关联
        logger.info(f"持久化保存关联: {kb_id} {session_id} {file_id_list}")
        self.kb_document_relations_repository.batch_create_relations(
            kb_id=kb_id,
            file_map=current_file_map,
            session_id=session_id,
        )

        # 使用线程池异步上传文件到网盘存储
        if new_document_list and document_download_urls and len(new_document_list) > 0:
            logger.info(
                f"异步上传文件到网盘存储: {new_document_list} {document_download_urls}"
            )

            # 使用线程池进行异步上传
            import threading

            def run_upload():
                try:
                    self._upload_file_to_cloud_storage_sync(
                        auth_context=auth_context,
                        new_documents=new_document_list,
                        document_download_urls=document_download_urls,
                    )
                except Exception as e:
                    logger.error(f"文件上传任务异常: {e}")

            # 在新线程中运行上传任务
            thread = threading.Thread(target=run_upload)
            thread.daemon = True
            thread.start()

    def _upload_file_to_cloud_storage_sync(
            self,
            auth_context: AuthContext,
            new_documents: List[KbDocumentModel],
            document_download_urls: Dict[str, str],
    ) -> bool:
        """同步上传所有文件到网盘"""
        logger.info(f"开始上传文件到网盘存储: {new_documents} {document_download_urls}")

        # 串行执行所有文件上传任务
        results = True
        for document in new_documents:
            file_id = document.file_id
            try:
                result = self.upload_single_file_sync(
                    file_id=document.file_id,
                    file_name=document.file_title,
                    auth_context=auth_context,
                    download_url=document_download_urls[document.file_id],
                )
                if result is None:
                    logger.error(
                        f"[KnowledgeService] 文件上传任务返回None: file_id={file_id}"
                    )
                    results = False
                else:
                    logger.info(
                        f"[KnowledgeService] 文件上传任务成功: file_id={file_id}"
                    )
            except Exception as e:
                logger.exception(
                    f"[KnowledgeService] 文件上传任务异常: file_id={file_id}, error_type={type(e).__name__}, error={str(e)}"
                )
                results = False
        return results

    def upload_single_file_sync(
            self, 
            file_id: str, 
            file_name: str, 
            auth_context: AuthContext, 
            download_url: str
    ):
        """同步上传单个文件到网盘（用于gevent）"""

        try:
            logger.info(
                f"[KnowledgeService] 开始处理文件: file_id={file_id} file_name={file_name} download_url={download_url}"
            )

            # 1. 调用pre_upload_file获取上传链接
            # 生成时间戳参数
            timestamp = TimeUtils.get_current_timestamp_str()
            file_path = f"/ai_artifacts_hub/{timestamp}"

            # 获取上传URL和文件信息
            cloud_storage_client = get_cloud_storage_client()
            pre_upload_response = cloud_storage_client.pre_upload_file(
                file_name=file_name,
                parent_folder_path=file_path,
                product_type="CloudDesktop",
                user_ali_uid=auth_context.ali_uid,
                wy_drive_owner_id=auth_context.wy_id,
                tags=["AI_PRODUCT"],
            )
            if (
                    not pre_upload_response
                    or not pre_upload_response.body
                    or not pre_upload_response.body.multi_parts_upload_info_list
                    or len(pre_upload_response.body.multi_parts_upload_info_list) == 0
            ):
                logger.error(
                    f"[KnowledgeService] 预上传失败，无响应: file_id={file_id}"
                )
                return None
            
            upload_url = pre_upload_response.body.multi_parts_upload_info_list[
                0
            ].upload_url
            logger.info(
                f"[KnowledgeService] 获取到上传链接: file_id={file_id}, upload_url={upload_url}"
            )
            if not upload_url:
                logger.error(
                    f"[KnowledgeService] 预上传失败，没有upload_url: file_id={file_id}"
                )
                return None

            real_file_id = pre_upload_response.body.file_id
            if not real_file_id:
                logger.error(
                    f"[KnowledgeService] 预上传失败，没有real_file_id: file_id={file_id}"
                )
                return None

            # etag = pre_upload_response.body.etag
            upload_id = pre_upload_response.body.upload_id
            if not upload_id:
                logger.error(
                    f"[KnowledgeService] 预上传失败，没有upload_id: file_id={file_id}"
                )
                return None

            # 下载文件
            file_content = self._download_file_from_url_sync(download_url)
            if not file_content:
                logger.error(f"[KnowledgeService] 无法下载文件内容: file_id={file_id}")
                return None

            # 上传文件到云存储
            upload_success = self._upload_single_file_to_cloud_storage_sync(
                upload_url=upload_url, 
                file_content=file_content, 
                file_name=file_name
            )
            if not upload_success:
                logger.error(f"[KnowledgeService] 文件上传失败: file_id={file_id}")
                return False

            # 4. 调用complete_upload_file通知上传完成
            complete_response = cloud_storage_client.complete_upload_file(
                file_id=real_file_id,
                upload_id=upload_id,
                product_type="CloudDesktop",
                user_ali_uid=auth_context.ali_uid,
                wy_drive_owner_id=auth_context.wy_id,
            )
            if not complete_response or not complete_response.body:
                logger.error(f"[KnowledgeService] 完成上传失败: file_id={file_id}")
                return False
            logger.info(f"[KnowledgeService] 文件处理完成: file_id={file_id}")
            return True

        except Exception as e:
            logger.error(
                f"[KnowledgeService] 文件处理异常: file_id={file_id}, error_type={type(e).__name__}, error={str(e)}",
                exc_info=True,
            )
            return False

    def _download_file_from_url_sync(self, download_url: str) -> Optional[bytes]:
        """
        从URL同步下载文件内容
        """
        try:
            import httpx

            with httpx.Client(timeout=30.0) as client:
                response = client.get(download_url)
                if response.status_code == 200:
                    content = response.content
                    logger.info(
                        f"[KnowledgeService] 文件下载成功: size={len(content)} bytes"
                    )
                    return content
                else:
                    logger.error(
                        f"[KnowledgeService] 文件下载失败: status={response.status_code}"
                    )
                    return None
        except Exception as e:
            logger.error(f"[KnowledgeService] 文件下载异常: error={e}")
            return None

    def _upload_single_file_to_cloud_storage_sync(
            self, 
            upload_url: str, 
            file_content: bytes, 
            file_name: str
    ) -> bool:
        """
        同步上传文件到云存储
        """
        try:
            import httpx
            logger.info(f"开始上传文件到云存储: upload_url: {upload_url}, file_name: {file_name}")
            headers = {
                "Content-Length": str(len(file_content)),
            }

            with httpx.Client(timeout=30.0) as client:
                response = client.put(upload_url, content=file_content, headers=headers)
                if response.status_code == 200:
                    logger.info(f"[KnowledgeService] 文件上传成功: {file_name}")
                    return True
                else:
                    logger.error(
                        f"[KnowledgeService] 文件上传失败: {file_name}, status={response.status_code}"
                    )
                    return False
        except Exception as e:
            logger.exception(f"[KnowledgeService] 文件上传异常: {file_name}, error={e}")
            return False

    def list_sessions(
            self,
            auth_context: AuthContext,
            kb_id: str,
            max_results: int,
            next_token: str,
    ) -> KnowledgeBaseSessionPaginationResponse:
        """
        查询会话列表
        """
        logger.info(
            f"查询会话列表请求: auth_context: {auth_context}, kb_id: {kb_id}, max_results: {max_results}, next_token: {next_token}")

        # 检查参数
        CheckUtils.parameter_not_empty(kb_id, "kb_id")
        CheckUtils.parameter_not_null(max_results, "max_results")

        # 获取知识库详情
        self._get_knowledge_base_by_id(kb_id, auth_context)

        # 解析 next_token
        pagination_model = NextTokenUtils.decode(next_token)
        less_than_equal_gmt_create = pagination_model.gmt
        less_than_equal_id = pagination_model.id

        # 分页搜索知识库文档关联
        sessions = self.kb_sessions_repository.list_sessions(
            kb_id=kb_id,
            limit=max_results + 1,
            less_than_equal_id=less_than_equal_id,
            less_than_equal_gmt_create=less_than_equal_gmt_create,
            order_pairs=[
                KbSessionModel.gmt_created.desc(),
                KbSessionModel.id.desc(),
            ],
        )

        # 统计知识库会话数量
        count = self.kb_sessions_repository.count_sessions(
            kb_id=kb_id,
        )

        # 生成 next_token
        next_token = None
        if len(sessions) == max_results + 1:
            last_item = sessions.pop(len(sessions) - 1)
            next_token = NextTokenUtils.encode(
                id=last_item.id, date=last_item.gmt_created
            )

        # 封装输出结果
        data = []
        if len(sessions) > 0:
            data = [
                KnowledgeBaseSessionListResponse.from_orm_model(obj) for obj in sessions
            ]
            # 查询会话的名称列表
            import src.domain.services.session_service

            session_id_list = [obj.session_id for obj in sessions]
            session_infos = (
                src.domain.services.session_service.session_service.get_sessions_by_ids(
                    session_ids=session_id_list
                )
            )

            # 批量查询会话的文件大小
            file_sizes = (
                self.kb_documents_repository.batch_sum_file_size_by_session_ids(
                    kb_id=kb_id, session_ids=session_id_list
                )
            )

            # 填充会话信息
            for session in data:
                # 设置会话的名称
                for session_info in session_infos:
                    if session_info.session_id == session.session_id:
                        session.session_name = session_info.title
                        break

                # 设置文件大小
                session.file_size = file_sizes.get(session.session_id, 0)

        # 返回分页结果
        return KnowledgeBaseSessionPaginationResponse(
            data=data,
            max_results=count,
            next_token=next_token,
        )

    def get_kb_session_message_ids(
            self,
            auth_context: AuthContext,
            kb_id: str,
            session_id: str,
    ) -> List[str]:
        """
        获取指定知识库关联会话的所有消息ID列表

        Args:
            auth_context: 认证上下文
            kb_id: 知识库ID
            session_id: 会话ID

        Returns:
            List[str]: 消息ID列表

        Raises:
            ParameterException: 参数错误
            ObjectNotFoundException: 会话不存在
        """
        logger.info(
            f"获取指定知识库关联会话的所有消息ID列表请求: auth_context: {auth_context}, kb_id: {kb_id}, session_id: {session_id}")

        # 检查参数
        CheckUtils.parameter_not_empty(kb_id, "kb_id")
        CheckUtils.parameter_not_empty(session_id, "session_id")

        # 获取知识库详情（验证权限）
        self._get_knowledge_base_by_id(kb_id, auth_context)

        # 获取会话详情
        session = self.kb_sessions_repository.get_session(
            kb_id=kb_id,
            session_id=session_id,
        )
        # 解析消息ID列表
        if not session or not session.message_id_list:
            return []

        message_id_list = session.message_id_list.split(",")
        # 过滤空字符串
        return [msg_id.strip() for msg_id in message_id_list if msg_id.strip()]

    def list_session_messages(
            self,
            auth_context: AuthContext,
            kb_id: str,
            session_id: str,
            max_results: int,
            next_token: Optional[str] = None,
    ) -> KnowledgeBaseSessionMessagePaginationResponse:
        """
        查询会话消息列表
        """
        logger.info(
            f"查询会话消息列表请求: auth_context: {auth_context}, kb_id: {kb_id}, session_id: {session_id}, max_results: {max_results}, next_token: {next_token}")

        # 检查参数
        CheckUtils.parameter_not_empty(kb_id, "kb_id")
        CheckUtils.parameter_not_empty(session_id, "session_id")
        CheckUtils.parameter_not_null(max_results, "max_results")

        # 获取知识库详情
        self._get_knowledge_base_by_id(kb_id, auth_context)

        # 分页搜索知识库文档关联
        session = self.kb_sessions_repository.get_session(
            kb_id=kb_id,
            session_id=session_id,
        )
        CheckUtils.object_exists(session, "session")
        message_id_list = session.message_id_list.split(",") if session.message_id_list else []

        # 统计知识库会话数量
        count = len(message_id_list)

        # 解析 next_token
        index = int(next_token) if next_token else 0

        # 获取消息详情
        message_id_list = message_id_list[index: index + max_results]
        messages = memory_sdk.get_message_by_id_list(message_id_list)

        # 生成 next_token
        next_token = None
        if count > index + max_results:
            next_token = str(index + max_results)

        # 返回分页结果
        return KnowledgeBaseSessionMessagePaginationResponse(
            data=[
                KnowledgeBaseSessionMessageListResponse(
                    message_id=message.message_id,
                    role=message.role,
                    content=message.content,
                    gmt_created=TimeUtils.to_iso8601_utc(message.timestamp),
                    gmt_modified=TimeUtils.to_iso8601_utc(message.timestamp),
                )
                for message in messages
            ],
            max_results=count,
            next_token=next_token,
        )

    def list_session_files(
            self,
            auth_context: AuthContext,
            kb_id: str,
            session_id: str,
            max_results: int,
            next_token: str,
    ) -> KnowledgeBaseSessionFilePaginationResponse:
        """
        查询会话文件列表
        """
        logger.info(
            f"查询会话文件列表请求: auth_context: {auth_context}, kb_id: {kb_id}, session_id: {session_id}, max_results: {max_results}, next_token: {next_token}")

        # 检查参数
        CheckUtils.parameter_not_empty(kb_id, "kb_id")
        CheckUtils.parameter_not_empty(session_id, "session_id")
        CheckUtils.parameter_not_null(max_results, "max_results")

        # 获取知识库详情
        self._get_knowledge_base_by_id(kb_id, auth_context)

        # 解析 next_token
        pagination_model = NextTokenUtils.decode(next_token)
        less_than_equal_gmt_create = pagination_model.gmt
        less_than_equal_id = pagination_model.id

        # 分页搜索知识库文档关联
        relations = self.kb_document_relations_repository.list_relations(
            kb_id=kb_id,
            session_id=session_id,
            limit=max_results + 1,
            less_than_equal_id=less_than_equal_id,
            less_than_equal_gmt_create=less_than_equal_gmt_create,
            order_pairs=[
                KbDocumentRelationModel.gmt_created.desc(),
                KbDocumentRelationModel.id.desc(),
            ],
        )

        # 统计知识库文档关联数量
        count = self.kb_document_relations_repository.count_relations(
            kb_id=kb_id,
            session_id=session_id,
        )

        # 生成 next_token
        next_token = None
        if len(relations) == max_results + 1:
            last_item = relations.pop(len(relations) - 1)
            next_token = NextTokenUtils.encode(
                id=last_item.id, date=last_item.gmt_created
            )

        # 搜索文档信息
        data = []
        if len(relations) > 0:
            # 搜索文档信息
            file_id_list = [obj.file_id for obj in relations]
            documents = self.kb_documents_repository.list_kb_documents(
                file_id_list=file_id_list,
            )
            data = [
                KnowledgeBaseSessionFileListResponse.from_orm_model(obj)
                for obj in documents
            ]

        # 返回分页结果
        return KnowledgeBaseSessionFilePaginationResponse(
            data=data,
            max_results=count,
            next_token=next_token,
        )

    def list_session_document_relations(
            self,
            auth_context: AuthContext,
            kb_id: str,
            session_id: str,
    ) -> List[str]:
        """
        获取会话下和知识库关联的所有文档file_id列表
        """
        logger.info(
            f"获取会话下和知识库关联的所有文档file_id列表请求: auth_context: {auth_context}, kb_id: {kb_id}, session_id: {session_id}")

        # 检查参数
        CheckUtils.parameter_not_empty(kb_id, "kb_id")
        CheckUtils.parameter_not_empty(session_id, "session_id")

        # 获取知识库详情
        self._get_knowledge_base_by_id(kb_id, auth_context)

        # 获取所有关联
        relations = self.kb_document_relations_repository.list_relations(
            kb_id=kb_id,
            session_id=session_id,
            limit=None,  # 不限制数量
            less_than_equal_id=None,
            less_than_equal_gmt_create=None,
            order_pairs=None,
        )

        # 只返回file_id列表
        file_ids = [relation.file_id for relation in relations]

        return file_ids

    def get_document_preview_url(
            self,
            auth_context: AuthContext,
            file_id: str,
            expires_in: int = 3600,
    ) -> Optional[Tuple[str, str, int]]:
        """
        根据file_id获取文档预览链接

        Args:
            auth_context: 认证上下文
            file_id: 文件ID
            expires_in: 链接过期时间（秒），默认1小时

        Returns:
            Optional[Tuple[str, str, int]]: (文件名, 预览URL, 过期时间)，失败时返回None
        """
        logger.info(
            f"获取文档预览链接请求: auth_context: {auth_context}, file_id: {file_id}, expires_in: {expires_in}")

        # 检查参数
        CheckUtils.parameter_not_empty(file_id, "file_id")

        try:
            # 1. 通过file_id查询文档信息
            document = self.kb_documents_repository.get_kb_document_by_id(file_id)
            if not document:
                logger.warning(f"[KnowledgeService] 文档不存在: file_id={file_id}")
                return None

            # 2. 检查文档状态（只有成功状态的文档才能预览）
            if document.status != "success":
                logger.warning(
                    f"[KnowledgeService] 文档状态不支持预览: file_id={file_id}, status={document.status}"
                )
                return None

            # 3. 检查OSS对象名称
            if not document.oss_object_name:
                logger.warning(
                    f"[KnowledgeService] 文档缺少OSS对象名称: file_id={file_id}"
                )
                return None

            # 4. 生成预览链接
            from src.infrastructure.oss.oss_service import oss_service

            preview_url = oss_service.generate_rag_presigned_url_with_preview(
                key=document.oss_object_name, expires_in=expires_in
            )

            if not preview_url:
                logger.error(f"[KnowledgeService] 生成预览链接失败: file_id={file_id}")
                return None

            logger.info(
                f"[KnowledgeService] 生成文档预览链接成功: file_id={file_id}, expires_in={expires_in}s"
            )
            return document.file_title, preview_url, expires_in

        except Exception as e:
            logger.error(
                f"[KnowledgeService] 获取文档预览链接异常: file_id={file_id}, error={e}"
            )
            return None

    def update_session(
            self,
            auth_context: AuthContext,
            kb_id: str,
            session_id: str,
            message_list: Optional[List[str]] = None,
            file_list: Optional[List[str]] = None,
            file_ignore: bool = False,
            message_ignore: bool = False,
    ):
        """
        更新会话
        """
        logger.info(
            f"更新会话请求: auth_context: {auth_context}, kb_id: {kb_id}, session_id: {session_id}, message_list: {message_list}, file_list: {file_list}, file_ignore: {file_ignore}, message_ignore: {message_ignore}")

        # 检查参数
        CheckUtils.parameter_not_empty(kb_id, "kb_id")
        CheckUtils.parameter_not_empty(session_id, "session_id")

        # 获取知识库详情
        self._get_knowledge_base_by_id(kb_id, auth_context, is_check_exists=True)

        # 更新知识库时间
        self.knowledgebase_repository.update_knowledge_base(
            kb_id=kb_id,
            is_update_selective=True,
        )

        # 查询会话详情，如果会话不存在，则创建会话
        session = self.kb_sessions_repository.get_session(
            kb_id=kb_id,
            session_id=session_id,
        )

        if session is None:
            # 调用创建 session 的逻辑
            self.create_sessions(
                auth_context=auth_context,
                kb_id=kb_id,
                session_id=session_id,
                message_list=message_list if not message_ignore else None,
                file_list=file_list if not file_ignore else None,
            )
        else:
            # 调用更新 message的逻辑
            # 判断message_id_list是否发生变化
            if not message_ignore:
                self._process_update_message_list(
                    auth_context, kb_id, session, message_list
                )

            # 判断file_list是否有变化
            if not file_ignore:
                self._process_update_file_list(auth_context, kb_id, session, file_list)

    def _process_update_message_list(
            self,
            auth_context: AuthContext,
            kb_id: str,
            session: KbSessionModel,
            message_list: List[str],
    ):
        """
        处理消息列表
        """
        logger.info(f"开始处理 知识库{kb_id} 会话{session.session_id} 消息列表: {message_list}")
        current_message_list = (
            session.message_id_list.split(",") if session.message_id_list else []
        )
        if current_message_list != (message_list or []):
            # 重新创建 snippet
            snippet_id = self._process_rag_snippet(auth_context, kb_id, message_list)

            # 解除snippet和知识库的关联
            self._process_delete_rag_snippet(session.snippet_id)

            # 持久化保存
            session.snippet_id = snippet_id
            session.message_id_list = ",".join(message_list) if message_list else None
            session.snippet_status = KbState.PROCESSING.value
            session.session_status = KbState.PROCESSING.value
            self.kb_sessions_repository.update_session_by_id(session)

    def _process_update_file_list(
            self,
            auth_context: AuthContext,
            kb_id: str,
            session: KbSessionModel,
            file_list: List[str],
    ):
        """
        处理文件列表
        """
        logger.info(f"开始处理 知识库{kb_id} 会话{session.session_id} 文件列表: {file_list}")
        relations = self.kb_document_relations_repository.list_relations(
            kb_id=kb_id,
            session_id=session.session_id,
        )
        cur_file_list = [obj.file_id for obj in relations]
        new_file_list = [file_id for file_id in file_list] if file_list else []
        if cur_file_list != new_file_list:
            # 获取待删除的文件
            delete_file_list = set(cur_file_list) - set(new_file_list)
            # 获取待新增的文件
            add_file_list = set(new_file_list) - set(cur_file_list)

            # 处理知识库中待删除的会话文件
            if delete_file_list and len(delete_file_list) > 0:
                # 调用rag服务删除文件关联
                client = create_rag_client()
                delete_documents = self.kb_documents_repository.list_kb_documents(
                    file_id_list=delete_file_list
                )
                client.modify_documents_kb(
                    kb_id=kb_id,
                    doc_ids=[document.doc_id for document in delete_documents],
                    operation="unbind",
                )
                # 批量软删除关联
                self.kb_document_relations_repository.soft_delete_relations(
                    kb_id=kb_id,
                    file_ids=delete_file_list,
                )

            # 处理知识库中待新增的文件
            if add_file_list and len(add_file_list) > 0:
                self._process_add_session_file_list(
                    auth_context=auth_context,
                    kb_id=kb_id,
                    session_id=session.session_id,
                    file_id_list=list(add_file_list),
                )

    def delete_session(
            self,
            auth_context: AuthContext,
            kb_id: str,
            session_id: str,
    ):
        """
        删除会话（单个会话）
        """
        logger.info(
            f"删除会话请求: auth_context: {auth_context}, kb_id: {kb_id}, session_id: {session_id}")
        self.delete_sessions(auth_context, kb_id, [session_id])

    def delete_sessions(
            self,
            auth_context: AuthContext,
            kb_id: str,
            session_ids: List[str],
    ):
        """
        批量删除会话
        """
        logger.info(
            f"批量删除会话请求: auth_context: {auth_context}, kb_id: {kb_id}, session_ids: {session_ids}")

        # 检查参数
        CheckUtils.parameter_not_empty(kb_id, "kb_id")
        CheckUtils.parameter_not_empty(session_ids, "session_ids")

        # 获取知识库详情
        self._get_knowledge_base_by_id(kb_id, auth_context, is_check_exists=True)

        # 更新知识库时间
        self.knowledgebase_repository.update_knowledge_base(
            kb_id=kb_id,
            is_update_selective=True,
        )

        # 获取会话详情列表
        sessions = self.kb_sessions_repository.list_sessions(
            kb_id=kb_id,
            session_id_list=session_ids,
        )
        if not sessions:
            logger.warning(
                f"没有找到要删除的会话: kb_id={kb_id}, session_ids={session_ids}"
            )
            return
        for session in sessions:
            CheckUtils.object_state(
                session.session_status != KbState.PROCESSING.value, "Session"
            )

        # 批量软删除会话
        deleted_count = self.kb_sessions_repository.soft_delete_sessions(
            kb_id=kb_id,
            session_ids=session_ids,
        )
        logger.info(f"批量软删除会话成功: kb_id={kb_id}, 删除数量={deleted_count}")

        # 批量软删除会话关联的文档关系
        self.kb_document_relations_repository.soft_delete_relations(
            kb_id=kb_id,
            session_ids=session_ids,
        )

        # 批量记录操作日志
        logs_data = [
            {
                "kb_id": kb_id,
                "ali_uid": auth_context.ali_uid,
                "wy_id": auth_context.wy_id,
                "operation_type": KbOperationType.DELETE.value,
                "target_type": KbTargetType.SESSION.value,
                "target_id": session_id,
                "status": KbState.SUCCESS.value,
            }
            for session_id in session_ids
        ]
        if logs_data:
            self.kb_operation_logs_repository.batch_insert_logs(logs_data)

        # 批量调用 RAG 服务删除会话信息
        client = create_rag_client()
        snippet_ids = [session.snippet_id for session in sessions if session.snippet_id]
        for snippet_id in snippet_ids:
            try:
                client.delete_snippet(snippet_id=snippet_id)
                logger.info(f"删除 RAG 会话片段成功: snippet_id={snippet_id}")
            except Exception as e:
                logger.exception(
                    f"删除 RAG 会话片段失败: snippet_id={snippet_id}, error={e}"
                )

    # ==================== 知识库日志操作 ====================

    def list_logs(
            self,
            auth_context: AuthContext,
            kb_id: str,
            max_results: int,
            next_token: str,
    ) -> KnowledgeBaseLogListPaginationResponse:
        """
        查询日志列表
        """
        logger.info(
            f"查询日志列表请求: auth_context: {auth_context}, kb_id: {kb_id}, max_results: {max_results}, next_token: {next_token}")

        # 解析 next_token
        pagination_model = NextTokenUtils.decode(next_token)
        less_than_equal_gmt_create = pagination_model.gmt
        less_than_equal_id = pagination_model.id

        # 获取知识库详情
        if kb_id:
            self._get_knowledge_base_by_id(kb_id, auth_context, is_check_exists=True)

        # 查询日志列表
        logs = self.kb_operation_logs_repository.list_logs(
            ali_uid=auth_context.ali_uid,
            wy_id=auth_context.wy_id,
            kb_id=kb_id,
            limit=max_results + 1,
            less_than_equal_id=less_than_equal_id,
            less_than_equal_gmt_create=less_than_equal_gmt_create,
            order_pairs=[
                KbOperationLogModel.gmt_created.desc(),
                KbOperationLogModel.id.desc(),
            ],
        )

        # 统计日志数量
        count = self.kb_operation_logs_repository.count_logs(
            ali_uid=auth_context.ali_uid,
            wy_id=auth_context.wy_id,
            kb_id=kb_id,
        )

        # 生成 next_token
        next_token = None
        if len(logs) == max_results + 1:
            last_item = logs.pop(len(logs) - 1)
            next_token = NextTokenUtils.encode(
                id=last_item.id, date=last_item.gmt_created
            )

        # 查询相关补充信息
        data = []
        if len(logs) > 0:
            data = [KnowledgeBaseLogListResponse.from_orm_model(obj) for obj in logs]
            self._fill_log_data(auth_context, data)

        return KnowledgeBaseLogListPaginationResponse(
            data=data,
            max_results=count,
            next_token=next_token,
        )

    def _fill_log_data(
            self, auth_context: AuthContext, data: List[KnowledgeBaseLogListResponse]
    ):
        """
        填充日志数据
        """
        logger.info(f"开始填充日志数据: auth_context: {auth_context}, data: {data}")

        # 查询知识库信息
        kb_ids = [obj.kb_id for obj in data]
        kbs = self.knowledgebase_repository.list_knowledge_bases(
            owner_ali_uid=auth_context.ali_uid,
            owner_wy_id=auth_context.wy_id,
            kb_ids=kb_ids,
            is_all=True,
        )
        kb_dict = {kb.kb_id: kb for kb in kbs}
        for obj in data:
            if obj.kb_id and obj.kb_id in kb_dict:
                obj.kb_name = kb_dict[obj.kb_id].name
            else:
                if obj.kb_id:
                    logger.warning(
                        f"[KnowledgeService] 日志中的知识库ID不存在: {obj.kb_id}, 用户: {auth_context.ali_uid}"
                    )
                obj.kb_name = None

        # 查询会话信息
        session_id_list = [
            obj.target_id
            for obj in data
            if obj.target_type == KbTargetType.SESSION.value
        ]
        import src.domain.services.session_service

        session_infos = (
            src.domain.services.session_service.session_service.get_sessions_by_ids(
                session_ids=session_id_list
            )
        )
        session_dict = {session.session_id: session for session in session_infos}
        for obj in data:
            if obj.target_type == KbTargetType.SESSION.value:
                obj.title = session_dict[obj.target_id].title

        # 查询文档信息
        file_id_list = [
            obj.target_id
            for obj in data
            if obj.target_type == KbTargetType.DOCUMENT.value
        ]
        documents = self.kb_documents_repository.list_kb_documents(
            file_id_list=file_id_list,
        )
        document_dict = {document.file_id: document for document in documents}
        for obj in data:
            if obj.target_type == KbTargetType.DOCUMENT.value:
                obj.title = document_dict[obj.target_id].file_title

        return data

    # ==================== 知识库知识更新操作 ====================

    def update_knowledge_base_status(
            self,
    ):
        """
        更新知识库资源状态
        """
        logger.info("[KnowledgeService] 更新知识库资源状态")

        # 同步文档状态
        self._update_documents_status()

        # 同步会话解析状态
        self._update_snippet_status()

        # 同步会话状态
        self._update_session_status()

        # 同步日志状态
        self._update_log_status()

    def _update_documents_status(self) -> None:
        """
        更新知识库文档状态，如果状态为SUCCESS或者FAILED，则更新kb_documents表
        """
        logger.info("[KnowledgeService] 更新知识库文档状态")
        client = create_rag_client()
        documents = self.kb_documents_repository.list_kb_documents(
            status=KbState.PROCESSING.value,
        )
        document_dict = {document.doc_id: document for document in documents}
        document_ids = [obj.doc_id for obj in documents]

        # 查询RAG服务每个文档的状态，如果状态为FINISHED，则更新kb_documents表中status为FINISHED
        if document_ids:
            response = client.check_docs_status(
                doc_ids=document_ids,
            )
            if response and response.body and response.body.data:
                for rag_obj in response.body.data:
                    if (
                            rag_obj.doc_id in document_dict
                            and rag_obj.infos
                            and rag_obj.infos.final_state
                            and (
                            rag_obj.infos.final_state == KbState.SUCCESS.value
                            or rag_obj.infos.final_state == KbState.FAILED.value
                    )
                    ):
                        logger.info(
                            f"[KnowledgeService] 更新文档状态: doc_id={rag_obj.doc_id}, status={rag_obj.infos.final_state}"
                        )
                        self.kb_documents_repository.update_kb_document(
                            doc_id=rag_obj.doc_id,
                            status=rag_obj.infos.final_state,
                            oss_bucket=rag_obj.infos.bucket,
                            oss_object_name=rag_obj.file_paths.raw,
                            file_size=rag_obj.infos.filesize,
                        )

    def _update_snippet_status(self) -> None:
        """
        更新知识库会话解析状态
        """
        logger.info("[KnowledgeService] 更新知识库会话解析状态")
        # 查询 kb_sessions 表中 snippet_status 为 PROCESSING 的会话
        sessions = self.kb_sessions_repository.list_sessions(
            snippet_status=KbState.PROCESSING.value,
        )
        session_dict = {session.snippet_id: session for session in sessions}

        # 查询RAG服务每个snippet的状态，如果状态为FINISHED，则更新kb_sessions表中session_status为FINISHED
        client = create_rag_client()
        snippet_ids = [session.snippet_id for session in sessions if session.snippet_id]
        if snippet_ids:
            response = client.check_snippet_status(snippet_ids=snippet_ids)
            if response and response.body and response.body.data:
                for rag_obj in response.body.data:
                    if (
                            rag_obj.snippet_id in session_dict
                            and rag_obj.infos
                            and rag_obj.infos.final_state
                            and (
                            rag_obj.infos.final_state == KbState.SUCCESS.value
                            or rag_obj.infos.final_state == KbState.FAILED.value
                    )
                    ):
                        session = session_dict[rag_obj.snippet_id]
                        session.snippet_status = rag_obj.infos.final_state
                        logger.info(
                            f"[KnowledgeService] 更新会话状态: session_id={session.session_id}, snippet_status={session.snippet_status}"
                        )
                        self.kb_sessions_repository.update_session_by_id(session)

    def _update_session_status(self) -> None:
        """
        更新知识库会话状态
        """
        logger.info("[KnowledgeService] 更新知识库会话状态")
        # 查询 kb_sessions 表中 session_status 为 PROCESSING, snippet_status 为 FINISHED 的会话
        logger.info("[KnowledgeService] 更新知识库会话总和状态")
        sessions = self.kb_sessions_repository.list_sessions(
            session_status=KbState.PROCESSING.value,
            not_snippet_status=KbState.PROCESSING.value,
        )
        for session in sessions:
            relations = self.kb_document_relations_repository.list_relations(
                session_id=session.session_id,
                kb_id=session.kb_id,
            )
            if len(relations) == 0:
                session.session_status = KbState.SUCCESS.value
                logger.info(
                    f"[KnowledgeService] 会话没有关联文档，更新会话状态: session_id={session.session_id}, session_status={session.session_status}"
                )
                self.kb_sessions_repository.update_session_by_id(session)
            else:
                file_ids = [obj.file_id for obj in relations]
                documents = self.kb_documents_repository.list_kb_documents(
                    file_id_list=file_ids,
                )
                # 如果文档有未处理完成，则跳过
                if any(
                        document.status == KbState.PROCESSING.value
                        for document in documents
                ):
                    logger.info(
                        f"[KnowledgeService] 文档未处理完成，跳过: session_id={session.session_id}"
                    )
                    continue

                # 如果会话解析失败或者文档解析失败，则更新会话状态为FAILED
                # 如果会话解析成功，且所有文档都处理完成，则更新会话状态为SUCCESS
                if session.snippet_status == KbState.FAILED.value or any(
                        document.status == KbState.FAILED.value for document in documents
                ):
                    session.session_status = KbState.FAILED.value
                else:
                    session.session_status = KbState.SUCCESS.value

                logger.info(
                    f"[KnowledgeService] 更新会话状态: session_id={session.session_id}, session_status={session.session_status}"
                )
                self.kb_sessions_repository.update_session_by_id(session)

    def _update_log_status(self) -> None:
        """
        更新知识库日志状态
        """
        logger.info("[KnowledgeService] 更新知识库日志状态")

        # 查询会话日志，查询会话日志状态
        logs = self.kb_operation_logs_repository.list_logs(
            status=KbState.PROCESSING.value,
            target_type=KbTargetType.SESSION.value,
            operation_type=KbOperationType.CREATE.value,
        )
        session_id_list = [log.target_id for log in logs]
        sessions = self.kb_sessions_repository.list_sessions(
            session_id_list=session_id_list,
            not_deleted=False,
        )
        session_dict = {session.session_id: session for session in sessions}
        for log in logs:
            if log.target_id not in session_dict:
                continue
            session = session_dict[log.target_id]
            if (
                    session.session_status == KbState.SUCCESS.value
                    or session.session_status == KbState.FAILED.value
            ):
                self.kb_operation_logs_repository.update_log_by_id(
                    id=log.id, status=session.session_status
                )

        # 查询文档日志，查询文档日志状态
        logs = self.kb_operation_logs_repository.list_logs(
            status=KbState.PROCESSING.value,
            target_type=KbTargetType.DOCUMENT.value,
            operation_type=KbOperationType.CREATE.value,
        )
        document_ids = [log.target_id for log in logs]
        documents = self.kb_documents_repository.list_kb_documents(
            file_id_list=document_ids,
        )
        document_dict = {document.file_id: document for document in documents}
        for log in logs:
            if log.target_id not in document_dict:
                continue
            document = document_dict[log.target_id]
            if (
                    document.status == KbState.SUCCESS.value
                    or document.status == KbState.FAILED.value
            ):
                self.kb_operation_logs_repository.update_log_by_id(
                    id=log.id, status=document.status
                )


# 创建全局实例
knowledgebase_service = KnowledgeService()
