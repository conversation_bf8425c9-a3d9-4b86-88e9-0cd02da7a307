"""
SSE流管理器
支持 session_id + request_id 复合维度的连接管理
"""

import asyncio
import json
from datetime import datetime
from typing import Dict, Any, AsyncGenerator, Optional, List, Tuple, Set
from loguru import logger

from ..models import Session, Message


class ConnectionKey:
    """连接标识符 - 复合键 (session_id + request_id)"""
    
    def __init__(self, session_id: str, request_id: str):
        self.session_id = session_id
        self.request_id = request_id
        self._key = f"{session_id}#{request_id}"
    
    def __str__(self) -> str:
        return self._key
    
    def __repr__(self) -> str:
        return f"ConnectionKey(session_id='{self.session_id}', request_id='{self.request_id}')"
    
    def __eq__(self, other) -> bool:
        if isinstance(other, ConnectionKey):
            return self._key == other._key
        return False
    
    def __hash__(self) -> int:
        return hash(self._key)


class SSEManager:
    """SSE流管理器，支持复合维度连接管理"""
    
    def __init__(self):
        # 连接管理 - 使用复合键
        self.sse_connections: Dict[ConnectionKey, asyncio.Queue] = {}  # connection_key -> Queue
        self.heartbeat_tasks: Dict[ConnectionKey, asyncio.Task] = {}  # connection_key -> Task
        self.connection_start_times: Dict[ConnectionKey, float] = {}  # connection_key -> start_time
        
        # 会话级别的连接索引 - 用于快速查找某个会话的所有连接
        self.session_connections: Dict[str, Set[ConnectionKey]] = {}  # session_id -> Set[ConnectionKey]
        
        # 其他配置
        self.session_loader = None  # 稍后由SessionManager注入
        self.pending_messages: Dict[ConnectionKey, List[Dict[str, Any]]] = {}  # 待推送消息缓存
        self._session_cache: Dict[str, Tuple[Session, float]] = {}  # session_id -> (session, timestamp)
        self._cache_ttl = 300  # 缓存5分钟
        
    def set_session_loader(self, session_loader):
        """设置Session加载器引用"""
        self.session_loader = session_loader
    
    def _create_connection_key(self, session_id: str, request_id: str) -> ConnectionKey:
        """创建连接标识符"""
        return ConnectionKey(session_id, request_id)
    
    def _add_session_connection(self, connection_key: ConnectionKey):
        """添加会话连接索引"""
        session_id = connection_key.session_id
        if session_id not in self.session_connections:
            self.session_connections[session_id] = set()
        self.session_connections[session_id].add(connection_key)
    
    def _remove_session_connection(self, connection_key: ConnectionKey):
        """移除会话连接索引"""
        session_id = connection_key.session_id
        if session_id in self.session_connections:
            self.session_connections[session_id].discard(connection_key)
            # 如果该会话没有连接了，清理索引
            if not self.session_connections[session_id]:
                del self.session_connections[session_id]
    
    def get_session_connections(self, session_id: str) -> Set[ConnectionKey]:
        """获取某个会话的所有连接"""
        return self.session_connections.get(session_id, set()).copy()
    
    def has_session_connections(self, session_id: str) -> bool:
        """检查某个会话是否有活跃连接"""
        return session_id in self.session_connections and len(self.session_connections[session_id]) > 0
    
    async def create_sse_stream(
        self, 
        session_id: str, 
        request_id: str,
        last_message_id: Optional[str] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """创建SSE流生成器 - 支持复合维度连接"""
        import time
        
        connection_key = self._create_connection_key(session_id, request_id)
        
        # 记录连接开始时间
        self.connection_start_times[connection_key] = time.time()
        
        logger.info(f"[SSEManager] 开始创建SSE流: {connection_key}")
        if last_message_id:
            logger.info(f"[SSEManager] 断线重连: last_message_id={last_message_id}")
        
        # 加载Session
        session = await self._load_session_for_sse(session_id)
        if not session:
            raise ValueError(f"Session不存在: {session_id}")
        
        # 创建消息队列
        message_queue = self._create_message_queue_by_key(connection_key)
        
        heartbeat_task = None
        close_reason = "unknown"
        
        try:
            # 开始心跳
            heartbeat_task = asyncio.create_task(self._heartbeat_loop(message_queue, connection_key))
            self.heartbeat_tasks[connection_key] = heartbeat_task
            
            logger.info(f"[SSEManager] SSE流创建完成，开始消息事件循环: {connection_key}")
            
            # 监听消息
            try:
                async for event in self._message_event_loop(message_queue, connection_key):
                    yield event
                close_reason = "normal_completion"
                
            except asyncio.CancelledError:
                close_reason = "cancelled"
                logger.info(f"[SSEManager] SSE流被取消: {connection_key}")
                raise
                
            except Exception as e:
                close_reason = f"message_loop_error: {type(e).__name__}"
                logger.error(f"[SSEManager] 消息事件循环异常: {connection_key}, error={e}")
                raise
                
        except asyncio.CancelledError:
            close_reason = "cancelled"
            logger.info(f"[SSEManager] SSE流被取消: {connection_key}")
            
        except Exception as e:
            close_reason = f"stream_error: {type(e).__name__}"
            logger.error(f"[SSEManager] SSE流异常: {connection_key}, error={e}")
            
        finally:
            # 取消心跳任务
            if heartbeat_task and not heartbeat_task.done():
                heartbeat_task.cancel()
                try:
                    await heartbeat_task
                except asyncio.CancelledError:
                    pass
                except Exception as e:
                    logger.error(f"[SSEManager] 等待心跳任务结束时出错: {e}")
            
            # 清理连接
            self._cleanup_connection(connection_key)
            
            # 记录连接关闭信息
            if connection_key in self.connection_start_times:
                duration = time.time() - self.connection_start_times[connection_key]
                logger.info(f"[SSEManager] SSE流和HTTP连接已完全关闭: {connection_key}, "
                           f"持续时间={duration:.2f}秒, 关闭原因={close_reason}")
                del self.connection_start_times[connection_key]
            else:
                logger.info(f"[SSEManager] SSE流和HTTP连接已完全关闭: {connection_key}, 关闭原因={close_reason}")
            
            # 显示剩余连接统计
            session_conn_count = len(self.get_session_connections(session_id))
            total_conn_count = len(self.sse_connections)
            logger.info(f"[SSEManager] 清理后统计: 会话{session_id}剩余连接数={session_conn_count}, "
                       f"总连接数={total_conn_count}")
    
    def create_message_queue(self, connection_key: ConnectionKey) -> asyncio.Queue:
        """创建连接级别的消息队列"""
        # 如果队列已存在，直接返回
        if connection_key in self.sse_connections:
            return self.sse_connections[connection_key]
        
        message_queue = asyncio.Queue()
        self.sse_connections[connection_key] = message_queue
        
        # 添加到会话连接索引
        self._add_session_connection(connection_key)
        
        logger.info(f"[SSEManager] 创建消息队列: {connection_key}")
        return message_queue
    
    async def _message_event_loop(self, message_queue: asyncio.Queue, connection_key: ConnectionKey) -> AsyncGenerator[str, None]:
        """消息事件循环"""
        while True:
            try:
                message = await asyncio.wait_for(message_queue.get(), timeout=120.0)
                
                # 检查是否是关闭信号
                if isinstance(message, dict) and (message.get("type") == "close" or message.get("type") == "done"):
                    logger.info(f"[SSEManager] 收到关闭信号，退出消息循环: {connection_key}")
                    break
                
                # 正常消息处理
                if isinstance(message, dict):
                    message = json.dumps(message)
                
                logger.debug(f"[SSEManager] 发送消息: {connection_key}")
                yield f"data: {message}\n\n"
                
            except asyncio.TimeoutError:
                logger.warning(f"[SSEManager] SSE心跳超时，可能是客户端断开连接: {connection_key}")
                break
            except Exception as e:
                logger.error(f"[SSEManager] SSE异常，可能是客户端断开连接: {connection_key}, error={e}")
                break
    
    async def _heartbeat_loop(self, message_queue: asyncio.Queue, connection_key: ConnectionKey):
        """心跳循环"""
        try:
            while True:
                await asyncio.sleep(3)  # 3秒心跳
                heartbeat_data = json.dumps({"type": "heartbeat", "connection": str(connection_key)})
                await message_queue.put(heartbeat_data)
        except asyncio.CancelledError:
            logger.debug(f"[SSEManager] 心跳任务被取消: {connection_key}")
        except Exception as e:
            logger.error(f"[SSEManager] 心跳异常: {connection_key}, error={e}")
    
    async def _load_session_for_sse(self, session_id: str) -> Optional[Session]:
        """为SSE加载Session - 异步优化版本"""
        try:
            logger.debug(f"[SSEManager] 异步从数据库加载Session: {session_id}")
            
            # 检查缓存
            if session_id in self._session_cache:
                cached_session, timestamp = self._session_cache[session_id]
                if datetime.now().timestamp() - timestamp < self._cache_ttl:
                    logger.debug(f"[SSEManager] 从缓存加载Session: {session_id}")
                    return cached_session
                else:
                    logger.debug(f"[SSEManager] 缓存过期，重新加载Session: {session_id}")
                    del self._session_cache[session_id]
            
            # 使用session_loader异步加载Session
            if self.session_loader:
                session = await asyncio.to_thread(
                    self.session_loader.load_session_from_db,
                    session_id
                )
                if session:
                    return session
            
            # 如果没有session_loader，直接从数据库异步加载
            from ...infrastructure.database.repositories.session_repository import session_db_service
            session_model = await asyncio.to_thread(
                session_db_service.get_session_by_id,
                session_id
            )
            if not session_model:
                return None
            
            # 从数据库模型重建Session对象
            session = Session(
                session_id=session_model.session_id,
                ali_uid=session_model.ali_uid,
                agent_id=session_model.agent_id,
                title=session_model.title,
                status=session_model.status,
                gmt_create=session_model.gmt_create,
                gmt_modified=session_model.gmt_modified,
                metadata=session_model.meta_data or {}
            )
            
            # 加入缓存
            self._session_cache[session_id] = (session, datetime.now().timestamp())
            logger.debug(f"[SSEManager] Session已加入缓存: {session_id}")
            
            return session
            
        except Exception as e:
            logger.error(f"[SSEManager] 异步加载Session失败: {e}")
            return None

    def _cleanup_connection(self, connection_key: ConnectionKey):
        """清理单个连接"""
        # 清理消息队列
        if connection_key in self.sse_connections:
            del self.sse_connections[connection_key]

        # 清理心跳任务
        if connection_key in self.heartbeat_tasks:
            heartbeat_task = self.heartbeat_tasks[connection_key]
            if heartbeat_task and not heartbeat_task.done():
                heartbeat_task.cancel()
            del self.heartbeat_tasks[connection_key]

        # 清理待推送消息
        if connection_key in self.pending_messages:
            del self.pending_messages[connection_key]

        # 从会话连接索引中移除
        self._remove_session_connection(connection_key)

        logger.debug(f"[SSEManager] 连接清理完成: {connection_key}")

    def close_connection(self, session_id: str, request_id: str):
        """关闭特定的连接"""
        connection_key = self._create_connection_key(session_id, request_id)
        self._close_connection_by_key(connection_key)

    def _close_connection_by_key(self, connection_key: ConnectionKey):
        """通过连接键关闭连接"""
        try:
            logger.info(f"[SSEManager] 开始关闭连接: {connection_key}")

            # 取消心跳任务
            if connection_key in self.heartbeat_tasks:
                heartbeat_task = self.heartbeat_tasks[connection_key]
                if heartbeat_task and not heartbeat_task.done():
                    heartbeat_task.cancel()
                    logger.info(f"[SSEManager] 已取消心跳任务: {connection_key}")
                del self.heartbeat_tasks[connection_key]

            # 向连接发送关闭信号
            if connection_key in self.sse_connections:
                message_queue = self.sse_connections[connection_key]
                try:
                    # 发送关闭信号
                    try:
                        loop = asyncio.get_running_loop()
                        asyncio.create_task(message_queue.put({
                            "type": "close",
                            "data": {"message": "Connection closed", "connection": str(connection_key)}
                        }))
                    except RuntimeError:
                        # 没有运行的事件循环，使用同步方式
                        message_queue.put_nowait({
                            "type": "close",
                            "data": {"message": "Connection closed", "connection": str(connection_key)}
                        })
                    logger.info(f"[SSEManager] 已向连接发送关闭信号: {connection_key}")
                except Exception as e:
                    logger.error(f"[SSEManager] 发送关闭信号失败: {connection_key}, error={e}")

            # 立即清理连接（在测试环境中或需要立即清理时）
            self._cleanup_connection(connection_key)

        except Exception as e:
            logger.error(f"[SSEManager] 关闭连接失败: {connection_key}, error={e}")

    def close_session_connection(self, session_id: str):
        """关闭会话的所有连接 - 兼容旧接口"""
        self.close_all_session_connections(session_id)

    def close_all_session_connections(self, session_id: str):
        """关闭某个会话的所有连接"""
        try:
            logger.info(f"[SSEManager] 开始关闭会话的所有连接: session_id={session_id}")

            # 获取该会话的所有连接
            connection_keys = self.get_session_connections(session_id)

            if not connection_keys:
                logger.info(f"[SSEManager] 会话没有活跃连接: session_id={session_id}")
                return

            logger.info(f"[SSEManager] 找到 {len(connection_keys)} 个连接需要关闭: session_id={session_id}")

            # 逐个关闭连接
            for connection_key in connection_keys:
                self._close_connection_by_key(connection_key)

            logger.info(f"[SSEManager] 会话所有连接关闭完成: session_id={session_id}")

        except Exception as e:
            logger.error(f"[SSEManager] 关闭会话连接失败: session_id={session_id}, error={e}")

    async def push_to_sse(self, session_id: str, round_id: str, data: Dict[str, Any], event_type: str = "message"):
        """推送消息到会话的所有连接"""
        try:
            logger.info(f"[SSEManager] 尝试推送消息到会话: session_id={session_id}, event_type={event_type}")

            # 获取该会话的所有连接
            connection_keys = self.get_session_connections(session_id)

            if not connection_keys:
                logger.warning(f"[SSEManager] 会话没有活跃连接: session_id={session_id}")
                return

            logger.info(f"[SSEManager] 找到 {len(connection_keys)} 个连接，开始推送: session_id={session_id}")

            message_data = json.dumps(data)

            # 推送到所有连接
            success_count = 0
            for connection_key in connection_keys:
                try:
                    if connection_key in self.sse_connections:
                        message_queue = self.sse_connections[connection_key]
                        await message_queue.put(message_data)
                        success_count += 1
                        logger.debug(f"[SSEManager] 推送成功: {connection_key}")
                    else:
                        logger.warning(f"[SSEManager] 连接不存在: {connection_key}")
                except Exception as e:
                    logger.error(f"[SSEManager] 推送到连接失败: {connection_key}, error={e}")

            logger.info(f"[SSEManager] 消息推送完成: session_id={session_id}, "
                       f"成功={success_count}/{len(connection_keys)}")

        except Exception as e:
            logger.error(f"[SSEManager] 推送SSE失败: session_id={session_id}, error={e}")

    async def push_error_and_close(self, session_id: str, error_message: str, error_code: str = "UNKNOWN_ERROR"):
        """推送错误事件并关闭会话的所有连接"""
        try:
            import uuid
            from datetime import datetime

            logger.info(f"[SSEManager] 推送错误事件并关闭会话连接: session_id={session_id}, error_code={error_code}")

            # 构造错误事件
            error_event = {
                "type": "RUN_ERROR",
                "event_id": f"error_{uuid.uuid4().hex[:12]}",
                "session_id": session_id,
                "timestamp": datetime.now().isoformat(),
                "content": error_message,
                "error": {
                    "code": error_code,
                    "message": error_message
                }
            }

            # 推送错误事件到所有连接
            await self.push_to_sse(
                session_id=session_id,
                round_id="",
                data=error_event,
                event_type="error"
            )

            # 等待一小段时间确保错误事件发送完成
            await asyncio.sleep(0.5)

            # 关闭所有连接
            self.close_all_session_connections(session_id)
            logger.info(f"[SSEManager] 错误事件推送完成，所有连接已关闭: session_id={session_id}")

        except Exception as e:
            logger.error(f"[SSEManager] 推送错误事件并关闭连接失败: session_id={session_id}, error={e}")
            # 即使推送失败，也要尝试关闭连接
            try:
                self.close_all_session_connections(session_id)
                logger.info(f"[SSEManager] 强制关闭所有连接: session_id={session_id}")
            except Exception as close_error:
                logger.error(f"[SSEManager] 强制关闭连接失败: session_id={session_id}, error={close_error}")

    def cleanup_expired_connections(self):
        """清理过期的SSE连接"""
        try:
            current_connections = list(self.sse_connections.keys())
            current_heartbeat_connections = list(self.heartbeat_tasks.keys())

            if current_connections:
                logger.info(f"[SSEManager] 当前活跃SSE连接: {len(current_connections)}个")
                # 按会话分组显示连接统计
                session_stats = {}
                for conn_key in current_connections:
                    session_id = conn_key.session_id
                    session_stats[session_id] = session_stats.get(session_id, 0) + 1

                for session_id, count in session_stats.items():
                    logger.info(f"[SSEManager] 会话 {session_id}: {count} 个连接")

            if current_heartbeat_connections:
                logger.info(f"[SSEManager] 当前活跃心跳任务: {len(current_heartbeat_connections)}个")

            # 检查是否有孤立的心跳任务（有心跳任务但没有连接）
            orphaned_heartbeat_connections = set(current_heartbeat_connections) - set(current_connections)
            for connection_key in orphaned_heartbeat_connections:
                logger.warning(f"[SSEManager] 发现孤立的心跳任务，正在清理: {connection_key}")
                if connection_key in self.heartbeat_tasks:
                    task = self.heartbeat_tasks[connection_key]
                    if task and not task.done():
                        task.cancel()
                    del self.heartbeat_tasks[connection_key]

        except Exception as e:
            logger.error(f"[SSEManager] 清理SSE连接失败: {e}")

    def create_message_queue(self, session_id: str, request_id: str) -> asyncio.Queue:
        """创建消息队列"""
        connection_key = self._create_connection_key(session_id, request_id)
        return self._create_message_queue_by_key(connection_key)

    def _create_message_queue_by_key(self, connection_key: ConnectionKey) -> asyncio.Queue:
        """通过连接键创建消息队列"""
        # 如果队列已存在，直接返回
        if connection_key in self.sse_connections:
            return self.sse_connections[connection_key]

        message_queue = asyncio.Queue()
        self.sse_connections[connection_key] = message_queue

        # 添加到会话连接索引
        self._add_session_connection(connection_key)

        logger.info(f"[SSEManager] 创建消息队列: {connection_key}")
        return message_queue
