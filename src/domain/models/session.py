"""
会话模型
Session类的定义和相关方法
"""

import uuid
from dataclasses import dataclass, field
from datetime import datetime
from typing import List, Optional, Dict, Any, Callable
from loguru import logger

from .enums import SessionStatus
from .message import Role
from .message import Message

@dataclass
class Session:
    """会话充血模型"""
    session_id: str
    ali_uid: str
    agent_id: str
    wy_id: str
    title: str = ""
    status: SessionStatus = SessionStatus.CREATE
    gmt_create: datetime = field(default_factory=datetime.now)
    gmt_modified: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # 事件监听器
    _listeners: Dict[str, List[Callable]] = field(default_factory=dict, init=False)
    
    def __post_init__(self):
        """初始化后处理"""
        # 设置默认元数据
        self.metadata.setdefault('created_by', 'system')
        self.metadata.setdefault('max_rounds', 100)
        self.metadata.setdefault('timeout', 3600)
    
    @classmethod
    def create_new(cls, ali_uid: str, agent_id: str, wy_id: str, session_id: Optional[str] = None) -> "Session":
        """工厂方法：创建新会话"""
        if not session_id:
            session_id = f"sess_{uuid.uuid4().hex[:12]}"
        
        session = cls(
            session_id=session_id,
            ali_uid=ali_uid,
            agent_id=agent_id,
            wy_id=wy_id
        )
        
        logger.info(f"[Session] 创建会话: {session_id}")
        return session
    
    # ================ 事件系统 ================
    
    def on(self, event: str, callback: Callable):
        """注册事件监听器"""
        if event not in self._listeners:
            self._listeners[event] = []
        self._listeners[event].append(callback)
    
    def emit(self, event: str, *args, **kwargs):
        """触发事件"""
        for callback in self._listeners.get(event, []):
            try:
                callback(*args, **kwargs)
            except Exception as e:
                logger.error(f"[Session] 事件回调异常: {e}")
    
    # ================ 状态管理 ================
    

    
    def start_processing(self) -> bool:
        """开始处理"""
        # 转换为ACTIVE状态，表示正在处理
        old_status = self.status
        self.status = SessionStatus.ACTIVE
        self.gmt_modified = datetime.now()

        self.emit('status_changed', self, old_status, self.status, "开始处理")
        old_status_name = getattr(old_status, 'name', str(old_status))
        new_status_name = getattr(self.status, 'name', str(self.status))
        logger.info(f"[Session] 开始处理Session: {self.session_id} {old_status_name} -> {new_status_name}")
        return True

    def finish_processing(self) -> bool:
        """完成处理"""
        # 转换为CLOSED状态，表示处理完成，可以接收新消息
        old_status = self.status
        self.status = SessionStatus.CLOSED
        self.gmt_modified = datetime.now()

        self.emit('status_changed', self, old_status, self.status, "完成处理")
        old_status_name = getattr(old_status, 'name', str(old_status))
        new_status_name = getattr(self.status, 'name', str(self.status))
        logger.info(f"[Session] 完成处理: {self.session_id} {old_status_name} -> {new_status_name}")
        return True
    

    

    
    # ================ 业务逻辑 ================
    
    def update_title(self, title: str):
        """更新Session标题"""
        self.title = title
        self.gmt_modified = datetime.now()
        logger.info(f"[Session] 更新标题: {self.session_id} -> {title}")
    
    # ================ 状态查询 ================
    
    @property
    def is_create(self) -> bool:
        """是否刚创建"""
        return self.status == SessionStatus.CREATE
    
    @property
    def is_active(self) -> bool:
        """是否正在处理"""
        return self.status == SessionStatus.ACTIVE
    
    @property
    def is_closed(self) -> bool:
        """是否已关闭（处理完成）"""
        return self.status == SessionStatus.CLOSED
