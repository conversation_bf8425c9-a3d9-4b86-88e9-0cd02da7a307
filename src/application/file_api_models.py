# ==================== 文件服务相关模型 ====================
from domain.utils.time_utils import TimeUtils
from infrastructure.database.models.file_models import FileType
from pydantic import BaseModel, Field, ConfigDict
from datetime import datetime
from typing import Optional, Dict, Any, List
from enum import Enum

class FileUploadResponse(BaseModel):
    """文件上传响应"""
    session_id: str = Field(..., description="会话ID")
    file_id: str = Field(..., description="文件唯一标识符")
    file_name: str = Field(..., description="原始文件名")
    file_size: int = Field(..., description="文件大小（字节）")
    file_type: str = Field(..., description="文件类型")
    upload_status: str = Field(..., description="上传状态：uploading/completed/failed")


class FileStatusResponse(BaseModel):
    """文件状态响应"""
    file_id: str = Field(..., description="文件唯一标识符")
    file_name: str = Field(..., description="文件名")
    file_size: int = Field(..., description="文件大小（字节）")
    uploaded_size: int = Field(..., description="已上传大小（字节）")
    upload_progress: int = Field(..., description="上传进度百分比（0-100）")
    upload_status: str = Field(..., description="上传状态：uploading/completed/failed")
    error_message: Optional[str] = Field(None, description="错误信息（如果上传失败）")
    url: Optional[str] = Field(None, description="文件访问URL（上传完成后）")
    created_time: datetime = Field(..., description="文件创建时间")
    completed_time: Optional[datetime] = Field(None, description="上传完成时间")


class DownloadLink(BaseModel):
    """下载链接信息"""
    artifact_id: Optional[str] = Field(None, description="制品ID")
    file_name: str = Field(..., description="文件名")
    file_size: Optional[int] = Field(None, description="文件大小（字节）")
    content_type: Optional[str] = Field(None, description="文件MIME类型")
    download_url: str = Field(..., description="下载链接")
    expires_in: int = Field(..., description="链接过期时间（秒）")


class FailedFile(BaseModel):
    """失败的文件信息"""
    artifact_id: Optional[str] = Field(None, description="制品ID")
    file_name: str = Field(..., description="文件名")
    error: str = Field(..., description="错误信息")


class DownloadUrlsRequest(BaseModel):
    """批量下载链接请求"""
    session_id: str = Field(...,  description="会话ID")
    artifact_ids: List[str] = Field(..., description="制品ID列表")
    file_format: Optional[str] = Field(None, description="文件类型 for ppt")
    expires: int = Field(3600, description="链接过期时间（秒）")


class DownloadUrlsResponse(BaseModel):
    """批量下载链接响应"""
    download_links: List[DownloadLink] = Field(..., description="成功生成的下载链接")
    failed_files: List[FailedFile] = Field(..., description="失败的文件")


class FileInfoResponse(BaseModel):
    """文件信息"""
    file_id: str = Field(..., description="文件ID")
    artifact_id: Optional[str] = Field(None, description="制品ID，用于标识制品文件")
    file_name: str = Field(..., description="文件名")
    file_size: Optional[int] = Field(None, description="文件大小（字节）")
    content_type: Optional[str] = Field(None, description="文件MIME类型")
    doc_id: Optional[str] = Field(None, description="RAG解析返回的文档ID")
    artifact_type: str = Field(..., description="文件类型")
    upload_status: str = Field(..., description="上传状态: uploading/analyzing/completed/failed")
    download_url: Optional[str] = Field(None, description="下载链接")
    gmt_created: str = Field(..., description="创建时间")
    gmt_modified: str = Field(..., description="修改时间")
    is_in_kb: bool = Field(False, description="是否在知识库中")

    @classmethod
    def from_orm_model(cls, orm_obj, is_in_kb: bool = False):
        return cls(
            file_id=str(getattr(orm_obj, "id", "unknown")),
            artifact_id=getattr(orm_obj, "artifact_id", None),
            file_name=getattr(orm_obj, "title", "unknown"),
            file_size=getattr(orm_obj, "file_size", None),
            content_type=getattr(orm_obj, "content_type", None),
            doc_id=getattr(orm_obj, "doc_id", None),
            artifact_type=getattr(orm_obj, "type", "unknown"),
            upload_status=getattr(orm_obj, "upload_status", "unknown"),
            download_url=getattr(orm_obj, "download_url", None),
            gmt_created=TimeUtils.to_iso8601_utc(orm_obj.gmt_created),
            gmt_modified=TimeUtils.to_iso8601_utc(orm_obj.gmt_modified),
            is_in_kb=is_in_kb,
        )


class SessionFilesRequest(BaseModel):
    """会话文件请求"""
    session_id: str = Field(...,  description="会话ID")
    artifact_types: Optional[List[str]] = Field(None,  description="文件类型")
    max_results: int = Field(100,  description="最大结果数")
    next_token: Optional[str] = Field(None,  description="下一页标记")
    kb_id: Optional[str] = Field(None, description="知识库ID，查询是否在该知识库中")


class SessionFilesResponse(BaseModel):
    """会话文件列表响应"""
    data: List[FileInfoResponse] = Field(..., description="文件列表")
    max_result: int = Field(..., description="文件总数")
    next_token: Optional[str] = Field(None, description="下一页标记")


class RenameFileRequest(BaseModel):
    """重命名文件请求"""
    artifact_id: str = Field(..., description="制品ID")
    new_file_name: str = Field(..., description="新文件名")


class RenameFileResponse(BaseModel):
    """重命名文件响应"""
    artifact_id: str = Field(..., description="制品ID")
    old_file_name: str = Field(..., description="原文件名")
    new_file_name: str = Field(..., description="新文件名")
    gmt_modified: str = Field(..., description="修改时间")


class BatchDeleteFilesRequest(BaseModel):
    """批量删除文件请求"""
    # session_id: str = Field(...,  description="会话ID")
    artifact_ids: List[str] = Field(..., description="要删除的制品ID列表")
    delete_from_oss: bool = Field(True, description="是否同时删除OSS中的文件")


class DeletedFile(BaseModel):
    """成功删除的文件信息"""
    artifact_id: str = Field(..., description="制品ID")
    file_name: str = Field(..., description="文件名")


class FailedDeleteFile(BaseModel):
    """删除失败的文件信息"""
    artifact_id: str = Field(..., description="制品ID")
    file_name: str = Field(..., description="文件名")
    error: str = Field(..., description="删除失败的错误信息")


class BatchDeleteFilesResponse(BaseModel):
    """批量删除文件响应"""
    deleted_files: List[DeletedFile] = Field(..., description="成功删除的文件列表")
    failed_files: List[FailedDeleteFile] = Field(..., description="删除失败的文件列表")
    total_count: int = Field(..., description="总文件数量")
    deleted_count: int = Field(..., description="成功删除的文件数量")
    failed_count: int = Field(..., description="删除失败的文件数量")


class ArtifactPreviewRequest(BaseModel):
    """文件预览请求"""
    artifact_id: str = Field(..., description="制品ID")


class ArtifactPreviewResponse(BaseModel):
    """文件预览响应"""
    artifact_id: str = Field(..., description="制品ID")
    file_name: str = Field(..., description="文件名")
    url: str = Field(..., description="文件链接（预览或下载）")
    url_type: str = Field(..., description="链接类型：preview-预览，download-下载")
    expires_in: int = Field(..., description="链接过期时间（秒）")


class ArtifactExistRequest(BaseModel):
    """检查文件是否存在请求"""
    artifact_id: str = Field(..., description="制品ID")


class ArtifactExistResponse(BaseModel):
    """检查文件是否存在响应"""
    artifact_id: str = Field(..., description="制品ID")
    exists: bool = Field(..., description="文件是否存在（true-存在且未删除，false-不存在或已删除）")


class SyncToStorageRequest(BaseModel):
    """同步文件到存储请求"""
    session_id: str = Field(..., description="会话ID")
    artifact_ids: List[str] = Field(..., description="要同步的制品ID列表", min_length=1, max_length=10)


class SyncFileResult(BaseModel):
    """同步文件结果"""
    artifact_id: str = Field(..., description="制品ID")
    file_name: str = Field(..., description="文件名")
    status: str = Field(..., description="同步状态：success/failed")
    message: Optional[str] = Field(None, description="结果消息")
    file_size: Optional[int] = Field(None, description="文件大小（字节）")


class SyncToStorageResponse(BaseModel):
    """同步文件到存储响应"""
    total_count: int = Field(..., description="总文件数量")
    success_count: int = Field(..., description="成功同步的文件数量")
    failed_count: int = Field(..., description="同步失败的文件数量")
    results: List[SyncFileResult] = Field(..., description="详细结果列表")
    message: str = Field(..., description="总体处理结果消息")


class SyncTaskStatus(BaseModel):
    """同步任务状态"""
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态：processing/completed/failed")
    progress: float = Field(..., description="进度百分比（0-100）")
    total_count: int = Field(..., description="总文件数量")
    completed_count: int = Field(..., description="已处理文件数量")
    success_count: int = Field(..., description="成功处理文件数量")
    failed_count: int = Field(..., description="失败处理文件数量")
    message: str = Field(..., description="状态消息")
    created_time: str = Field(..., description="任务创建时间")
    updated_time: str = Field(..., description="最后更新时间")
    results: Optional[List[SyncFileResult]] = Field(None, description="详细结果列表（仅在完成时返回）")


class SyncTaskCreateResponse(BaseModel):
    """同步任务创建响应"""
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态")
    message: str = Field(..., description="创建结果消息")


class SyncTaskQueryRequest(BaseModel):
    """同步任务查询请求"""
    task_id: str = Field(..., description="任务ID")